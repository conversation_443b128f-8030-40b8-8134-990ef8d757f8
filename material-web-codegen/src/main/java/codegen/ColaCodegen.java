package codegen;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.mybatisflex.gateway.IGateway;
import cn.com.voyah.material.pojo.base.BasePO;
import codegen.generator.*;
import codegen.template.CustomTemplate;
import com.mybatisflex.codegen.Generator;
import com.mybatisflex.codegen.config.GlobalConfig;
import com.mybatisflex.codegen.constant.GenTypeConst;
import com.mybatisflex.codegen.dialect.JdbcTypeMapping;
import com.mybatisflex.codegen.generator.GeneratorFactory;
import com.zaxxer.hikari.HikariDataSource;

import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDateTime;

public class ColaCodegen {

    private final static GeneratorConfig generatorConfig = new GeneratorConfig();

    private final static GlobalConfig globalConfig = new GlobalConfig();

    static {
        // 设置表前缀和只生成哪些表，setGenerateTable 未配置时，生成所有表
        globalConfig.getStrategyConfig()
                // 忽略的前缀
//                .setTablePrefix("lantu_")
                .setGenerateTable("client_info");

        //可以单独配置某个列
//        ColumnConfig columnConfig = new ColumnConfig();
//        columnConfig.setColumnName("pw");
//        columnConfig.setLarge(true);
//        columnConfig.setVersion(true);
//        globalConfig.getStrategyConfig()
//                .setColumnConfig("user", columnConfig);

        // enable 启用生成，isOverride 是否覆盖原文件
        generatorConfig.setEntity(true, true);
        generatorConfig.setEntityDO(true, true);
        generatorConfig.setConvert(true, true);
        generatorConfig.setController(true, true);
        generatorConfig.setMapper(true, true);
        generatorConfig.setGateway(true, true);
        generatorConfig.setGatewayImpl(true, true);
    }

    public static void main(String[] args) {
        //配置数据源
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("********************************************************************************");
        dataSource.setUsername("root");
        dataSource.setPassword("Nacos@2023!");

        JdbcTypeMapping.registerMapping(Timestamp.class, LocalDateTime.class);
        JdbcTypeMapping.registerMapping(Date.class, LocalDateTime.class);
        //创建配置内容，两种风格都可以。
        GlobalConfig globalConfig = createGlobalConfigUseStyle2();

        //通过 datasource 和 globalConfig 创建代码生成器
        Generator generator = new Generator(dataSource, globalConfig);

        //生成代码
        generator.generate();
        dataSource.close();
    }

    public static GlobalConfig createGlobalConfigUseStyle2() {
        //创建配置内容
        String basePackage = "cn.com.voyah.material";

        //设置根包
        globalConfig.getPackageConfig()
                .setBasePackage(basePackage)
                .setMapperPackage(basePackage.concat(".mapper"))
                .setControllerPackage(basePackage.concat(".web"))
                .setEntityPackage(basePackage + ".pojo");

        // TableDef 在 build 或 mvn compile 时会自动生成  具体见：annotationProcessorPaths 配置 mybatis-flex.config
//        globalConfig.enableTableDef();

        //设置生成 entity 并启用 Lombok
        if (generatorConfig.getEntity().isEnable()) {
            globalConfig.enableEntity()
                    .setSourceDir("material-web-entity/src/main/java")
                    .setOverwriteEnable(generatorConfig.getEntity().isOverride())
                    .setClassSuffix("PO")
                    .setSuperClass(BasePO.class)
                    .setWithLombok(true)
                    .setJdkVersion(17);
        }
        //设置生成 entity 并启用 Lombok
        if (generatorConfig.getMapper().isEnable()) {
            globalConfig.enableMapper()
                    .setOverwriteEnable(generatorConfig.getMapper().isOverride())
                    .setSourceDir("material-web-infrastructure/src/main/java");
        }

        // 自定义构建
        customGenerator();
        return globalConfig;
    }

    private static void customGenerator() {
        CustomTemplate template = new CustomTemplate();
        String basePackage = globalConfig.getBasePackage();

        // domain 实体类
        EntityDOGenerator entityDOGenerator = (EntityDOGenerator) new EntityDOGenerator(template)
                .setSourceDir("material-web-domain/src/main/java")
                .setGenerateEnable(generatorConfig.getEntityDO().isEnable())
                .setOverwriteEnable(generatorConfig.getEntityDO().isOverride())
                .setSuperClass(BaseDO.class)
                .setWithLombok(true)
                .setWithSwagger(true)
                .setClassSuffix("DO")
                .setPackageName(basePackage.concat(".domain.entity"));
        GeneratorFactory.registerGenerator("EntityDO", entityDOGenerator);


        // 转换 接口类
        ConvertGenerator convertGenerator = (ConvertGenerator) new ConvertGenerator(template)
                .setSourceDir("material-web-infrastructure/src/main/java")
                .setGenerateEnable(generatorConfig.getConvert().isEnable())
                .setOverwriteEnable(generatorConfig.getConvert().isOverride())
                .setSuperClass(IConvert.class)
                .setPackageName(basePackage.concat(".convertor"));
        GeneratorFactory.registerGenerator("convert", convertGenerator);

        GatewayGenerator gatewayGenerator = new GatewayGenerator(template);
        gatewayGenerator.setGenerateEnable(generatorConfig.getGateway().isEnable())
                .setSourceDir("material-web-domain/src/main/java")
                .setOverwriteEnable(generatorConfig.getGateway().isOverride())
                .setSuperClass(IGateway.class)
                .setPackageName(basePackage + ".domain.gateway");
        GeneratorFactory.registerGenerator("gateway", gatewayGenerator);

        GatewayImplGenerator gatewayImplGenerator = new GatewayImplGenerator(template);
        gatewayImplGenerator.setGenerateEnable(generatorConfig.getGatewayImpl().isEnable())
                .setSourceDir("material-web-infrastructure/src/main/java")
                .setOverwriteEnable(generatorConfig.getGatewayImpl().isOverride())
                .setSuperClass(GatewayImpl.class)
                .setPackageName(basePackage + ".gatewayimpl");
        GeneratorFactory.registerGenerator("gatewayImpl", gatewayImplGenerator);

        // Controller 实体类
        ControllerGenerator controllerGenerator = new ControllerGenerator(template);
        controllerGenerator.setSourceDir("material-web-adapter/src/main/java");
        controllerGenerator.setGenerateEnable(generatorConfig.getController().isEnable());
        controllerGenerator.setOverwriteEnable(generatorConfig.getController().isOverride());
        controllerGenerator.setPackageName(globalConfig.getControllerPackage());
        controllerGenerator.setWithSwagger(true);
        GeneratorFactory.registerGenerator(GenTypeConst.CONTROLLER + "Custom", controllerGenerator);

    }
}