package codegen.generator;

import codegen.generator.base.GeneratorAbstract;
import com.mybatisflex.codegen.config.EntityConfig;
import com.mybatisflex.codegen.config.GlobalConfig;
import com.mybatisflex.codegen.entity.Table;
import com.mybatisflex.codegen.template.ITemplate;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class EntityDOGenerator extends GeneratorAbstract {

    public EntityDOGenerator(ITemplate template) {
        super(template, "/enjoy/entityDOOrBase.tpl");
    }

    @Override
    public void generate(Table table, GlobalConfig globalConfig) {
        this.init(table, globalConfig);
        Map<String, Object> customConfig = globalConfig.getCustomConfig();
        customConfig.put("entityDO", this);
        if (!this.isGenerateEnable()) {
            return;
        }
        File entityJavaFile = getFile();
        if (entityJavaFile.exists() && !this.isOverwriteEnable()) {
            return;
        }

        // Entity 通用模板
        EntityConfig entityConfig = this.getConfig();
        globalConfig.setEntitySuperClass(entityConfig.getSuperClass());
        table.setEntityConfig(entityConfig);

        Map<String, Object> params = new HashMap<>(4);
        params.put("table", table);
        params.put("entityConfig", entityConfig);
        params.put("entityClassName", this.getClassName());
        params.put("entityPackageName", this.getPackageName());
        params.put("packageConfig", globalConfig.getPackageConfig());
        params.put("javadocConfig", globalConfig.getJavadocConfig());
        params.put("isBase", false);
        this.getTemplate().generate(params, getTemplatePath(), entityJavaFile);

        System.out.println("Custom EntityDO ---> " + entityJavaFile);
    }

    private EntityConfig getConfig() {
        EntityConfig config = new EntityConfig();
        config.setSourceDir(this.getSourceDir());
        config.setClassPrefix(this.getClassPrefix());
        config.setClassSuffix(this.getClassSuffix());
        config.setSuperClass(this.getSuperClass());
        config.setWithLombok(this.isWithLombok());
        config.setWithSwagger(this.isWithSwagger());
        config.setSwaggerVersion(this.getSwaggerVersion());
        config.setOverwriteEnable(this.isOverwriteEnable());
        return config;
    }
}
