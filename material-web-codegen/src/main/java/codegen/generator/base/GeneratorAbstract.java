package codegen.generator.base;

import com.mybatisflex.codegen.config.EntityConfig;
import com.mybatisflex.codegen.config.GlobalConfig;
import com.mybatisflex.codegen.config.PackageConfig;
import com.mybatisflex.codegen.entity.Table;
import com.mybatisflex.codegen.generator.IGenerator;
import com.mybatisflex.codegen.template.ITemplate;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.File;

@Data
@Accessors(chain = true)
public abstract class GeneratorAbstract implements IGenerator {

    ITemplate template;


    String templatePath;

    boolean generateEnable;

    /**
     * 类所在包名称
     */
    String packageName;

    /**
     * 类名称
     */
    String className;
    /**
     * 类变量名称
     */
    String classVarName;

    /**
     * 代码生成目录，当未配置时，使用 PackageConfig 的配置
     */
    String sourceDir;

    /**
     * 类的前缀。
     */
    String classPrefix = "";

    /**
     * 类的后缀。
     */
    String classSuffix = "";

    /**
     * 类的父类，可以自定义一些 BaseEntity 类。
     */
    Class<?> superClass;

    /**
     * 是否使用 Swagger 注解。
     */
    boolean withSwagger;

    boolean overwriteEnable;

    /**
     * Swagger 版本
     */
    EntityConfig.SwaggerVersion swaggerVersion = EntityConfig.SwaggerVersion.DOC;

    /**
     * Entity 是否使用 Lombok 注解。
     */
    boolean withLombok;

    public GeneratorAbstract(ITemplate template, String templatePath) {
        this.template = template;
        this.templatePath = templatePath;
    }

    protected void init(Table table, GlobalConfig globalConfig) {
        PackageConfig packageConfig = globalConfig.getPackageConfig();
        if (this.getPackageName() == null) {
            this.setPackageName(packageConfig.getBasePackage());
        }
        String className = this.buildEntityClassName(table);
        this.setClassName(className);

        // 类名称首字母转小写，用作变量名
        char firstChar = className.charAt(0);
        char lowerCaseFirstChar = Character.toLowerCase(firstChar);
        String result = lowerCaseFirstChar + className.substring(1);
        this.setClassVarName(result);
        if (this.getSourceDir() == null) {
            this.setSourceDir(packageConfig.getSourceDir());
        }
    }

    /**
     * 构建 entity 的 Class 名称。
     */
    protected String buildEntityClassName(Table table) {
        return this.getClassPrefix() + table.getEntityJavaFileName() + this.getClassSuffix();
    }

    @Override
    public String getTemplatePath() {
        return templatePath;
    }

    @Override
    public void setTemplatePath(String templatePath) {
        this.templatePath = templatePath;
    }

    protected File getFile() {
        if (className == null) {
            throw new IllegalStateException("类名不能为空!!!");
        }
        String entityPackagePath = this.getPackageName().replace(".", "/");
        return new File(this.getSourceDir(), entityPackagePath + "/" + className + ".java");
    }

    public String buildSuperClassName() {
        if (superClass != null) {
            return superClass.getSimpleName();
        }
        return "";
    }

    public String buildSuperClassImport() {
        if (superClass == null) return "";
        return "import " + superClass.getName() + ";";
    }

    public String buildClassImport() {
        return "import " + packageName + "." + className + ";";
    }
}