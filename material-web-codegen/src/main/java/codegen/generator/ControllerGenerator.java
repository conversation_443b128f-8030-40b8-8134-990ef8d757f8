package codegen.generator;

import codegen.generator.base.GeneratorAbstract;
import com.mybatisflex.codegen.config.GlobalConfig;
import com.mybatisflex.codegen.entity.Table;
import com.mybatisflex.codegen.template.ITemplate;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class ControllerGenerator extends GeneratorAbstract {

    public ControllerGenerator(ITemplate template) {
        super(template, "enjoy/controller.tpl");
        this.setClassSuffix("Controller");
    }


    @Override
    public void generate(Table table, GlobalConfig globalConfig) {
        this.init(table, globalConfig);
        if (!this.isGenerateEnable()) {
            return;
        }

        File entityJavaFile = this.getFile();
        if (entityJavaFile.exists() && !this.isOverwriteEnable()) {
            return;
        }
        // 通用模板
        Map<String, Object> params = new HashMap<>();
        params.put("table", table);
        params.put("packageConfig", globalConfig.getPackageConfig());
        params.put("controllerConfig", globalConfig.getControllerConfig());
        params.put("javadocConfig", globalConfig.getJavadocConfig());
        params.put("withSwagger", this.isWithSwagger());
        params.put("swaggerVersion", this.getSwaggerVersion());
        params.putAll(globalConfig.getCustomConfig());
        this.getTemplate().generate(params, this.getTemplatePath(), entityJavaFile);

        System.out.println("Custom Controller ---> " + entityJavaFile);
    }
}
