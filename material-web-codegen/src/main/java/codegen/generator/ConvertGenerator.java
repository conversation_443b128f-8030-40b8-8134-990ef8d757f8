package codegen.generator;

import codegen.generator.base.GeneratorAbstract;
import com.mybatisflex.codegen.config.GlobalConfig;
import com.mybatisflex.codegen.entity.Table;
import com.mybatisflex.codegen.template.ITemplate;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class ConvertGenerator extends GeneratorAbstract {

    public ConvertGenerator(ITemplate template) {
        super(template, "enjoy/convert.tpl");
        this.setClassSuffix("Convert");
    }


    @Override
    public void generate(Table table, GlobalConfig globalConfig) {
        this.init(table, globalConfig);
        Map<String, Object> customConfig = globalConfig.getCustomConfig();
        customConfig.put("convert", this);
        if (!this.isGenerateEnable()) {
            return;
        }

        File entityJavaFile = this.getFile();
        if (entityJavaFile.exists() && !this.isOverwriteEnable()) {
            return;
        }
        // 通用模板
        Map<String, Object> params = new HashMap<>(4);
        params.put("table", table);
        params.put("packageConfig", globalConfig.getPackageConfig());
        params.put("javadocConfig", globalConfig.getJavadocConfig());
        params.putAll(customConfig);
        this.getTemplate().generate(params, getTemplatePath(), entityJavaFile);

        System.out.println("Custom Convert ---> " + entityJavaFile);
    }
}
