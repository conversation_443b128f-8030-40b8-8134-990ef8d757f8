package codegen;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 构建配置
 */
@Getter
public class GeneratorConfig {

    private State entity;
    private State entityDO;
    private State convert;
    private State mapperXml;
    private State mapper;
    private State gateway;
    private State gatewayImpl;
    private State controller;

    @Getter
    @AllArgsConstructor
    static class State {
        /**
         * 启用生成
         */
        private boolean enable;

        /**
         * 重新生成，覆盖原文件
         */
        private boolean isOverride;
    }

    public void setEntity(boolean enable, boolean isOverride) {
        this.entity = new State(enable, isOverride);
    }
    public void setEntityDO(boolean enable, boolean isOverride) {
        this.entityDO = new State(enable, isOverride);
    }

    public void setConvert(boolean enable, boolean isOverride) {
        this.convert = new State(enable, isOverride);
    }

    public void setController(boolean enable, boolean isOverride) {
        this.controller = new State(enable, isOverride);
    }

    public void setMapper(boolean enable, boolean isOverride) {
        this.mapper = new State(enable, isOverride);
    }

    public void setMapperXml(boolean enable, boolean isOverride) {
        this.mapperXml = new State(enable, isOverride);
    }

    public void setGateway(boolean enable, boolean isOverride) {
        this.gateway = new State(enable, isOverride);
    }

    public void setGatewayImpl(boolean enable, boolean isOverride) {
        this.gatewayImpl = new State(enable, isOverride);
    }
}
