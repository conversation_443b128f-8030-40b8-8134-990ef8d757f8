#set(tableComment = table.getComment())
#set(primaryKeyType = table.getPrimaryKey().getPropertySimpleType())
#set(entityClassName = entityDO.className)
#set(entityVarName = firstCharToLowerCase(entityDO.className))
#set(gatewayVarName = firstCharToLowerCase(gateway.className))
package #(packageConfig.controllerPackage);

import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.dto.MultiResponse;
import cn.com.voyah.material.dto.PageResponse;
import cn.com.voyah.material.dto.SingleResponse;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
#(entityDO.buildClassImport())
#(gateway.buildClassImport())
#if(controllerConfig.restStyle)
import org.springframework.web.bind.annotation.RestController;
#else
import org.springframework.stereotype.Controller;
#end
#if(controllerConfig.superClass != null)
import #(controllerConfig.buildSuperClassImport());
#end
#if(withSwagger && swaggerVersion.getName() == "FOX")
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
#end
#if(withSwagger && swaggerVersion.getName() == "DOC")
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
#end

/**
 * #(tableComment) 控制层。
 *
 * <AUTHOR>
 * @since #(javadocConfig.getSince())
 */
@CatchAndLog
#if(controllerConfig.restStyle)
@RestController
#else
@Controller
#end
#if(withSwagger && swaggerVersion.getName() == "FOX")
@Api("#(tableComment)接口")
#end
#if(withSwagger && swaggerVersion.getName() == "DOC")
@Tag(name = "#(tableComment)接口")
#end
@RequestMapping("#(table.buildControllerRequestMappingPrefix())/#(table.getName().replaceAll("_", "-"))")
public class #(table.buildControllerClassName()) #if(controllerConfig.superClass)extends #(controllerConfig.buildSuperClassName()) #end {

    @Autowired
    private #(gateway.className) #(gatewayVarName);

    /**
     * 添加#(tableComment)。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    #if(withSwagger && swaggerVersion.getName() == "FOX")
    @ApiOperation("保存#(tableComment)")
    #end
    #if(withSwagger && swaggerVersion.getName() == "DOC")
    @Operation(summary = "保存#(tableComment)")
    #end
    public SingleResponse<#(entityClassName)> save(@RequestBody #if(withSwagger && swaggerVersion.getName() == "FOX")@ApiParam("#(tableComment)") #end #if(withSwagger && swaggerVersion.getName() == "DOC")@Parameter(description="#(tableComment)")#end #(entityClassName) #(entityVarName)) {
        return SingleResponse.of(#(gatewayVarName).save(#(entityVarName)));
    }

    /**
     * 根据主键删除#(tableComment)。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    #if(withSwagger && swaggerVersion.getName() == "FOX")
    @ApiOperation("根据主键#(tableComment)")
    #end
    #if(withSwagger && swaggerVersion.getName() == "DOC")
    @Operation(summary = "根据主键#(tableComment)")
    #end
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") #(primaryKeyType) id) {
        return SingleResponse.of(#(gatewayVarName).removeById(id));
    }

    /**
     * 根据主键更新#(tableComment)。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    #if(withSwagger && swaggerVersion.getName() == "FOX")
    @ApiOperation("根据主键更新#(tableComment)")
    #end
    #if(withSwagger && swaggerVersion.getName() == "DOC")
    @Operation(summary = "根据主键更新#(tableComment)")
    #end
    public SingleResponse<Boolean> update(@RequestBody #if(withSwagger && swaggerVersion.getName() == "FOX")@ApiParam("#(tableComment)主键")
    #end #if(withSwagger && swaggerVersion.getName() == "DOC")@Parameter(description="#(tableComment)主键")#end #(entityClassName) #(entityVarName)) {
        return SingleResponse.of(#(gatewayVarName).updateById(#(entityVarName)));
    }

    /**
     * 查询所有#(tableComment)。
     *
     * @return 所有数据
     */
    @GetMapping("list")
    #if(withSwagger && swaggerVersion.getName() == "FOX")
    @ApiOperation("查询所有#(tableComment)")
    #end
    #if(withSwagger && swaggerVersion.getName() == "DOC")
    @Operation(summary = "查询所有#(tableComment)")
    #end
    public MultiResponse<#(entityClassName)> list() {
        return MultiResponse.of(#(gatewayVarName).list());
    }

    /**
     * 根据#(tableComment)主键获取详细信息。
     *
     * @param id #(tableComment)主键
     * @return #(tableComment)详情
     */
    @GetMapping("by/{id}")
    #if(withSwagger && swaggerVersion.getName() == "FOX")
    @ApiOperation("根据主键获取#(tableComment)")
    #end
    #if(withSwagger && swaggerVersion.getName() == "DOC")
    @Operation(summary = "根据主键获取#(tableComment)")
    #end
    public SingleResponse<#(entityClassName)> getInfo(@PathVariable(name = "id") #(primaryKeyType) id) {
        return SingleResponse.of(#(gatewayVarName).getById(id));
    }

    /**
     * 分页查询#(tableComment)。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    #if(withSwagger && swaggerVersion.getName() == "FOX")
    @ApiOperation("分页查询#(tableComment)")
    #end
    #if(withSwagger && swaggerVersion.getName() == "DOC")
    @Operation(summary = "分页查询#(tableComment)")
    #end
    public PageResponse<#(entityClassName)> page(@RequestBody PageQueryCondition<#(entityClassName)> query) {
        return PageUtil.of(#(gatewayVarName).page(PageUtil.of(query)));
    }

}