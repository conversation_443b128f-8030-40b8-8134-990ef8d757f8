package #(convert.packageName);

#(convert.buildSuperClassImport())
import #(packageConfig.entityPackage).#(table.buildEntityClassName());
import #(entityDO.packageName).#(entityDO.className);

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 * #(table.getComment()) 对象转换。
 *
 * <AUTHOR>
 * @since #(javadocConfig.getSince())
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface #(convert.className) #if(convert.superClass)extends #(convert.buildSuperClassName())<#(entityDO.className), #(table
.buildEntityClassName())>#end{

}