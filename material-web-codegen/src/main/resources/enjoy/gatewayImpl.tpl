package #(gatewayImpl.packageName);

#(gatewayImpl.buildSuperClassImport())
#(gateway.buildClassImport())
#(convert.buildClassImport())
#(entityDO.buildClassImport())
import #(packageConfig.entityPackage).#(table.buildEntityClassName());
import #(packageConfig.mapperPackage).#(table.buildMapperClassName());
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * #(table.getComment()) 服务层实现。
 *
 * <AUTHOR>
 * @since #(javadocConfig.getSince())
 */
@Component
@AllArgsConstructor
public class #(gatewayImpl.className) #if(gatewayImpl.superClass)extends #(gatewayImpl.buildSuperClassName())<#(entityDO.className), #(table
.buildMapperClassName()), #(table.buildEntityClassName()), #(convert.className)>#end  implements #(gateway.className) {

}