<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.voyah.material</groupId>
        <artifactId>material-server</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>material-web-codegen</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <dependencies>
        <!-- MyBatis-Flex 代码生成器 -->
        <dependency>
            <groupId>com.mybatis-flex</groupId>
            <artifactId>mybatis-flex-codegen</artifactId>
            <scope>compile</scope>
        </dependency>

        <!-- 数据源连接池 -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.voyah.material</groupId>
            <artifactId>material-web-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.voyah.material</groupId>
            <artifactId>material-web-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.voyah.material</groupId>
            <artifactId>material-web-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.voyah.material</groupId>
            <artifactId>material-component-mybatisflex</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>