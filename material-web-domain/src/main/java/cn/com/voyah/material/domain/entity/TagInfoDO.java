package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import cn.com.voyah.material.dto.TagInfoBaseDTO;
import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

/**
 *  实体类。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "")
public class TagInfoDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "字典主键")
    private Long id;

    /**
     * 字典名称
     */
    @Schema(description = "字典名称")
    private String name;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;

    /**
     * 字典描述
     */
    @Schema(description = "字典描述")
    private String tagValue;

    /**
     * 类目id
     */
    @Schema(description = "类目id")
    private Long categoryId;

    /**
     * 父级id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "父级id")
    private Long parentId;

    /**
     * 层级
     */
    @Schema(description = "层级")
    private Integer level;

    /**
     * 状态 0：非必填  1：必填
     */
    @Schema(description = "状态 0：非必填  1：必填")
    private Integer required;

    @Schema(description = "0 显示 1 隐藏")
    private boolean hidden=false;


    @Schema(description = "0 可见 1 部分可见  2 隐藏")
    private Integer hiddenStatus;

    public static TagInfoBaseDTO buildToBase(TagInfoDO tagInfoDO){
        TagInfoBaseDTO tagInfoBaseDTO=new TagInfoBaseDTO();
        BeanUtils.copyProperties(tagInfoDO,tagInfoBaseDTO);
        return tagInfoBaseDTO;
    }

    public static List<TagInfoBaseDTO> buildToBaseList(List<TagInfoDO> list){
        if(CollectionUtil.isEmpty(list)){
            return List.of();
        }
        return list.stream().map(TagInfoDO::buildToBase).collect(Collectors.toList());
    }

}