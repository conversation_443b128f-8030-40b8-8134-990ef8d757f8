package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 文件记录中间表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件记录中间表")
public class FileDetailTmpDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件id
     */
    @Schema(description = "文件id")
    private Long id;

    @Schema(description = "")
    private String url;

    /**
     * 文件大小，单位字节
     */
    @Schema(description = "文件大小，单位字节")
    private Long size;

    @Schema(description = "")
    private String filename;

    @Schema(description = "")
    private String originalFilename;

    /**
     * 基础存储路径
     */
    @Schema(description = "基础存储路径")
    private String basePath;

    @Schema(description = "")
    private String path;

    /**
     * MIME类型
     */
    @Schema(description = "MIME类型")
    private String contentType;

    /**
     * 缩略图访问路径
     */
    @Schema(description = "缩略图访问路径")
    private String thUrl;

    /**
     * 缩略图名称
     */
    @Schema(description = "缩略图名称")
    private String thFilename;

    /**
     * 缩略图大小，单位字节
     */
    @Schema(description = "缩略图大小，单位字节")
    private Long thSize;

    /**
     * 缩略图MIME类型
     */
    @Schema(description = "缩略图MIME类型")
    private String thContentType;

    @Schema(description = "")
    private Integer status;

}