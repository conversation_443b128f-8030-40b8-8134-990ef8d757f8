package cn.com.voyah.material.domain.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "cos")
public class CosConfig {
    private String secretId;
    private String secretKey;
    private String appId;
    private String bucket;
    private String region;
    private String url;
    private String prefix;

    private int duration;
    private boolean urlPresigned=false;
}
