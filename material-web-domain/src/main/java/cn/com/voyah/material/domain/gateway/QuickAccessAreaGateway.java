package cn.com.voyah.material.domain.gateway;

import cn.com.voyah.material.domain.entity.QuickAccessAreaDO;
import cn.com.voyah.material.mybatisflex.gateway.IGateway;
import cn.com.voyah.material.pojo.QuickAccessAreaPO;


/**
 * 金刚位表 服务层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface QuickAccessAreaGateway extends IGateway<QuickAccessAreaDO> {
    QuickAccessAreaPO getNearerDOBySort(Long sort, boolean asc);
    boolean updateSort(Long id, Long sort);
}