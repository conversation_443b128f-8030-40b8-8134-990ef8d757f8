package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 收藏表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "收藏表")
public class MaterialFavoriteDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "")
    private Integer id;

    /**
     * 素材库id
     */
    @Schema(description = "素材库id")
    private Long materialId;

    @Schema(description = "")
    private String shortId;

    @Schema(description = "")
    private String userName;

}