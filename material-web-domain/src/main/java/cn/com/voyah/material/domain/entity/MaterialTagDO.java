package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import cn.com.voyah.material.dto.MaterialTagDTO;
import java.io.Serializable;

import cn.com.voyah.material.dto.MaterialTagNewDTO;
import cn.com.voyah.material.dto.MaterialTagVO;
import cn.com.voyah.material.pojo.MaterialTagPO;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

/**
 *  实体类。
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "")
public class MaterialTagDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "")
    private Long id;

    @Schema(description = "")
    private Long materialId;

    @Schema(description = "")
    private String remark;

    /**
     * 用户浏览量
     */
    private Long userViewCount;
    /**
     * 用户收藏量
     */
    private Long userFavoriteCount;

    /**
     * 用户下载量
     */
    private Long userDownloadCount;


    public static MaterialTagDO buildFrom(MaterialTagVO materialTagVO) {
        MaterialTagDO materialTagDO = new MaterialTagDO();
        materialTagDO.setMaterialId(materialTagVO.getMaterialId());
        materialTagDO.setUserViewCount(0L);
        materialTagDO.setUserFavoriteCount(0L);
        materialTagDO.setUserDownloadCount(0L);
        return materialTagDO;
    }
    public static MaterialTagDO buildFrom(MaterialTagNewDTO materialTagNewDTO) {
        MaterialTagDO materialTagDO = new MaterialTagDO();
        materialTagDO.setMaterialId(materialTagNewDTO.getMaterialId());
        materialTagDO.setUserViewCount(0L);
        materialTagDO.setUserFavoriteCount(0L);
        materialTagDO.setUserDownloadCount(0L);
        return materialTagDO;
    }

    public static MaterialTagDO buildFrom(Long materialId,String remark) {
        MaterialTagDO materialTagDO = new MaterialTagDO();
        materialTagDO.setMaterialId(materialId);
        materialTagDO.setUserViewCount(0L);
        materialTagDO.setUserFavoriteCount(0L);
        materialTagDO.setUserDownloadCount(0L);
        materialTagDO.setRemark(remark);
        return materialTagDO;
    }






    public static MaterialTagDO buildFrom(MaterialTagPO materialTagPO) {
        if(ObjectUtil.isEmpty(materialTagPO)){
            return null;
        }
        MaterialTagDO materialTagDO = new MaterialTagDO();
        BeanUtils.copyProperties(materialTagPO, materialTagDO);
        return materialTagDO;
    }

    public static MaterialTagDTO buildTO(MaterialTagDO materialTagDO) {
        if(ObjectUtil.isEmpty(materialTagDO)){
            return null;
        }
        MaterialTagDTO materialTagDTO = new MaterialTagDTO();
        BeanUtils.copyProperties(materialTagDO, materialTagDTO);
        return materialTagDTO;

    }

}