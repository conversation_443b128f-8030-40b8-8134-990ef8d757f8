package cn.com.voyah.material.domain.entity.dto;

import cn.com.voyah.material.domain.config.CosConfig;
import com.tencent.cloud.Response;
import lombok.Data;

//存储对象临时访问凭证
@Data
public class ResponseDTO {
    //"存储桶名"
    private String bucket;
    //"地域"
    private String region;
    //"临时证书密钥ID"
    private String secretId;
    //"临时证书密钥Key"
    private String secretKey;
    //"临时令牌"
    private String sessionToken;
    //"临时访问凭证开始时间"
    private long startTime;
    //"临时访问凭证过期时间"
    private long expiredTime;

    private String dirPrefix;

    public static ResponseDTO from(CosConfig cosConfig, Response response,String dirPrefix){
        ResponseDTO responseDTO=new ResponseDTO();
        responseDTO.setBucket(cosConfig.getBucket());
        responseDTO.setRegion(cosConfig.getRegion());
        responseDTO.setSecretId(response.credentials.tmpSecretId);
        responseDTO.setSecretKey(response.credentials.tmpSecretKey);
        responseDTO.setSessionToken(response.credentials.sessionToken);
        responseDTO.setStartTime(response.startTime);
        responseDTO.setExpiredTime(response.expiredTime);
        responseDTO.setDirPrefix(dirPrefix);
        return responseDTO;

    }
}