package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 素材库（类型） 实体类。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "素材库（类型）")
public class MaterialLibraryDO extends BaseDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 素材ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "素材ID")
    private Long id;

    /**
     * 素材库编号
     */
    @Schema(description = "素材库编号")
    private String libraryNo;

    /**
     * 素材库名称
     */
    @Schema(description = "素材库名称")
    private String libraryName;

    /**
     * 是否公开 0：否  1：是
     */
    @Schema(description = "是否公开 0：否  1：是")
    private Integer isPublic;

    /**
     * 启用状态 0：禁用 1：启用
     */
    @Schema(description = "启用状态 0：禁用 1：启用")
    private Integer status;

    /**
     * 原始素材主文件夹ID
     */
    @Schema(description = "原始素材主文件夹ID")
    private Long originMaterialId;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

}