package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import cn.com.voyah.material.dto.MaterialTagVO;
import cn.com.voyah.material.pojo.MaterialTagItemPO;
import cn.com.voyah.material.pojo.MaterialTagPO;
import cn.com.voyah.material.vo.user.TagInfoVO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

/**
 *  实体类。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "")
public class MaterialTagItemDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "字典主键")
    private Long id;

    /**
     * 字典名称
     */
    @Schema(description = "字典名称")
    private String name;

    /**
     * 字典描述
     */
    @Schema(description = "字典描述")
    private String tagValue;
    private String code;

    /**
     * 字典主键
     */
    @Schema(description = "字典主键")
    private Long tagId;

    @Schema(description = "")
    private Long materialId;

    /**
     * 类目id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "类目id")
    private Long categoryId;

    /**
     * 父级id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "父级id")
    private Long parentId;

    /**
     * 层级
     */
    @Schema(description = "层级")
    private Integer level;

    /**
     * 状态 0：非必填  1：必填
     */
    @Schema(description = "状态 0：非必填  1：必填")
    private Integer required;

    private Long materialTagId;

    public static MaterialTagItemDO buildFrom(MaterialTagItemPO materialTagItemPO) {
        MaterialTagItemDO materialTagItemDO = new MaterialTagItemDO();
        BeanUtils.copyProperties(materialTagItemPO,materialTagItemDO);
        return materialTagItemDO;
    }
    public static List<MaterialTagItemDO> buildFrom(List<MaterialTagItemPO> materialTagPOList) {
        if(CollectionUtil.isEmpty(materialTagPOList)){
            return List.of();
        }
        return BeanUtil.copyToList(materialTagPOList,MaterialTagItemDO.class);
    }
    public static TagInfoVO buildToVo(MaterialTagItemDO materialTagItemDO) {
        if(ObjectUtil.isEmpty(materialTagItemDO)){
            return null;
        }
        TagInfoVO tagInfoVO=new TagInfoVO();
        BeanUtil.copyProperties(materialTagItemDO,tagInfoVO,"id","tagId");
        tagInfoVO.setId(materialTagItemDO.getTagId());
        return tagInfoVO;
    }

    public static List<TagInfoVO> buildToVo(List<MaterialTagItemDO> materialTagDOList) {
        if(CollectionUtil.isEmpty(materialTagDOList)){
            return List.of();
        }
        return materialTagDOList.stream().map(MaterialTagItemDO::buildToVo).collect(Collectors.toList());
    }



}