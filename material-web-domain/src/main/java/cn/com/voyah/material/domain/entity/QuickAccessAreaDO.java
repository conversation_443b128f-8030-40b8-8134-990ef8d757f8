package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 金刚位表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "金刚位表")
public class QuickAccessAreaDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long id;

    @Schema(description = "标签列表")
    private List<TagInfoMergeVO> tags;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long fileId;

    @Schema(description = "")
    private String name;

    @Schema(description = "")
    private String remark;

    @Schema(description = "")
    private Long sort;

    @Schema(description = "")
    private LocalDateTime startTime;

    @Schema(description = "")
    private LocalDateTime endTime;

    /**
     * 位置
     */
    @Schema(description = "位置")
    private Integer position;


    @Schema(description = "上架状态 0下架 1 上架")
    private Integer status;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "背景图ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long backgroundFileId;


}