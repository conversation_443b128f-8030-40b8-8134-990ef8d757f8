package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import cn.com.voyah.material.dto.TagCategoryDTO;
import cn.com.voyah.material.dto.TagInfoDTO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.util.List;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

/**
 *  实体类。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "")
public class TagCategoryDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long id;

    @Schema(description = "")
    private String name;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Long sort;

    @Schema(description = "")
    private String desc;

    /**
     * 状态 0：停用  1：启用
     */
    @Schema(description = "状态 0：停用  1：启用")
    private Integer status;

    @Schema(description = "状态 0：非必填  1：必填")
    private Integer required;

    public static TagCategoryDTO buildToDTO(TagCategoryDO tagCategoryDO,List<TagInfoDTO> list){
        TagCategoryDTO tagCategoryDTO=new TagCategoryDTO();
        BeanUtils.copyProperties(tagCategoryDO,tagCategoryDTO);
        tagCategoryDTO.setTagList(list);
        return tagCategoryDTO;
    }


}