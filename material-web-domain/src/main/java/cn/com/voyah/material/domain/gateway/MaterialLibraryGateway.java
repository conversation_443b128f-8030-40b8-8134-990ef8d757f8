package cn.com.voyah.material.domain.gateway;

import cn.com.voyah.material.domain.entity.MaterialLibraryDO;
import cn.com.voyah.material.mybatisflex.gateway.IGateway;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 素材库（类型） 服务层。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
public interface MaterialLibraryGateway extends IGateway<MaterialLibraryDO> {

    /**
     * 启用和禁用
     * @param id 库ID
     * @param status true: 启用， false: 禁用
     */
    void enableOnDisable(Long id, boolean status);

    @Transactional(rollbackFor = Exception.class)
    void updateSort(List<MaterialLibraryDO> list);

    boolean updatePublicByMaterialId(Long materialId,Integer publicStatus);
}