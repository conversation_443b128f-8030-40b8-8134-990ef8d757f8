package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.time.LocalDateTime;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 任务基本信息表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "任务基本信息表")
public class TaskInfoDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long id;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String taskName;

    /**
     * 任务期望完成完成时间
     */
    @Schema(description = "任务期望开始时间")
    private LocalDateTime taskStartTime;

    /**
     * 任务期望完成完成时间
     */
    @Schema(description = "任务期望完成完成时间")
    private LocalDateTime taskEndTime;

    /**
     * 派发人姓名
     */
    @Schema(description = "派发人姓名")
    private String dispatchBy;

    /**
     * 派发人短id
     */
    @Schema(description = "派发人短id")
    private String dispatchShortId;

    /**
     * 执行人姓名
     */
    @Schema(description = "执行人姓名")
    private String executeBy;

    /**
     * 执行人短id
     */
    @Schema(description = "执行人短id")
    private String executeShortId;

    /**
     * 任务状态  新建进行中
     */
    @Schema(description = "任务状态  新建进行中 0 新建 1进行中 2已完成")
    private Integer taskStatus;

    /**
     * 素材库id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "素材库id")
    private Long materialFoldId;

    /**
     * 素材数量
     */
    @Schema(description = "素材数量")
    private Integer materialFileCount;

    @Schema(description = "")
    private LocalDateTime taskFinishTime;


    /**
     * 素材数量
     */
    @Schema(description = "已经上传素材数量")
    private Integer actualFileCount;


    private String remark;
}