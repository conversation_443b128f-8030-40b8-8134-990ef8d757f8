package cn.com.voyah.material.domain.entity;

import java.io.Serial;
import java.io.Serializable;


import cn.com.voyah.material.domain.entity.base.BaseDO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 文件记录表 实体类。
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件记录表")
public class FileDetailDO extends BaseDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "文件id")
    private Long id;

    /**
     * 文件访问地址
     */
    @Schema(description = "文件访问地址")
    private String url;

    /**
     * 文件大小，单位字节
     */
    @Schema(description = "文件大小，单位字节")
    private Long size;

    /**
     * 文件路径
     */
    @Schema(description = "文件名称")
    private String filename;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名")
    private String originalFilename;

    /**
     * 基础存储路径
     */
    @Schema(description = "基础存储路径")
    private String basePath;

    /**
     * 存储路径
     */
    @Schema(description = "存储完整路径")
    private String path;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名")
    private String ext;

    /**
     * MIME类型
     */
    @Schema(description = "MIME类型")
    private String contentType;

    /**
     * 存储平台
     */
    @Schema(description = "存储平台")
    private String platform;

    private String tag;

    /**
     * 上传ID，仅在手动分片上传时使用
     */
    @Schema(description = "上传ID，仅在手动分片上传时使用")
    private String uploadId;


    /**
     * 上传状态，仅在手动分片上传时使用，1：初始化完成，2：上传完成
     */
    private Integer uploadStatus;

    /**
     * 缩略图访问路径
     */
    private String thUrl;

    /**
     * 缩略图名称
     */
    private String thFilename;

    private String thContentType;


    /**
     * 缩略图大小，单位字节
     */
    private Long thSize;

    /**
     * 哈希信息
     */
    private String hashInfo;

}