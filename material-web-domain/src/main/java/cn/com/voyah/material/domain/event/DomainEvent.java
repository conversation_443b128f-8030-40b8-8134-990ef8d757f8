package cn.com.voyah.material.domain.event;

import cn.com.voyah.material.constants.EventTypeEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.Clock;

@Getter
public class DomainEvent<T> extends ApplicationEvent {
    /**
     * 事件类型
     */
    private final EventTypeEnum eventType;
    /**
     * 事件数据
     */
    private final T data;

    public DomainEvent(EventTypeEnum eventType, T data) {
        super(eventType.name(), Clock.systemUTC());
        this.eventType = eventType;
        this.data = data;
    }
}
