package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 通知表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "通知表")
public class NoticeMessageDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long id;
    @Schema(description = "1.任务派发通知2.任务到期提醒")
    private Integer type;

    @Schema(description = "")
    private String content;

    @Schema(description = "")
    private String shortId;

    @Schema(description = "")
    private String userName;

    @Schema(description = "")
    private LocalDateTime sendTime;

    @Schema(description = "0 任务")
    private Integer source;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "关联id")
    private Long relationId;

    @Schema(description = "0 未读 1已读")
    private Integer state;

}