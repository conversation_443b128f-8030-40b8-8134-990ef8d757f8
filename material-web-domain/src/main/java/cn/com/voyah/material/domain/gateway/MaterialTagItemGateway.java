package cn.com.voyah.material.domain.gateway;

import cn.com.voyah.material.mybatisflex.gateway.IGateway;
import cn.com.voyah.material.domain.entity.MaterialTagItemDO;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;

import java.util.List;

/**
 *  服务层。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface MaterialTagItemGateway extends IGateway<MaterialTagItemDO> {
    List<MaterialTagItemDO> getBaseList(Long materialId, Long categoryId);

    List<MaterialTagItemDO> getList(Long materialId, Long categoryId);

    boolean delete(Long materialId, Long categoryId);

    boolean deleteByTagIds(Long categoryId,Long materialId, List<Long> tagIds);

    List<Long> getCategoryIdList(Long materialId);

    List<TagInfoMergeVO> getTagMergeVoList(Long materialId, Long categoryId);

    List<Long> getMaterialIdListByTags(List<TagInfoMergeVO> tags);
}