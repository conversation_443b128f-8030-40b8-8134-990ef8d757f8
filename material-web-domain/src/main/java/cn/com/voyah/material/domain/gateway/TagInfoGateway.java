package cn.com.voyah.material.domain.gateway;

import cn.com.voyah.material.domain.entity.TagInfoDO;
import cn.com.voyah.material.dto.query.TagInfoQuery;
import cn.com.voyah.material.mybatisflex.gateway.IGateway;

import java.util.List;

/**
 *  服务层。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface TagInfoGateway extends IGateway<TagInfoDO> {

    List<TagInfoDO> list(TagInfoDO tagInfoDO);
    List<TagInfoDO> getList(Long categoryId);

    List<TagInfoDO> getList(TagInfoDO tagInfoDO,List<Long> parentIdList);

    List<TagInfoDO> getTagNameAndLevelList(Long categoryId);

    List<TagInfoDO> getListByIdList(List<Long> idList);

    List<TagInfoDO> getByParentIdList(TagInfoQuery tagInfoQuery);

    List<TagInfoDO> getListByParentId(Long parentId);

    List<TagInfoDO> getList(Long categoryId, Integer level, String tagName,List<String> values, List<Long> parentIdList);

    List<TagInfoDO> getTagNameAndValueList(Long categoryId,boolean filterHiden);

    void updateHiddenStatusByExistTagIdList(List<Long> existTagIdList);

    void setHiddenStatus(List<TagInfoDO> tagInfoDOS);
    List<TagInfoDO> getAllTreeList(Long id);

    List<TagInfoDO> getAllSubTreeList(Long id);

    List<TagInfoDO> getAllParentList(Long id);
}