package cn.com.voyah.material.domain.gateway;

import cn.com.voyah.material.mybatisflex.gateway.IGateway;
import cn.com.voyah.material.domain.entity.MaterialTagDO;
import cn.com.voyah.material.pojo.OriginMaterialPO;
import cn.hutool.core.util.ObjectUtil;

import java.util.List;

/**
 *  服务层。
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface MaterialTagGateway extends IGateway<MaterialTagDO> {

    MaterialTagDO getByMaterialId(Long materialId);
    Boolean updateTagRemark(Long id, String remark);
    boolean addViewCount(Long materialId);
    boolean addFavoriteCount(Long materialId,boolean isIncr);

    boolean addDownloadCount(Long materialId);

    List<Long> existTagIdList();
}