package cn.com.voyah.material.domain.gateway;

import cn.com.voyah.material.mybatisflex.gateway.IGateway;
import cn.com.voyah.material.domain.entity.MaterialAuthDO;
import cn.com.voyah.material.pojo.MaterialAuthPO;

import java.util.List;

/**
 *  服务层。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface MaterialAuthGateway extends IGateway<MaterialAuthDO> {
    List<MaterialAuthPO> getList(String shortId);

    MaterialAuthPO getAuth(String shortId, Long materialFoldId);

    void updateAllAuth(List<String>materialIdList,Long materialId);
}