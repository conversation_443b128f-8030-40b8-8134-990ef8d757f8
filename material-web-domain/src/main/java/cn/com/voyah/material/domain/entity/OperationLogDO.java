package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import cn.com.voyah.material.dto.OperationLogExportVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

/**
 * 日志表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "日志表")
public class OperationLogDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long id;

    @Schema(description = "")
    private String shortId;

    @Schema(description = "")
    private String userName;

    @Schema(description = " 0 成功 1失败")
    private Integer state;

    @Schema(description = "")
    private String interfaceRemark;

    @Schema(description = "")
    private LocalDateTime operationTime;

    @Schema(description = "")
    private String ip;

    @Schema(description = "")
    private String path;

    @Schema(description = "")
    private String method;

    @Schema(description = "")
    private String requestParams;

    @Schema(description = "")
    private String response;

    @Schema(description = "")
    private String errorMsg;

    @Schema(description = "")
    private Long timeInterval;

    @Schema(description = "")
    private LocalDateTime requestTime;

    @Schema(description = "")
    private LocalDateTime responseTime;
    public static OperationLogExportVO buildToExportVO(OperationLogDO each){
        OperationLogExportVO operationLogExportVO=new OperationLogExportVO();
        BeanUtils.copyProperties(each,operationLogExportVO);
        return operationLogExportVO;
    }

    public static List<OperationLogExportVO> buildToExportVO(List<OperationLogDO> list){
        return list.stream().map(OperationLogDO::buildToExportVO).collect(Collectors.toList());
    }

}