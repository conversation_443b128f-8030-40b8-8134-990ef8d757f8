package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import cn.com.voyah.material.dto.OriginMaterialDTO;
import cn.com.voyah.material.exception.BizException;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.handler.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 素材 实体类。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "素材")
@EqualsAndHashCode(callSuper = true)
public class OriginMaterialDO extends BaseDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 素材ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "素材ID")
    private Long id;

    /**
     * 素材名称
     */
    @Schema(description = "素材名称")
    private String materialName;

    /**
     * 素材库id 1：产品素材 2：传播素材 已经多余，可以去掉
     */
    @Schema(description = "素材类型 1：产品素材 2：传播素材")
    private Long libraryId;

    /**
     * 是否公开 0：否  1：是
     */
    @Schema(description = "是否公开 0：否  1：是")
    private Integer isPublic;

    /**
     * 是否是目录 0：否  1：是
     */
    @Schema(description = "是否是目录 0：否  1：是")
    private Boolean isDir;

    /**
     * 素材层级 0级为素材库
     */
    @Schema(description = "素材层级 0级为素材库")
    private Integer level;

    /**
     * 是否显示
     */
    @Schema(description = "是否显示")
    private Integer isShow;

    /**
     * 层级父级ID
     */
    @Schema(description = "层级父级ID")
    private Long parentId;

    /**
     * 目录及文件大小 Byte
     */
    @Schema(description = "目录及文件大小 Byte")
    private Long fileSize;

    /**
     * 文件类型 如；dir、pdf、img
     */
    @Schema(description = "文件类型 如；dir、pdf、img")
    private String fileType;

    /**
     * 文件本身的id
     */
    @Schema(description = "文件本身的id")
    private Long fileId;

    /**
     * 最后上传人
     */
    @Schema(description = "最后上传人")
    private String lastUploadUser;

    /**
     * 最后上传时间
     */
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后上传时间")
    private LocalDateTime lastUploadTime;
    @Column(value="parent_id_list",typeHandler = JacksonTypeHandler.class)
    @Schema(description = "层级父级ID")
    private List<Long> parentIdList;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    private Long fileCount;

    private Integer tagStatus;

    public static OriginMaterialDO buildFrom(OriginMaterialDTO originMaterialDTO) {
        OriginMaterialDO originMaterialDO = new OriginMaterialDO();
        BeanUtils.copyProperties(originMaterialDTO, originMaterialDO);
        emptyCheck(originMaterialDO);
        return originMaterialDO;

    }

    public static void emptyCheck(OriginMaterialDO originMaterialDO) {
//        if (ObjectUtils.isEmpty(originMaterialDO.getLibraryId())) {
//            //默认为0L
//            throw new BizException("素材库id不能为空!");
//        }
        if (!StringUtils.hasLength(originMaterialDO.getMaterialName())) {
            throw new BizException("素材名称不能为空!");
        }
        if (ObjectUtils.isEmpty(originMaterialDO.getParentId())) {
            //默认为0L
            throw new BizException("父级文件夹id不能为空!");
        }
        if (originMaterialDO.getIsDir()) {
            originMaterialDO.setFileId(0L);
        } else if (ObjectUtils.isEmpty(originMaterialDO.getFileId())) {
            throw new BizException("文件id不能为空!");
        }
    }


}