package cn.com.voyah.material.domain.gateway;

import cn.com.voyah.material.constants.TaskStatus;
import cn.com.voyah.material.mybatisflex.gateway.IGateway;
import cn.com.voyah.material.domain.entity.TaskInfoDO;
import cn.com.voyah.material.pojo.TaskInfoPO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务基本信息表 服务层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface TaskInfoGateway extends IGateway<TaskInfoDO> {
    List<TaskInfoPO> getList(String shortId, Long foldId, LocalDateTime endTime);
    List<TaskInfoPO> getProcessList(String shortId, Long foldId, LocalDateTime endTime);
    boolean updateFileCount(Long id, int actualFileCount);

    boolean updateTaskStatus(Long id, TaskStatus taskStatus,LocalDateTime updateTime);

    boolean updateStatusBatch(List<Long> ids, TaskStatus status);
}