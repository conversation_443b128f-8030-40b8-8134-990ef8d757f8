package cn.com.voyah.material.domain.gateway;

import cn.com.voyah.material.domain.entity.OriginMaterialDO;
import cn.com.voyah.material.dto.OriginMaterialExtensionDTO;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.mybatisflex.gateway.IGateway;
import com.mybatisflex.core.paginate.Page;

import java.util.List;

/**
 * 素材 服务层。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
public interface OriginMaterialGateway extends IGateway<OriginMaterialDO> {


    Page<OriginMaterialExtensionDTO> getPageInfo(Page<OriginMaterialExtensionDTO> pageQuery,OriginMaterialExtensionDTO originMaterialExtensionDTO,List<Long> authMaterialIdList);

    OriginMaterialExtensionDTO getDetailById(Long id);

    int updateEntity(OriginMaterialDO originMaterialDO);

    List<OriginMaterialExtensionDTO> getList(OriginMaterialExtensionDTO originMaterialExtensionDTO,List<Long> authMaterialIdList);

    void updateChildFileCountById(Long parentId);

    /**
     * 删除对应等级下的文件及目录，包含 parent 自身
     *
     * @param id 原素材中的ID（目录）
     */
    void removeIdAndChildAll(Long id);

    /**
     * 修改显示状态
     * @param id 原素材ID
     * @param isShow 是否显示 @see {@link OriginMaterialDO#getIsShow()}
     */
    void updateIsShow(Long id, Integer isShow);

    Boolean updateTagStatus(Long id, Integer tagStatus);

    int updatePublicStatusBatchByParentIdList(Long parentId,Integer publicStatus);


    void updatePublicStatusByChildStatus(Long parentId);

    void updateSelfPublicStatusById(Long id,Integer publicStatus);

    List<OriginMaterialExtensionDTO> getLabList(OriginMaterialExtensionDTO originMaterialExtensionDTO);

    List<OriginMaterialExtensionDTO> getChildrenList(Long id,Boolean onlyDir);
}