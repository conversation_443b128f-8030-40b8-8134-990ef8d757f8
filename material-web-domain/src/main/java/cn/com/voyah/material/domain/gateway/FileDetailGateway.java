package cn.com.voyah.material.domain.gateway;


import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.mybatisflex.gateway.IGateway;


/**
 * 文件记录表 服务层。
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface FileDetailGateway extends IGateway<FileDetailDO> {

    FileDetailDO getOneByEntity(FileDetailDO fileDetailDO);

    void updateThById(FileDetailDO fileDetailDO);
}