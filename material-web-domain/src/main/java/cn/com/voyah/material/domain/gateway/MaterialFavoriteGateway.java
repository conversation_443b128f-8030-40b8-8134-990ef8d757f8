package cn.com.voyah.material.domain.gateway;

import cn.com.voyah.material.mybatisflex.gateway.IGateway;
import cn.com.voyah.material.domain.entity.MaterialFavoriteDO;

import java.util.List;

/**
 * 收藏表 服务层。
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface MaterialFavoriteGateway extends IGateway<MaterialFavoriteDO> {

    boolean deleteByMaterialIdAndShortId(Long materialId,String shortId);
    List<Long> getHaveFavoriteIdList(List<Long> materialIdList, String shortId);
}