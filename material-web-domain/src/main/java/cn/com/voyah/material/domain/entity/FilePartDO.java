package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import java.io.Serializable;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 文件分片信息表，仅在手动分片上传时使用 实体类。
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件分片信息表，仅在手动分片上传时使用")
public class FilePartDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分片id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "分片id")
    private Long id;

    /**
     * 存储平台
     */
    @Schema(description = "存储平台")
    private String platform;

    /**
     * 上传ID，仅在手动分片上传时使用
     */
    @Schema(description = "上传ID，仅在手动分片上传时使用")
    private String uploadId;

    /**
     * 分片 ETag
     */
    @Schema(description = "分片 ETag")
    private String eTag;

    /**
     * 分片号。每一个上传的分片都有一个分片号，一般情况下取值范围是1~10000
     */
    @Schema(description = "分片号。每一个上传的分片都有一个分片号，一般情况下取值范围是1~10000")
    private Integer partNumber;

    /**
     * 文件大小，单位字节
     */
    @Schema(description = "文件大小，单位字节")
    private Long partSize;

    /**
     * 哈希信息
     */
    @Schema(description = "哈希信息")
    private String hashInfo;

}