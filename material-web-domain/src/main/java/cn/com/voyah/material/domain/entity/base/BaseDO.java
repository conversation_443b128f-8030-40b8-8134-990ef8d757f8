package cn.com.voyah.material.domain.entity.base;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BaseDO {
    /**
     * 创建人短ID
     */
    private String createShortId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人短ID
     */
    private String updateShortId;

    /**
     * 修改人
     */
    private String updateBy;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Boolean isDelete;

    private Long version;

}
