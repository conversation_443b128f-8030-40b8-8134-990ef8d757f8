package cn.com.voyah.material.domain.entity;

import cn.com.voyah.material.domain.entity.base.BaseDO;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 *  实体类。
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "")
public class ClientInfoDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "桌面客户端名称")
    private String name;

    @Schema(description = "当前版本")
    private String currentVersion;

    @Schema(description = "类型 1.为window 2为mac")
    private Integer type;

    @Schema(description = " 1 为激活")
    private Integer active;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "文件id")
    private Long fileId;

}