package cn.com.voyah.material.domain.entity.migrate;

import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.entity.OriginMaterialDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.List;

@Data
public class MigrateEntityDO {

    private Long materialId;
    private Long parentId;
    private String materialName;
    private Long fileId;
    private String filename;
    private String originFlename;
    private List<Long> parentIdList;
    private Long fileCount;
    private String contentType;
    private Boolean isDir;
    /**
     * 文件访问地址
     */
    private String url;

    /**
     * 文件大小，单位字节
     */
    private Long size;
    /**
     * 存储平台
     */
    private String platform;

    private String pathPrefix;

//    public List<MigrateEntity> children;

    @Schema(description = "素材层级 0级为素材库")
    private Integer level;

    private String path;

    public static FileDetailDO buildToFileDetailDO(MigrateEntityDO migrateEntityDO){
        FileDetailDO fileDetailDO=new FileDetailDO();
        BeanUtils.copyProperties(migrateEntityDO,fileDetailDO);
        fileDetailDO.setId(migrateEntityDO.getFileId());
        return fileDetailDO;

    }

    public static OriginMaterialDO buildToOriginMaterialDO(MigrateEntityDO migrateEntityDO){
        OriginMaterialDO originMaterialDO=new OriginMaterialDO();
        BeanUtils.copyProperties(migrateEntityDO,originMaterialDO);
        originMaterialDO.setId(migrateEntityDO.getMaterialId());
        originMaterialDO.setIsShow(1);
        originMaterialDO.setFileSize(migrateEntityDO.getSize());
        originMaterialDO.setFileType(migrateEntityDO.getContentType());
        return originMaterialDO;
    }


}
