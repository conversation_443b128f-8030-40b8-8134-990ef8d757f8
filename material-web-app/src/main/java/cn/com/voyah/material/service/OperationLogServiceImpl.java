package cn.com.voyah.material.service;

import cn.com.voyah.material.client.OperationLogService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.OperationLogDO;
import cn.com.voyah.material.domain.gateway.OperationLogGateway;
import cn.com.voyah.material.dto.OperationLogExportQuery;
import cn.com.voyah.material.dto.OperationLogExportVO;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Objects;
import static cn.com.voyah.domain.entity.def.OperationLogDef.OPERATION_LOG;

@Service
public class OperationLogServiceImpl implements OperationLogService {
    @Autowired
    private OperationLogGateway operationLogGateway;

    @Override
    public List<OperationLogDO> list(OperationLogExportQuery operationLogExportQuery) {
        QueryWrapper query = QueryWrapper.create();
        query.select(OPERATION_LOG.ALL_COLUMNS)
                .from(OPERATION_LOG.getTableName())
                .where(OPERATION_LOG.IS_DELETE.eq(false))
                // 主文件夹不展示
                .orderBy(OPERATION_LOG.CREATE_TIME.getName(),false);
        if (Objects.nonNull(operationLogExportQuery.getCreateStartTime()) && Objects.nonNull(operationLogExportQuery.getCreateEndTime())) {
            query.and(OPERATION_LOG.CREATE_TIME.ge(operationLogExportQuery.getCreateStartTime()))
                    .and(OPERATION_LOG.CREATE_TIME.le(operationLogExportQuery.getCreateEndTime()));
        }
        if (Objects.nonNull(operationLogExportQuery)&&Objects.nonNull(operationLogExportQuery.getState())) {
            query.and(OPERATION_LOG.STATE.eq(operationLogExportQuery.getState()));
        }
        if (StringUtils.isNotEmpty(operationLogExportQuery.getUserNameOrShortId())) {
            query.and(OPERATION_LOG.USER_NAME.like(operationLogExportQuery.getUserNameOrShortId())
                    .or(OPERATION_LOG.SHORT_ID.like(operationLogExportQuery.getUserNameOrShortId())));
        }
        if(ObjectUtil.isNotEmpty(operationLogExportQuery.getLimit())){
            query.limit(operationLogExportQuery.getLimit());
        }
        return operationLogGateway.listAs(query, OperationLogDO.class);
    }

    @Override
    public Page<OperationLogDO> getPage(PageQueryCondition<OperationLogDO> pageQuery, TimeQueryInterval createTimeQueryInterval) {
        QueryWrapper query = QueryWrapper.create();
        query.select(OPERATION_LOG.ALL_COLUMNS)
                .from(OPERATION_LOG.getTableName())
                .where(OPERATION_LOG.IS_DELETE.eq(false))
                .orderBy(pageQuery.getOrderBy(), CommonConstants.ASC.equals(pageQuery.getOrderDirection()));
        if (Objects.nonNull(createTimeQueryInterval) && Objects.nonNull(createTimeQueryInterval.getStartTime()) && Objects.nonNull(createTimeQueryInterval.getEndTime())) {
            query.and(OPERATION_LOG.CREATE_TIME.ge(createTimeQueryInterval.getStartTime()))
                    .and(OPERATION_LOG.CREATE_TIME.le(createTimeQueryInterval.getEndTime()));
        }
        OperationLogDO condition=pageQuery.getCondition();
        if (Objects.nonNull(condition)&&Objects.nonNull(condition.getState())) {
            query.and(OPERATION_LOG.STATE.eq(condition.getState()));
        }
        if (StringUtils.isNotEmpty(pageQuery.getUserNameOrShortId())) {
            query.and(OPERATION_LOG.USER_NAME.like(pageQuery.getUserNameOrShortId())
                    .or(OPERATION_LOG.SHORT_ID.like(pageQuery.getUserNameOrShortId())));
        }
        Page<OperationLogDO> page = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        return operationLogGateway.pageAs(page, query, OperationLogDO.class);
    }

    @Override
    public void exportLogs(OperationLogExportQuery operationLogExportQuery, HttpServletResponse response) {
        List<OperationLogDO> result=list(operationLogExportQuery);
        response.setHeader("Content-Disposition", "attachment; filename=export.xlsx");
        response.setContentType("application/vnd.ms-excel");
        try (OutputStream out = response.getOutputStream()) {
            List<OperationLogExportVO> list = OperationLogDO.buildToExportVO(result);
            EasyExcel.write(out, OperationLogExportVO.class).sheet("日志列表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
