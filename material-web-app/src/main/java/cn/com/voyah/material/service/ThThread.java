package cn.com.voyah.material.service;

import cn.com.voyah.material.client.FileService;
import cn.com.voyah.material.client.HistoryTransferService;
import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.gateway.FileDetailGateway;
import cn.com.voyah.material.util.MaterialFileUtils;
import cn.com.voyah.material.util.SpireThumbnailUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
public class ThThread  implements Runnable{

    private FileDetailDO fileDetailDO;
    private HistoryTransferService historyTransferService;
    private FileDetailGateway fileDetailGateway;
    private String dirPrefixOrigin;
    private String dirPrefixTh;
    private FileService fileService;
    ThThread(HistoryTransferService historyTransferService, FileService fileService,FileDetailGateway fileDetailGateway, FileDetailDO fileDetailDO,String dirPrefixOrigin,String dirPrefixTh){
        this.fileDetailDO = fileDetailDO;
        this.historyTransferService = historyTransferService;
        this.fileService=fileService;
        this.fileDetailGateway = fileDetailGateway;
        this.dirPrefixOrigin=dirPrefixOrigin;
        this.dirPrefixTh=dirPrefixTh;
    }
    @Override
    public void run() {
        Long timeStamp=System.currentTimeMillis();
        String fileName=fileDetailDO.getFilename();
        String originFileName=fileDetailDO.getOriginalFilename();
        String thOriginFileName= MaterialFileUtils.generateThFileName(originFileName,timeStamp);
        String thFileName=MaterialFileUtils.generateThFileName(fileName,timeStamp);
        Long fileId=fileDetailDO.getId();
        try{
            historyTransferService.downloadFile(fileDetailDO.getFilename(),dirPrefixOrigin+originFileName);
        }catch(Exception e){
            log.error(e.getMessage());
            log.warn("generete-th ====fail=====method=downloadFile======fileId{}===========================",fileId);
            return ;
        }
        int tryCount=3;
        int i=0;
        boolean generateImgSuccess=false;
        while(!generateImgSuccess&&i<tryCount){
            try{
                SpireThumbnailUtil.generateThumbnail(dirPrefixOrigin+originFileName, dirPrefixTh+thOriginFileName);
                generateImgSuccess=true;
            }catch(Exception e){
                log.error(e.getMessage());
                generateImgSuccess=false;
                i++;
            }
        }
        if(!generateImgSuccess){
            log.warn("generete-th ====fail======tryCount{}========fileId{}========================",tryCount,fileId);
            return ;
        }
        int  uploadTryCount=3;
        int uploadCount=0;
        boolean uploadImgSuccess=false;
        while(!uploadImgSuccess&&uploadCount<uploadTryCount) {
            try {
                fileService.upload(dirPrefixTh + thOriginFileName, thFileName);
                uploadImgSuccess = true;
            } catch (Exception e) {
                log.error(e.getMessage());
                uploadImgSuccess = false;
                uploadCount++;
            }
        }
        log.info("generete-th ====uploadImg {}===fileId{}============",uploadImgSuccess?"SUCCESS":"FAIL",fileId);
        if(!uploadImgSuccess){
            return ;
        }
        Long size=0L;
        try{
            size=historyTransferService.getFileSize(dirPrefixTh+thOriginFileName);
            historyTransferService.deleteLocalFile(dirPrefixOrigin+originFileName);
            historyTransferService.deleteLocalFile(dirPrefixTh+thOriginFileName);
        }catch (Exception e){

        }
        fileDetailDO.setThFilename(thFileName);
        String thUrl=fileService.getObjectUrl(thFileName);
        fileDetailDO.setThUrl(thUrl);
        fileDetailDO.setThContentType("jpeg");
        fileDetailDO.setThSize((long)size);
        fileDetailGateway.updateThById(fileDetailDO);
        log.info("generete-th ====finish success=====fileId{}==========",fileId);
    }
}
