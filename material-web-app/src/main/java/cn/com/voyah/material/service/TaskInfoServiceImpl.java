package cn.com.voyah.material.service;

import cn.com.voyah.material.client.TaskInfoService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.constants.TaskStatus;
import cn.com.voyah.material.domain.entity.TaskInfoDO;
import cn.com.voyah.material.domain.gateway.MaterialAuthGateway;
import cn.com.voyah.material.domain.gateway.TaskInfoGateway;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.pojo.MaterialAuthPO;
import cn.com.voyah.material.pojo.TaskInfoPO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.properties.IncInnerUserInfo;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import static cn.com.voyah.domain.entity.def.TaskInfoDef.TASK_INFO;

@Service
public class TaskInfoServiceImpl implements TaskInfoService {

    @Autowired
    private TaskInfoGateway taskInfoGateway;

    @Autowired
    private MaterialAuthGateway materialAuthGateway;
    @Value("${task.max.file.count:500}")
    private  int maxFileCount;
    @Override
    public Collection<TaskInfoDO> list(TaskInfoDO taskInfoDO) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TASK_INFO.ALL_COLUMNS)
                .from(TASK_INFO.getTableName())
                .where(TASK_INFO.IS_DELETE.eq(false))
                // 主文件夹不展示
                .orderBy(TASK_INFO.CREATE_TIME.getName(),false);
        if(ObjectUtil.isNotEmpty(taskInfoDO.getTaskStatus())){
            query.and(TASK_INFO.TASK_STATUS.eq(taskInfoDO.getTaskStatus()));
        }
        return taskInfoGateway.listAs(query, TaskInfoDO.class);
    }

    @Override
    public Page<TaskInfoDO> getPage(PageQueryCondition<TaskInfoDO> pageQuery,TimeQueryInterval createTimeQueryInterval) {
        TaskInfoDO condition = pageQuery.getCondition();
        QueryWrapper query = QueryWrapper.create();
        query.select(TASK_INFO.ALL_COLUMNS)
                .from(TASK_INFO.getTableName())
                .where(TASK_INFO.IS_DELETE.eq(false))
                .orderBy(pageQuery.getOrderBy(), CommonConstants.ASC.equals(pageQuery.getOrderDirection()));
        if(Objects.nonNull(condition.getTaskStatus())){
            query.and(TASK_INFO.TASK_STATUS.eq(condition.getTaskStatus()));
        }
        if(!StringUtils.isEmpty(condition.getTaskName())){
            query.and(TASK_INFO.TASK_NAME.like(condition.getTaskName()));
        }
        if(!StringUtils.isEmpty(condition.getExecuteBy())){
            query.and(TASK_INFO.EXECUTE_BY.like(condition.getExecuteBy()));
        }
        if(!StringUtils.isEmpty(condition.getDispatchBy())){
            query.and(TASK_INFO.DISPATCH_BY.like(condition.getDispatchBy()));
        }
//        if (org.apache.commons.lang.StringUtils.isNotEmpty(pageQuery.getUserNameOrShortId())) {
//            query.and(TASK_INFO.CREATE_BY.like(pageQuery.getUserNameOrShortId())
//                    .or(TASK_INFO.CREATE_SHORT_ID.like(pageQuery.getUserNameOrShortId())));
//        }
        // 添加时间查询
        if (Objects.nonNull(createTimeQueryInterval)&&Objects.nonNull(createTimeQueryInterval.getStartTime()) && Objects.nonNull(createTimeQueryInterval.getEndTime())) {
            query.and(TASK_INFO.CREATE_TIME.ge(createTimeQueryInterval.getStartTime()))
                    .and(TASK_INFO.CREATE_TIME.le(createTimeQueryInterval.getEndTime()));
        }
        Page<TaskInfoDO> page = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        return taskInfoGateway.pageAs(page, query, TaskInfoDO.class);
    }

    @Override
    public boolean updateStatus(Long id, TaskStatus status,LocalDateTime updateTime) {
        if(ObjectUtil.isEmpty(id)||ObjectUtil.isEmpty(status)){
            return false;
        }
        return taskInfoGateway.updateTaskStatus(id,status,updateTime);
    }

    @Override
    public TaskInfoDO save(TaskInfoDO taskInfoDO) {
        IncInnerUserInfo userInfo= IncUserThreadLocal.get();
        taskInfoDO.setDispatchBy(userInfo.getUserName());
        taskInfoDO.setDispatchShortId(userInfo.getName());
        taskInfoDO.setTaskStatus(TaskStatus.PROGRESS.getValue());
        taskInfoDO.setTaskStartTime(LocalDateTime.now());
        checkValid(taskInfoDO);
        MaterialAuthPO auth=materialAuthGateway.getAuth(taskInfoDO.getExecuteShortId(),taskInfoDO.getMaterialFoldId());
        if(ObjectUtil.isEmpty(auth)){
            throw new BizException("执行人没有该素材库权限!");
        }
        taskInfoDO.setExecuteBy(auth.getUserName());
        List<TaskInfoPO> taskList=taskInfoGateway.getProcessList(taskInfoDO.getExecuteShortId(),taskInfoDO.getMaterialFoldId(),LocalDateTime.now());
        if(CollectionUtil.isNotEmpty(taskList)){
            throw new BizException("已存在相同时间段任务，请修改任务信息!");
        }
        return taskInfoGateway.save(taskInfoDO);
    }

    @Override
    public void taskFileCountAdd(String shortId, Long materialFoldId) {
        LocalDateTime now=LocalDateTime.now();
        List<TaskInfoPO> taskList=taskInfoGateway.getProcessList(shortId,materialFoldId,LocalDateTime.now());
        if(CollectionUtil.isNotEmpty(taskList)){
            TaskInfoPO taskInfoPO=taskList.get(0);
            if(!TaskStatus.PROGRESS.getValue().equals(taskInfoPO.getTaskStatus())){
                return ;
            }
            Integer actualFileCount=(ObjectUtil.isEmpty(taskInfoPO.getActualFileCount())?0:(taskInfoPO.getActualFileCount()+1));
            taskInfoGateway.updateFileCount(taskInfoPO.getId(),ObjectUtil.isEmpty(taskInfoPO.getActualFileCount())?0:(taskInfoPO.getActualFileCount()+1));
            if(actualFileCount.equals(taskInfoPO.getMaterialFileCount())){
                taskInfoGateway.updateTaskStatus(taskInfoPO.getId(),TaskStatus.COMPLETE,now);
            }
        }
    }

    @Override
    public boolean updateStatusBatch(List<Long> ids, TaskStatus status) {
        return taskInfoGateway.updateStatusBatch(ids,status);
    }

    private void checkValid(TaskInfoDO taskInfoDO){
        if(ObjectUtil.isEmpty(taskInfoDO.getMaterialFileCount())){
            throw new BizException("素材数量不能为空！");
        }
        if(taskInfoDO.getMaterialFileCount()<1){
            throw new BizException("素材数量必须大于0！");
        }
        if(taskInfoDO.getMaterialFileCount()>maxFileCount){
            throw new BizException("素材数量必须小于"+maxFileCount+"!");
        }
        if(ObjectUtil.isEmpty(taskInfoDO.getMaterialFoldId())){
            throw new BizException("素材库不能为空!");
        }
        if(ObjectUtil.isEmpty(taskInfoDO.getTaskEndTime())){
            throw new BizException("截止时间不能为空!");
        }
        LocalDateTime taskEndTime=taskInfoDO.getTaskEndTime();
        if(taskEndTime.isBefore(LocalDateTime.now())){
            throw new BizException("截止时间不能早于当前时间!");
        }
    }

}
