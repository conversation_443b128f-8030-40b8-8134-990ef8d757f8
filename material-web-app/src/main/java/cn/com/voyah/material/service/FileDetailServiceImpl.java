package cn.com.voyah.material.service;

import cn.com.voyah.material.client.FileDetailService;
import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.gateway.FileDetailGateway;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;

@Service
public class FileDetailServiceImpl implements FileDetailService {

    @Autowired
    private FileDetailGateway fileDetailGateway;
    @Override
    public FileDetailDO save(FileDetailDO fileDetailDO) {

        return fileDetailGateway.save(fileDetailDO);
    }

    @Override
    public Boolean removeById(Long id) {
        return fileDetailGateway.removeById(id);
    }


    @Override
    public FileDetailDO getOne(FileDetailDO fileDetailDO) {
        return fileDetailGateway.getOneByEntity(fileDetailDO);
    }

    @Override
    public void updateThById(FileDetailDO fileDetailDO) {
        fileDetailGateway.updateThById(fileDetailDO);
    }


}
