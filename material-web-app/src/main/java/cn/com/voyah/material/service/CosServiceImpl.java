package cn.com.voyah.material.service;

import cn.com.voyah.material.client.FileService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.config.CosConfig;
import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.entity.FilePartDO;
import cn.com.voyah.material.domain.entity.dto.ResponseDTO;
import cn.com.voyah.material.domain.entity.migrate.MigrateEntityDO;
import cn.com.voyah.material.util.JsonUtil;
import cn.com.voyah.material.utils.RedisKeys;
import cn.com.voyah.material.utils.RedisUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.BasicSessionCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.Download;
import com.qcloud.cos.transfer.TransferManager;
import com.tencent.cloud.CosStsClient;
import com.tencent.cloud.Response;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@Service("cosService")
@Slf4j
public class CosServiceImpl implements FileService {
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private CosConfig cosConfig;

    @Resource
    private RedisKeys redisKeys;



    private Response getCredential() {
        TreeMap<String, Object> config = new TreeMap<>();

        // 云 api 密钥 SecretId
        config.put("secretId", cosConfig.getSecretId());
        // 云 api 密钥 SecretKey
        config.put("secretKey", cosConfig.getSecretKey());
        // 设置域名,可通过此方式设置内网域名
        //config.put("host", "sts.internal.tencentcloudapi.com");
        // 临时密钥有效时长，单位是秒
        config.put("durationSeconds", cosConfig.getDuration());
        // 换成你的 bucket
        config.put("bucket", cosConfig.getBucket());
        // 换成 bucket 所在地区
        config.put("region", cosConfig.getRegion());
        // 可以通过 allowPrefixes 指定前缀数组, 例子： a.jpg 或者 a/* 或者 * (使用通配符*存在重大安全风险, 请谨慎评估使用)
        config.put("allowPrefixes", new String[]{"*"});
        // 密钥的权限列表。简单上传和分片需要以下的权限，其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
        String[] allowActions = new String[]{
                //简单上传操作
                "name/cos:PutObject",
                //表单上传对象
                "name/cos:PostObject",
                //获取对象
                "name/cos:GetBucket",
                //分块上传：初始化分块操作
                "name/cos:InitiateMultipartUpload",
                //分块上传：List 进行中的分块上传
                "name/cos:ListMultipartUploads",
                //分块上传：List 已上传分块操作
                "name/cos:ListParts",
                //分块上传：上传分块操作
                "name/cos:UploadPart",
                //分块上传：完成所有分块上传操作
                "name/cos:CompleteMultipartUpload",
                //取消分块上传操作
                "name/cos:AbortMultipartUpload",
                //检索存储桶及其权限
                "name/cos:HeadBucket",
                //删除对象
                "name/cos:DeleteBucket",
                "name/cos:GetObject",
                "name/cos:HeadObject",
                "name/cos:OptionsObject"

        };
        config.put("allowActions", allowActions);
        Response response = null;
        try {
            response = CosStsClient.getCredential(config);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return response;
    }


    @Override
    public ResponseDTO getFederationToken() {
        /* 如果是循环调用下面举例的接口，需要从此处开始你的循环语句。切记！ */
        ResponseDTO ret = null;
        Response response;
        String key = redisKeys.getCosCredential();
        String value = redisUtil.get(key);
        if (value != null) {
            response = JsonUtil.readValue(value, Response.class);
        } else {
            response = getCredential();
            if (response != null) {
                long duration = Math.max(0, response.expiredTime - System.currentTimeMillis() / 1000);
                String msg = JsonUtil.writeValueAsString(response);
                log.info("key:{},duration:{} seconds,response:{}", key, duration, msg);
                redisUtil.set(key, msg, duration);
            }
        }
        if (response != null) {
            ret = ResponseDTO.from(cosConfig, response,cosConfig.getPrefix());
        }
        return ret;

    }


    public COSClient getCosClient() {
        String secretId = cosConfig.getSecretId();
        String secretKey = cosConfig.getSecretKey();
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        Region region = new Region(cosConfig.getRegion());
        ClientConfig clientConfig = new ClientConfig(region);
        clientConfig.setHttpProtocol(HttpProtocol.https);
        return new COSClient(cred, clientConfig);
    }

    @Override
    public String getObjectUrl(String fileName) {
        COSClient cosClient = getCosClient();
        URL url=null;
        if(cosConfig.isUrlPresigned()){
            GeneratePresignedUrlRequest request =
                    new GeneratePresignedUrlRequest(cosConfig.getBucket(),
                            fileName, HttpMethodName.GET);
            Date expirationDate = new DateTime().plusMinutes(15).toDate();
            request.setExpiration(expirationDate);
            url = cosClient.generatePresignedUrl(request);
        }else{
            url = cosClient.getObjectUrl(cosConfig.getBucket(),fileName);
        }
        cosClient.shutdown();
        return url.toString();
    }

    @Override
    public FileDetailDO sliceUploadStart(MultipartFile file, String newFileName) {
        String contentType=file.getContentType();
        String fileName=file.getOriginalFilename();
        long size=file.getSize();
        InitiateMultipartUploadRequest initiateRequest = new InitiateMultipartUploadRequest(cosConfig.getBucket(), newFileName);
        InitiateMultipartUploadResult initiateResponse = getCosClient().initiateMultipartUpload(initiateRequest);
        String uploadId = initiateResponse.getUploadId();
        FileDetailDO fileDetailDO = new FileDetailDO();
        fileDetailDO.setContentType(contentType);
        fileDetailDO.setOriginalFilename(fileName);
        fileDetailDO.setFilename(newFileName);
        fileDetailDO.setSize(size);
//        fileDetailDO.setTag(putObjectResult.getETag());
        fileDetailDO.setUrl(cosConfig.getUrl() + newFileName);
        fileDetailDO.setUploadId(uploadId);
        fileDetailDO.setUploadStatus(1);
        return fileDetailDO;
    }

    @Override
    public FilePartDO sliceUploading(MultipartFile file,String uploadId,String key,int partNum) throws IOException {
        long fileSize = file.getSize();
        long partSize = 1024 * 1024; // 每块5MB
        // 分块上传
        long startPos = (partNum-1) * partSize;
        long currentPartSize = Math.min(partSize, fileSize - startPos);
        UploadPartRequest uploadPartRequest = new UploadPartRequest()
                .withBucketName(cosConfig.getBucket())
                .withKey(key)
                .withUploadId(uploadId)
                .withPartNumber(partNum)
                .withInputStream(file.getInputStream())
                .withFileOffset(startPos)
                .withPartSize(currentPartSize);
        // 上传分块
        PartETag partETag = getCosClient().uploadPart(uploadPartRequest).getPartETag();
        FilePartDO filePartDO=new FilePartDO();
        filePartDO.setPartNumber(partNum);
        filePartDO.setPartSize(currentPartSize);
        filePartDO.setUploadId(uploadId);
        filePartDO.setETag(partETag.getETag());
        return filePartDO;
    }

    @Override
    public void sliceUploadFinish(String key,String uploadId,List<FilePartDO> parts) {
        // 完成上传
        List<PartETag> partETags=parts.stream()
                .map(each->{return new PartETag(each.getPartNumber(),each.getETag());}).collect(Collectors.toList());
        CompleteMultipartUploadRequest completeRequest = new CompleteMultipartUploadRequest(cosConfig.getBucket(), key, uploadId, partETags);
        getCosClient().completeMultipartUpload(completeRequest);
    }

    @Override
    public FileDetailDO generateThFile(FileDetailDO originFileDetailDO) {
        return null;
    }



    @Override
    public FileDetailDO upload(MultipartFile file, String newFileName) {
        ResponseDTO responseDTO = getFederationToken();
        BasicSessionCredentials cred = new BasicSessionCredentials(responseDTO.getSecretId(), responseDTO.getSecretKey(),
                responseDTO.getSessionToken());
        ClientConfig clientConfig = new ClientConfig(new Region(responseDTO.getRegion()));
        // 生成cos客户端对象
        COSClient cosClient = new COSClient(cred, clientConfig);
        // 上传的bucket名字
        String bucketName = responseDTO.getBucket();
        // 上传object, 建议20M以下的文件使用该接口
        long size = file.getSize();
        String contentType = file.getContentType();
        String fileName = file.getOriginalFilename();
        ObjectMetadata objectMetadata = new ObjectMetadata();
        // 上传
        InputStream input = null;
        try {
            input = file.getInputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, newFileName, input, objectMetadata);
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
        System.out.println(putObjectResult.getMetadata());
        //        return putObjectResult.getMetadata();
//        String contentMd5=putObjectResult.getContentMd5();
        FileDetailDO fileDetailDO = new FileDetailDO();
        fileDetailDO.setContentType(contentType);
        fileDetailDO.setOriginalFilename(fileName);
        fileDetailDO.setFilename(newFileName);
        fileDetailDO.setSize(size);
        fileDetailDO.setTag(putObjectResult.getETag());
        fileDetailDO.setUrl(cosConfig.getUrl() + newFileName);
        return fileDetailDO;
    }

    @Override
    public Collection<MigrateEntityDO> getObjectLists(String dir) {
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
        // 设置bucket名称
        listObjectsRequest.setBucketName(cosConfig.getBucket());
        // prefix表示列出的object的key以prefix开始
        String keyPrefix=cosConfig.getPrefix() +dir+CommonConstants.DIR_SEPRATE;
        listObjectsRequest.setPrefix(keyPrefix);
        // 设置最大遍历出多少个对象, 一次listobject最大支持1000
        listObjectsRequest.setMaxKeys(1000);
//         listObjectsRequest.setDelimiter("/");
        ObjectListing objectListing = null;
        Map<String,MigrateEntityDO> mapMigrateEntity=new HashMap<String,MigrateEntityDO>();
        do {

            try {
                objectListing = getCosClient().listObjects(listObjectsRequest);
            } catch (CosServiceException e) {
                e.printStackTrace();
                return Collections.emptyList();
            } catch (CosClientException e) {
                e.printStackTrace();
                return Collections.emptyList();
            }
            // common prefix表示表示被delimiter截断的路径, 如delimter设置为/, common prefix则表示所有子目录的路径
            List<String> commonPrefixs = objectListing.getCommonPrefixes();

            // object summary表示所有列出的object列表
            List<COSObjectSummary> cosObjectSummaries = objectListing.getObjectSummaries();

            int startIndex=2;
            Long parentLevel0=226959331107078144L;
            for (COSObjectSummary cosObjectSummary : cosObjectSummaries) {
                //prod/history/1218/1218-2/10213.jpg
                // 文件的路径key
                String key = cosObjectSummary.getKey();
//                String subKey=key.substring();
                String[] strs=key.split(CommonConstants.DIR_SEPRATE_STR);
                int dirCount=strs.length;
                int level=1;
                Long tmpParentId=parentLevel0;
                MigrateEntityDO tmpMigrateEntity=null;
                List<Long> tmpParentIdList=new ArrayList<>();
                tmpParentIdList.add(0L);
                tmpParentIdList.add(parentLevel0);
                //文件夹处理
                for(int i=startIndex;i<(dirCount-1);i++){
                    String tmpKey=getPrefixKey(strs,i);
                    if(mapMigrateEntity.containsKey(tmpKey)){
                        tmpMigrateEntity=mapMigrateEntity.get(tmpKey);
                    }else{
                        MigrateEntityDO migrateEntity=new MigrateEntityDO();
                        migrateEntity.setPath(tmpKey);
                        Long materialId= IdUtil.getSnowflakeNextId();
                        migrateEntity.setMaterialId(materialId);
                        migrateEntity.setIsDir(true);
                        migrateEntity.setMaterialName(strs[i]);
                        migrateEntity.setParentId(tmpParentId);
                        migrateEntity.setLevel(level);
                        List<Long> tmpParentIdList2=new ArrayList<>();
                        tmpParentIdList2.addAll(tmpParentIdList);
                        migrateEntity.setParentIdList(tmpParentIdList2);
//                        System.out.println("tmpMigrateEntity:"+ JSONUtil.toJsonStr(tmpMigrateEntity));
                        mapMigrateEntity.put(tmpKey,migrateEntity);
                        tmpMigrateEntity=migrateEntity;
                    }
                    tmpParentIdList.add(tmpMigrateEntity.getMaterialId());
                    tmpParentId=tmpMigrateEntity.getMaterialId();
                    level++;
                }
                MigrateEntityDO migrateEntity=new MigrateEntityDO();
                migrateEntity.setPath(key);
                Long materialId= IdUtil.getSnowflakeNextId();
                migrateEntity.setMaterialId(materialId);
                migrateEntity.setMaterialName(strs[dirCount-1]);
                migrateEntity.setParentId(tmpParentId);
                migrateEntity.setParentIdList(tmpParentIdList);
                migrateEntity.setLevel(level);
                if(key.endsWith(CommonConstants.DIR_SEPRATE_STR)){
                    migrateEntity.setIsDir(true);
                }else{
                    migrateEntity.setIsDir(false);
                    try {
                        long fileSize = cosObjectSummary.getSize();
                        migrateEntity.setFileId(materialId);
                        migrateEntity.setSize(fileSize);
                        migrateEntity.setFilename(key);
                        migrateEntity.setOriginFlename(strs[dirCount-1]);
                        String contentType=getFileContentType(key);
                        migrateEntity.setContentType(contentType);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
                mapMigrateEntity.put(key,migrateEntity);
//                System.out.println("migrateEntity:"+ JSONUtil.toJsonStr(migrateEntity));
////                //一共有多少个
////                // 文件的etag
////                String etag = cosObjectSummary.getETag();
////                // 文件的长度
////
////                // 文件的存储类型
////                String storageClasses = cosObjectSummary.getStorageClass();
//                System.out.println("key:"+key);
//                System.out.println("cosObjectSummary:"+ JSONUtil.toJsonStr(cosObjectSummary));

            }
            String nextMarker = objectListing.getNextMarker();
            listObjectsRequest.setMarker(nextMarker);
        } while (objectListing.isTruncated());
        System.out.println("end======");
        mapMigrateEntity.remove(keyPrefix);
        return mapMigrateEntity.values();
    }

    @Override
    public void upload(String fileDir, String newFileName) {
        File file=new File(fileDir);
        ResponseDTO responseDTO = getFederationToken();
        BasicSessionCredentials cred = new BasicSessionCredentials(responseDTO.getSecretId(), responseDTO.getSecretKey(),
                responseDTO.getSessionToken());
        ClientConfig clientConfig = new ClientConfig(new Region(responseDTO.getRegion()));
        // 生成cos客户端对象
        COSClient cosClient = new COSClient(cred, clientConfig);
        // 上传的bucket名字
        String bucketName = responseDTO.getBucket();
        // 上传object, 建议20M以下的文件使用该接口

        ObjectMetadata objectMetadata = new ObjectMetadata();
        // 上传
        InputStream input = null;
        try {
            input =new FileInputStream(file);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, newFileName, input, objectMetadata);
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
        try {
            input.close();
        } catch (IOException e) {

        }

    }

    public String getFileContentType(String key) throws CosServiceException, FileNotFoundException, IOException{
        GetObjectMetadataRequest getObjectMetadataRequest = new GetObjectMetadataRequest(cosConfig.getBucket(), key);
        ObjectMetadata objectMetadata = getCosClient().getObjectMetadata(getObjectMetadataRequest);
        // 获取内容类型
        return objectMetadata.getContentType();
    }
    public String getPrefixKey(String[] strs,int size){
        StringBuffer sb=new StringBuffer();
        for (int i=0;i<=size;i++) {
            sb.append(strs[i]).append(CommonConstants.DIR_SEPRATE_STR);
        }
        return sb.toString();

    }

    @Override
    public void downloadFileTmpKey(String key, String tmpPathName) throws IOException {
        ResponseDTO responseDTO = getFederationToken();
        BasicSessionCredentials cred = new BasicSessionCredentials(responseDTO.getSecretId(), responseDTO.getSecretKey(),
                responseDTO.getSessionToken());
        ClientConfig clientConfig = new ClientConfig(new Region(responseDTO.getRegion()));
        // 生成cos客户端对象
        COSClient cosClient = new COSClient(cred, clientConfig);
        // 上传的bucket名字
        String bucketName = responseDTO.getBucket();
        // 上传object, 建议20M以下的文件使用该接口

        try {
            GetObjectRequest getObjectRequest = new GetObjectRequest(cosConfig.getBucket(), key);
            // 限流使用的单位是 bit/s, 这里设置下载带宽限制为10MB/s
            getObjectRequest.setTrafficLimit(80*1024*1024);
            COSObject cosObject = cosClient.getObject(getObjectRequest);
            COSObjectInputStream cosObjectInput = cosObject.getObjectContent();
            // 下载对象的 CRC64
            String crc64Ecma = cosObject.getObjectMetadata().getCrc64Ecma();
            // 关闭输入流
            cosObjectInput.close();
            // 方法2 下载文件到本地
            File downFile = new File(tmpPathName);
            getObjectRequest = new GetObjectRequest(bucketName, key);
            ObjectMetadata downObjectMeta = cosClient.getObject(getObjectRequest, downFile);

        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        }
    }

}
