package cn.com.voyah.material.service;


import cn.com.voyah.material.client.HistoryTransferService;
import cn.com.voyah.material.domain.config.CosConfig;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.utils.RedisKeys;
import cn.com.voyah.material.utils.RedisUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.GetObjectRequest;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.Download;
import com.qcloud.cos.transfer.TransferManager;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class HistoryTransferServiceImpl implements HistoryTransferService {
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private CosConfig cosConfig;

    @Resource
    private RedisKeys redisKeys;

    private TransferManager createTransferManager() {
        // 1 初始化用户身份信息(secretId, secretKey)
        COSCredentials cred = new BasicCOSCredentials(cosConfig.getSecretId(), cosConfig.getSecretKey());
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(cosConfig.getRegion()));
        clientConfig.setHttpProtocol(HttpProtocol.https);
        // 3 生成cos客户端
        COSClient cosclient = new COSClient(cred, clientConfig);

        ExecutorService threadPool = Executors.newFixedThreadPool(32);
        // 传入一个threadpool, 若不传入线程池, 默认TransferManager中会生成一个单线程的线程池。
        TransferManager transferManager = new TransferManager(cosclient, threadPool);
        return transferManager;
    }
    @Override
    public void downloadFile(String key,String tmpPathName) {
        TransferManager transferManager = createTransferManager();
        File downloadFile = new File(tmpPathName);
        GetObjectRequest getObjectRequest = new GetObjectRequest(cosConfig.getBucket(), key);
        try {
            // 返回一个异步结果copy, 可同步的调用waitForCompletion等待download结束, 成功返回void, 失败抛出异常.
            Download download = transferManager.download(getObjectRequest, downloadFile);
            download.waitForCompletion();
        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        transferManager.shutdownNow();
    }

    @Override
    public void deleteLocalFile(String tmpPathName) {
        File file = new File(tmpPathName); // 替换为您要删除的文件路径
        if (file.exists()) {
            if (file.isFile()) {
                boolean success = file.delete();
                if (success) {
                    System.out.println("文件删除成功");
                } else {
                    System.out.println("文件删除失败");
                }
            } else {
                System.out.println("这不是一个文件，无法删除");
            }
        } else {
            System.out.println("文件不存在");
        }
    }

    @Override
    public long getFileSize(String tmpPathName) {

        File file = new File(tmpPathName);

        if (file.exists() && !file.isDirectory()) {
            // 文件存在且不是目录
            long fileSize = file.length();
           return fileSize;
        } else {
            throw new BizException("文件不存在!");
        }
    }

    @Override
    public void downloadFileTmpKey(String key, String tmpPathName) {
        TransferManager transferManager = createTransferManager();
        File downloadFile = new File(tmpPathName);
        GetObjectRequest getObjectRequest = new GetObjectRequest(cosConfig.getBucket(), key);
        try {
            // 返回一个异步结果copy, 可同步的调用waitForCompletion等待download结束, 成功返回void, 失败抛出异常.
            Download download = transferManager.download(getObjectRequest, downloadFile);
            download.waitForCompletion();
        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        transferManager.shutdownNow();
    }


}
