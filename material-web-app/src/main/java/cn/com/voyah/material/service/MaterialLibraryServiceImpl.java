package cn.com.voyah.material.service;

import cn.com.voyah.material.api.MaterialLibraryService;
import cn.com.voyah.material.client.OriginMaterialService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.convertor.MaterialLibraryCoConvert;
import cn.com.voyah.material.domain.entity.MaterialLibraryDO;
import cn.com.voyah.material.domain.gateway.MaterialAuthGateway;
import cn.com.voyah.material.domain.gateway.MaterialLibraryGateway;
import cn.com.voyah.material.dto.OriginMaterialExtensionDTO;
import cn.com.voyah.material.dto.library.MaterialLibraryAdd;
import cn.com.voyah.material.dto.library.MaterialLibraryQuery;
import cn.com.voyah.material.dto.library.MaterialLibrarySortChange;
import cn.com.voyah.material.dto.library.MaterialLibraryUpdate;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.util.BoolUtil;
import cn.com.voyah.material.util.PageUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.com.voyah.domain.entity.def.MaterialLibraryDef.MATERIAL_LIBRARY;

@Component
@AllArgsConstructor
public class MaterialLibraryServiceImpl implements MaterialLibraryService {

    private MaterialLibraryCoConvert materialLibraryCoConvert;

    private MaterialLibraryGateway materialLibraryGateway;

    private MaterialAuthGateway materialAuthGateway;

    private OriginMaterialService originMaterialService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialLibraryDO saveLibrary(MaterialLibraryAdd add) {
        MaterialLibraryDO libraryDO = materialLibraryCoConvert.convert(add);
        OriginMaterialExtensionDTO extensionDTO = new OriginMaterialExtensionDTO();
        extensionDTO.setIsPublic(1);
        extensionDTO.setParentId(0L);
        extensionDTO.setIsShow(CommonConstants.ENABLE);
        List<OriginMaterialExtensionDTO> originMaterialList = originMaterialService.getList(extensionDTO, true);
        List<String> idList = originMaterialList.stream().map(OriginMaterialExtensionDTO::getId).map(Object::toString).collect(Collectors.toList());
        // 启用状态 , 新增的默认启用
        libraryDO.setStatus(BoolUtil.toInt(true));
        MaterialLibraryDO res = materialLibraryGateway.save(libraryDO);
        materialAuthGateway.updateAllAuth(idList,res.getOriginMaterialId());
        return res;
    }

    @Override
    public Boolean updateLibrary(MaterialLibraryUpdate update) {
        MaterialLibraryDO libraryDO = materialLibraryCoConvert.convert(update);

        return materialLibraryGateway.updateById(libraryDO);
    }

    @Override
    public Page<MaterialLibraryDO> page(PageQueryCondition<MaterialLibraryQuery> condition) {
        MaterialLibraryQuery query = condition.getCondition();

        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.from(MATERIAL_LIBRARY.getTableName())
                .where(MATERIAL_LIBRARY.LIBRARY_NAME.like(query.getLibraryName(), StringUtils::hasLength))
                .and(MATERIAL_LIBRARY.STATUS.eq(query.getStatus(), Objects::nonNull))
                .and(MATERIAL_LIBRARY.IS_PUBLIC.eq(query.getIsPublic(), Objects::nonNull))
                .orderBy(MATERIAL_LIBRARY.SORT.desc());
        return materialLibraryGateway.page(PageUtil.of(condition), queryWrapper);
    }

    @Override
    public void sortChange(MaterialLibrarySortChange sortChange) {
        MaterialLibraryDO one = new MaterialLibraryDO()
                .setId(sortChange.getOneId())
                .setSort(sortChange.getTowSort());

        MaterialLibraryDO tow = new MaterialLibraryDO()
                .setId(sortChange.getTowId())
                .setSort(sortChange.getOneSort());
        materialLibraryGateway.updateSort(List.of(one, tow));
    }
}
