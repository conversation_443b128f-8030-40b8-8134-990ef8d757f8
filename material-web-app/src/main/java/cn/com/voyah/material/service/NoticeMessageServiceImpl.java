package cn.com.voyah.material.service;

import cn.com.voyah.material.client.NoticeMessageService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.NoticeMessageDO;
import cn.com.voyah.material.domain.gateway.NoticeMessageGateway;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static cn.com.voyah.domain.entity.def.NoticeMessageDef.NOTICE_MESSAGE;
import static cn.com.voyah.domain.entity.def.OperationLogDef.OPERATION_LOG;

@Service
public class NoticeMessageServiceImpl implements NoticeMessageService {
    @Autowired
    private NoticeMessageGateway noticeMessageGateway;

    @Override
    public Collection<NoticeMessageDO> list(NoticeMessageDO noticeMessageDO) {
        QueryWrapper query = QueryWrapper.create();
        query.select(NOTICE_MESSAGE.ALL_COLUMNS)
                .from(NOTICE_MESSAGE.getTableName())
                .where(NOTICE_MESSAGE.IS_DELETE.eq(false))
                // 主文件夹不展示
                .orderBy(NOTICE_MESSAGE.CREATE_TIME.getName(),false);
        if(ObjectUtil.isNotEmpty(noticeMessageDO.getRelationId())){
            query.and(NOTICE_MESSAGE.RELATION_ID.eq(noticeMessageDO.getRelationId()));
        }
        if(ObjectUtil.isNotEmpty(noticeMessageDO.getType())){
            query.and(NOTICE_MESSAGE.TYPE.eq(noticeMessageDO.getType()));
        }
        return noticeMessageGateway.listAs(query,NoticeMessageDO.class);
    }

    @Override
    public Page<NoticeMessageDO> getPage(PageQueryCondition<NoticeMessageDO> pageQuery,TimeQueryInterval createTimeQueryInterval) {
        NoticeMessageDO condition = pageQuery.getCondition();
        QueryWrapper query = QueryWrapper.create();
        query.select(NOTICE_MESSAGE.ALL_COLUMNS)
                .from(NOTICE_MESSAGE.getTableName())
                .where(NOTICE_MESSAGE.IS_DELETE.eq(false))
                // 主文件夹不展示
                .orderBy(pageQuery.getOrderBy(), CommonConstants.ASC.equals(pageQuery.getOrderDirection()));
        if (Objects.nonNull(createTimeQueryInterval) &&Objects.nonNull(createTimeQueryInterval.getStartTime()) && Objects.nonNull(createTimeQueryInterval.getEndTime())) {
            query.and(OPERATION_LOG.CREATE_TIME.ge(createTimeQueryInterval.getStartTime()))
                    .and(OPERATION_LOG.CREATE_TIME.le(createTimeQueryInterval.getEndTime()));
        }
        if(Objects.nonNull(condition)&&Objects.nonNull(condition.getType())){
            query.and(NOTICE_MESSAGE.TYPE.eq(condition.getType()));
        }
        if(Objects.nonNull(condition)&&Objects.nonNull(condition.getState())){
            query.and(NOTICE_MESSAGE.STATE.eq(condition.getState()));
        }
        if(Objects.nonNull(condition)&&StringUtils.isNotEmpty(condition.getShortId())){
            query.and(NOTICE_MESSAGE.SHORT_ID.eq(condition.getShortId()));
        }
        Page<NoticeMessageDO> page = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        return noticeMessageGateway.pageAs(page, query, NoticeMessageDO.class);
    }

    @Override
    public Long getCount(NoticeMessageDO noticeMessageDO) {
        QueryWrapper query = QueryWrapper.create();
        query.from(NOTICE_MESSAGE.getTableName())
                .where(NOTICE_MESSAGE.IS_DELETE.eq(false))
                .and(NOTICE_MESSAGE.SHORT_ID.eq(noticeMessageDO.getShortId()));
        if(Objects.nonNull(noticeMessageDO)&&Objects.nonNull(noticeMessageDO.getType())){
            query.and(NOTICE_MESSAGE.TYPE.eq(noticeMessageDO.getType()));
        }
        if(Objects.nonNull(noticeMessageDO)&&Objects.nonNull(noticeMessageDO.getState())){
            query.and(NOTICE_MESSAGE.STATE.eq(noticeMessageDO.getState()));
        }
        return noticeMessageGateway.count(query);
    }

    @Override
    public NoticeMessageDO save(NoticeMessageDO noticeMessageDO) {
        return noticeMessageGateway.save(noticeMessageDO);
    }

    @Override
    public void saveBatch(List<NoticeMessageDO> noticeMessageDOList) {
        noticeMessageGateway.saveBatch(noticeMessageDOList);
    }
}
