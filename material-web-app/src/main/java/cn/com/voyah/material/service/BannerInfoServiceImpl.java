package cn.com.voyah.material.service;

import cn.com.voyah.material.client.BannerInfoService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.BannerInfoDO;
import cn.com.voyah.material.domain.entity.QuickAccessAreaDO;
import cn.com.voyah.material.domain.gateway.BannerInfoGateway;
import cn.com.voyah.material.dto.BannerInfoDTO;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.update.SortChangeDTO;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.pojo.BannerInfoPO;
import cn.com.voyah.material.pojo.QuickAccessAreaPO;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigInteger;
import java.util.Collection;
import java.util.Objects;
import static cn.com.voyah.domain.entity.def.BannerInfoDef.BANNER_INFO;
import static cn.com.voyah.domain.entity.def.FileDetailDef.FILE_DETAIL;

@Service
public class BannerInfoServiceImpl implements BannerInfoService {

    @Autowired
    private BannerInfoGateway bannerInfoGateway;
    @Override
    public Collection<BannerInfoDTO> list(BannerInfoDO bannerInfoDO) {
        QueryWrapper query = QueryWrapper.create();
        query.select(BANNER_INFO.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL,
                        FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL,FILE_DETAIL.CONTENT_TYPE,FILE_DETAIL.SIZE)
                .from(BANNER_INFO.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(BANNER_INFO.FILE_ID))
                .where(BANNER_INFO.IS_DELETE.eq(false))
                // 主文件夹不展示
                .orderBy(BANNER_INFO.SORT.getName(),false);
        if(Objects.nonNull(bannerInfoDO.getStatus())){
            query.and(BANNER_INFO.STATUS.eq(bannerInfoDO.getStatus()));
        }
        if(!StringUtils.isEmpty(bannerInfoDO.getName())){
            query.and(BANNER_INFO.NAME.like(bannerInfoDO.getName()));
        }
        return bannerInfoGateway.listAs(query, BannerInfoDTO.class);
    }

    @Override
    public Page<BannerInfoDTO> page(PageQueryCondition<BannerInfoDO> pageQuery, TimeQueryInterval createTimeQueryInterval) {
        BannerInfoDO condition = pageQuery.getCondition();
        QueryWrapper query = QueryWrapper.create();
        query.select(BANNER_INFO.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL,
                        FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL,FILE_DETAIL.CONTENT_TYPE,FILE_DETAIL.SIZE)
                .from(BANNER_INFO.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(BANNER_INFO.FILE_ID))
                .where(BANNER_INFO.IS_DELETE.eq(false))
                // 主文件夹不展示
                .orderBy(pageQuery.getOrderBy(), CommonConstants.ASC.equals(pageQuery.getOrderDirection()));
        if(Objects.nonNull(condition.getStatus())){
            query.and(BANNER_INFO.STATUS.eq(condition.getStatus()));
        }
        if(!StringUtils.isEmpty(condition.getName())){
            query.and(BANNER_INFO.NAME.like(condition.getName()));
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(pageQuery.getUserNameOrShortId())) {
            query.and(BANNER_INFO.CREATE_BY.like(pageQuery.getUserNameOrShortId())
                    .or(BANNER_INFO.CREATE_SHORT_ID.like(pageQuery.getUserNameOrShortId())));
        }

        // 添加时间查询
        if (Objects.nonNull(createTimeQueryInterval) &&Objects.nonNull(createTimeQueryInterval.getStartTime()) && Objects.nonNull(createTimeQueryInterval.getEndTime())) {
            query.and(BANNER_INFO.CREATE_TIME.ge(createTimeQueryInterval.getStartTime()))
                    .and(BANNER_INFO.CREATE_TIME.le(createTimeQueryInterval.getEndTime()));
        }
        Page<BannerInfoDTO> page = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        return bannerInfoGateway.pageAs(page, query, BannerInfoDTO.class);
    }

    @Override
    public BannerInfoDTO getById(Long id) {
        QueryWrapper query = QueryWrapper.create();
        query.select(BANNER_INFO.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL,
                        FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL,FILE_DETAIL.CONTENT_TYPE,FILE_DETAIL.SIZE)
                .from(BANNER_INFO.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(BANNER_INFO.FILE_ID))
                .where(BANNER_INFO.IS_DELETE.eq(false))
                .and(BANNER_INFO.ID.eq(id));
        return bannerInfoGateway.getOneAs(query,BannerInfoDTO.class);
    }

    @Override
    public String generateFileName(String dir,String originName) {
        if (StringUtils.isEmpty(originName)) {
            throw new BizException("文件名不能为空");
        }
        int index = originName.lastIndexOf(CommonConstants.FILE_SEPRATE_SUFFIX);
        String prefix=dir+"banner"+CommonConstants.DIR_SEPRATE_STR;
        String suffix = "";
        if (index > 0) {
            prefix = prefix+originName.substring(0, index);
            suffix = originName.substring(index);
        } else {
            prefix = prefix+originName;
        }
        String finalName = originName;
        boolean existFlag = false;
        int i = 0;
        do {
            if (i > 0) {
                StringBuilder stringBuilder = new StringBuilder(prefix);
                stringBuilder.append(CommonConstants.FILE_DUPLICATION_SEPRATE);
                stringBuilder.append(i);
                if (!StringUtils.isEmpty(suffix)) {
                    stringBuilder.append(suffix);
                }
                finalName = stringBuilder.toString();
            }
            existFlag = existFileNameDb(finalName);
            i++;
        } while (existFlag);
        return finalName;
    }

    @Override
    public Boolean sortChange(SortChangeDTO sortChangeDTO) {
        Long beforeId=sortChangeDTO.getBeforeId();
        Long afterId=sortChangeDTO.getAfterId();
        if(ObjectUtils.isEmpty(beforeId)&&ObjectUtils.isEmpty(afterId)){
            return false;
        }
        Long beforeSort=null;
        if(!ObjectUtils.isEmpty(beforeId)){
            BannerInfoDO quickAccessAreaDO=bannerInfoGateway.getById(beforeId);
            beforeSort=quickAccessAreaDO.getSort();
        }
        Long afterSort=null;
        if(!ObjectUtils.isEmpty(afterId)){
            BannerInfoDO quickAccessAreaDO=bannerInfoGateway.getById(afterId);
            afterSort=quickAccessAreaDO.getSort();
        }
        if(ObjectUtils.isEmpty(beforeSort)&&!ObjectUtils.isEmpty(afterSort)){
            BannerInfoPO quickAccessAreaDO=bannerInfoGateway.getNearerDOBySort(afterSort,true);
            if(ObjectUtil.isNotEmpty(quickAccessAreaDO)){
                beforeSort=quickAccessAreaDO.getSort();
            }
        }
        if(ObjectUtils.isEmpty(afterSort)&&!ObjectUtils.isEmpty(beforeSort)){
            BannerInfoPO quickAccessAreaDO=bannerInfoGateway.getNearerDOBySort(beforeSort,false);
            if(ObjectUtil.isNotEmpty(quickAccessAreaDO)){
                afterSort=quickAccessAreaDO.getSort();
            }
        }
        Long sort= avgSort(beforeSort,afterSort);
        return bannerInfoGateway.updateSort(sortChangeDTO.getId(),sort);
    }

    private Long avgSort(Long beforeSort,Long afterSort){
        if(ObjectUtil.isEmpty(beforeSort)){
            beforeSort=Long.MAX_VALUE;
        }
        if(ObjectUtil.isEmpty(afterSort)){
            afterSort=0L;
        }
        return BigInteger.valueOf(beforeSort).add(BigInteger.valueOf(afterSort)).divide(BigInteger.valueOf(2)).longValue();
    }

    private boolean existFileNameDb(String finalName) {
        QueryWrapper query = QueryWrapper.create();
        query.from(BANNER_INFO.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(BANNER_INFO.FILE_ID))
                .where(FILE_DETAIL.FILENAME.eq(finalName)
                        .and(BANNER_INFO.IS_DELETE.eq(false)));
        long count = bannerInfoGateway.count(query);
        return count > 0;
    }
}
