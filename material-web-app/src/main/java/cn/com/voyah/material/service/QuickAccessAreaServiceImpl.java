package cn.com.voyah.material.service;

import cn.com.voyah.material.client.QuickAccessAreaService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.QuickAccessAreaDO;
import cn.com.voyah.material.domain.gateway.QuickAccessAreaGateway;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.QuickAccessAreaDTO;
import cn.com.voyah.material.dto.update.SortChangeDTO;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.pojo.QuickAccessAreaPO;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import java.math.BigInteger;
import java.util.Collection;
import java.util.Objects;
import static cn.com.voyah.domain.entity.def.QuickAccessAreaDef.QUICK_ACCESS_AREA;
import static cn.com.voyah.domain.entity.def.FileDetailDef.FILE_DETAIL;
@Service
public class QuickAccessAreaServiceImpl implements QuickAccessAreaService {


    @Autowired
    private QuickAccessAreaGateway quickAccessAreaGateway;
    @Override
    public Collection<QuickAccessAreaDTO> list(QuickAccessAreaDO quickAccessAreaDO) {
        QueryWrapper query = QueryWrapper.create();
        query.select(QUICK_ACCESS_AREA.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL,
                        FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL,FILE_DETAIL.CONTENT_TYPE,FILE_DETAIL.SIZE)
                .from(QUICK_ACCESS_AREA.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(QUICK_ACCESS_AREA.FILE_ID))
                .where(QUICK_ACCESS_AREA.IS_DELETE.eq(false))
                // 主文件夹不展示
                .orderBy(QUICK_ACCESS_AREA.SORT.getName(),false);
        if(Objects.nonNull(quickAccessAreaDO.getStatus())){
            query.and(QUICK_ACCESS_AREA.STATUS.eq(quickAccessAreaDO.getStatus()));
        }
        if(!StringUtils.isEmpty(quickAccessAreaDO.getName())){
            query.and(QUICK_ACCESS_AREA.NAME.like(quickAccessAreaDO.getName()));
        }
        return quickAccessAreaGateway.listAs(query,QuickAccessAreaDTO.class);
    }

    @Override
    public Page<QuickAccessAreaDTO> page(PageQueryCondition<QuickAccessAreaDO> pageQuery, TimeQueryInterval createTimeQueryInterval) {

        QuickAccessAreaDO condition = pageQuery.getCondition();
        QueryWrapper query = QueryWrapper.create();
        query.select(QUICK_ACCESS_AREA.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL,
                        FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL,FILE_DETAIL.CONTENT_TYPE,FILE_DETAIL.SIZE)
                .from(QUICK_ACCESS_AREA.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(QUICK_ACCESS_AREA.FILE_ID))
                .where(QUICK_ACCESS_AREA.IS_DELETE.eq(false))
                // 主文件夹不展示
                .orderBy(pageQuery.getOrderBy(), CommonConstants.ASC.equals(pageQuery.getOrderDirection()));
        if(Objects.nonNull(condition.getStatus())){
            query.and(QUICK_ACCESS_AREA.STATUS.eq(condition.getStatus()));
        }
        if(!StringUtils.isEmpty(condition.getName())){
            query.and(QUICK_ACCESS_AREA.NAME.like(condition.getName()));
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(pageQuery.getUserNameOrShortId())) {
            query.and(QUICK_ACCESS_AREA.CREATE_BY.like(pageQuery.getUserNameOrShortId())
                    .or(QUICK_ACCESS_AREA.CREATE_SHORT_ID.like(pageQuery.getUserNameOrShortId())));
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(pageQuery.getCondition().getCreateBy())) {
            query.and(QUICK_ACCESS_AREA.CREATE_BY.like(pageQuery.getCondition().getCreateBy())
                    .or(QUICK_ACCESS_AREA.CREATE_SHORT_ID.like(pageQuery.getCondition().getCreateBy())));
        }
        // 添加时间查询
        if (Objects.nonNull(createTimeQueryInterval) &&Objects.nonNull(createTimeQueryInterval.getStartTime()) && Objects.nonNull(createTimeQueryInterval.getEndTime())) {
            query.and(QUICK_ACCESS_AREA.CREATE_TIME.ge(createTimeQueryInterval.getStartTime()))
                    .and(QUICK_ACCESS_AREA.CREATE_TIME.le(createTimeQueryInterval.getEndTime()));
        }
        Page<QuickAccessAreaDTO> page = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        return quickAccessAreaGateway.pageAs(page, query, QuickAccessAreaDTO.class);
    }

    @Override
    public QuickAccessAreaDTO getById(Long id) {
        QueryWrapper query = QueryWrapper.create();
        query.select(QUICK_ACCESS_AREA.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL,
                        FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL,FILE_DETAIL.CONTENT_TYPE,FILE_DETAIL.SIZE)
                .from(QUICK_ACCESS_AREA.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(QUICK_ACCESS_AREA.FILE_ID))
                .where(QUICK_ACCESS_AREA.IS_DELETE.eq(false))
                .and(QUICK_ACCESS_AREA.ID.eq(id));
        return quickAccessAreaGateway.getOneAs(query,QuickAccessAreaDTO.class);
    }

    @Override
    public String generateFileName(String dir,String originName) {
        if (StringUtils.isEmpty(originName)) {
            throw new BizException("文件名不能为空");
        }
        int index = originName.lastIndexOf(CommonConstants.FILE_SEPRATE_SUFFIX);
        String prefix=dir+"quickaa"+CommonConstants.DIR_SEPRATE_STR;
        String suffix = "";
        if (index > 0) {
            prefix = prefix+originName.substring(0, index);
            suffix = originName.substring(index);
        } else {
            prefix = prefix+originName;
        }
        String finalName = originName;
        boolean existFlag = false;
        int i = 0;
        do {
            if (i > 0) {
                StringBuilder stringBuilder = new StringBuilder(prefix);
                stringBuilder.append(CommonConstants.FILE_DUPLICATION_SEPRATE);
                stringBuilder.append(i);
                if (!StringUtils.isEmpty(suffix)) {
                    stringBuilder.append(suffix);
                }
                finalName = stringBuilder.toString();
            }
            existFlag = existFileNameDb(finalName);
            i++;
        } while (existFlag);
        return finalName;
    }

    @Override
    public Boolean sortChange(SortChangeDTO sortChangeDTO) {
        Long beforeId=sortChangeDTO.getBeforeId();
        Long afterId=sortChangeDTO.getAfterId();
        if(ObjectUtils.isEmpty(beforeId)&&ObjectUtils.isEmpty(afterId)){
            return false;
        }
        Long beforeSort=null;
        if(!ObjectUtils.isEmpty(beforeId)){
            QuickAccessAreaDO quickAccessAreaDO=quickAccessAreaGateway.getById(beforeId);
            beforeSort=quickAccessAreaDO.getSort();
        }
        Long afterSort=null;
        if(!ObjectUtils.isEmpty(afterId)){
            QuickAccessAreaDO quickAccessAreaDO=quickAccessAreaGateway.getById(afterId);
            afterSort=quickAccessAreaDO.getSort();
        }
        if(ObjectUtils.isEmpty(beforeSort)&&!ObjectUtils.isEmpty(afterSort)){
            QuickAccessAreaPO quickAccessAreaDO=quickAccessAreaGateway.getNearerDOBySort(afterSort,true);
            if(ObjectUtil.isNotEmpty(quickAccessAreaDO)){
                beforeSort=quickAccessAreaDO.getSort();
            }
        }
        if(ObjectUtils.isEmpty(afterSort)&&!ObjectUtils.isEmpty(beforeSort)){
            QuickAccessAreaPO quickAccessAreaDO=quickAccessAreaGateway.getNearerDOBySort(beforeSort,false);
            if(ObjectUtil.isNotEmpty(quickAccessAreaDO)){
                afterSort=quickAccessAreaDO.getSort();
            }
        }
        Long sort= avgSort(beforeSort,afterSort);
        return quickAccessAreaGateway.updateSort(sortChangeDTO.getId(),sort);
    }

    private Long avgSort(Long beforeSort,Long afterSort){
        if(ObjectUtil.isEmpty(beforeSort)){
            beforeSort=Long.MAX_VALUE;
        }
        if(ObjectUtil.isEmpty(afterSort)){
            afterSort=0L;
        }
        return BigInteger.valueOf(beforeSort).add(BigInteger.valueOf(afterSort)).divide(BigInteger.valueOf(2)).longValue();
    }
    private boolean existFileNameDb(String finalName) {
        QueryWrapper query = QueryWrapper.create();
        query.from(QUICK_ACCESS_AREA.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(QUICK_ACCESS_AREA.FILE_ID))
                .where(FILE_DETAIL.FILENAME.eq(finalName)
                        .and(QUICK_ACCESS_AREA.IS_DELETE.eq(false)));
        long count = quickAccessAreaGateway.count(query);
        return count > 0;
    }

}
