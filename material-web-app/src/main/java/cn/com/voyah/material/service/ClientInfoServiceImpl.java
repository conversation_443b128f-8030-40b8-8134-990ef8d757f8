package cn.com.voyah.material.service;

import cn.com.voyah.material.client.ClientInfoService;
import cn.com.voyah.material.domain.gateway.ClientInfoGateway;
import cn.com.voyah.material.dto.ClientInfoDTO;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ClientInfoServiceImpl implements ClientInfoService {

    @Autowired
    private ClientInfoGateway clientInfoGateway;
    @Override
    public List<ClientInfoDTO> getRecentList() {
        List<ClientInfoDTO> list=clientInfoGateway.getList();
        List<ClientInfoDTO> resultList=new ArrayList();
        Set<String> nameSet=new HashSet<>();
        if(CollectionUtil.isNotEmpty(list)){
            list.stream().forEach(each->{
                if(!nameSet.contains(each.getName())){
                    nameSet.add(each.getName());
                    resultList.add(each);
                }
            });
        }
        return resultList;
    }
}
