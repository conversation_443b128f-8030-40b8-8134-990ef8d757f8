package cn.com.voyah.material.service;

import cn.com.voyah.material.api.UserAccountService;
import cn.com.voyah.material.dto.PageResponse;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.user.UserAccountQuery;
import cn.com.voyah.material.dto.user.UserAccountVO;
import cn.com.voyah.material.exception.Assert;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.comm.IncResponse;
import com.iov.tencent.inc.access.model.properties.IncRole;
import com.iov.tencent.inc.access.model.request.IncQueryWithPagingReq;
import com.iov.tencent.inc.access.model.response.IncPageableUserListRespData;
import com.iov.tencent.inc.access.service.IncUserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class UserAccountServiceImpl implements UserAccountService {

    private IncUserService incUserService;

    @Override
    public PageResponse<UserAccountVO> page(PageQueryCondition<UserAccountQuery> query) {
        UserAccountQuery condition = query.getCondition();
        IncQueryWithPagingReq req = new IncQueryWithPagingReq();
        req.setFilterText(condition.getText());
        req.setPageSize(query.getPageSize());
        req.setPage(query.getPageIndex());
        IncResponse<IncPageableUserListRespData> response = incUserService.getUserListWithPaging(req, IncUserThreadLocal.get().getToken());
        this.validationSuccess(response);

        IncPageableUserListRespData data = response.getData();
        List<UserAccountVO> list = data.getList().stream()
                .map(item -> new UserAccountVO()
                        .setShortId(item.getId())
                        .setUserName(item.getUserName())
                        .setMobile(item.getMobile())
                        .setRoleNames(item.getRoles().stream().map(IncRole::getRoleName).collect(Collectors.joining(",")))
                        // TODO 字段待填充 最后一次登录时间 应用权限 管理后台权限
                        .setLastLoginTime(null)
                        .setAppPermission(false)
                        .setAdminPermission(false)
                        .setDeptNames(null))
                .toList();
        return PageResponse.of(list, data.getCount(), query.getPageSize(), data.getPage());
    }


    private void validationSuccess(IncResponse<?> response) {
        Assert.isFalse(response.getCode() != 0, "第三方接口访问失败，请稍后重试！");
    }
}
