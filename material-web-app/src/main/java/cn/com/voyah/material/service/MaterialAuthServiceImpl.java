package cn.com.voyah.material.service;

import cn.com.voyah.material.client.MaterialAuthService;
import cn.com.voyah.material.domain.entity.MaterialAuthDO;
import cn.com.voyah.material.domain.gateway.MaterialAuthGateway;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.pojo.MaterialAuthPO;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.utils.RedisKeys;
import cn.com.voyah.material.utils.RedisUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.properties.IncInnerUserInfo;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.com.voyah.domain.entity.def.MaterialAuthDef.MATERIAL_AUTH;

@Service
@Slf4j
public class MaterialAuthServiceImpl implements MaterialAuthService {

    @Autowired
    @Lazy
    private MaterialAuthGateway materialAuthGateway;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private RedisKeys redisKeys;
    @Override
    public List<Long> getAuthParentIdList() {
        IncInnerUserInfo userInfo=IncUserThreadLocal.get();
        String shortId;
        if (userInfo != null) {
            shortId = userInfo.getName();
        } else {
            shortId = "";
        }
        if(StringUtils.isEmpty(shortId)){
            log.error("shortId 为空");
            return List.of();
        }
        log.info("shortId:{}",shortId);
        //todo add cache
        List<Long> idList=new ArrayList();
        String authKey=getAuthMaterialIdListFullKey(shortId);
        Object obj=redisUtil.get(authKey);
        if(!ObjectUtil.isEmpty(obj)){
            return JSONUtil.toList((String)obj, Long.class);
        }
        List<MaterialAuthPO> authList=materialAuthGateway.getList(shortId);
        if(CollectionUtil.isNotEmpty(authList)){
            idList=authList.get(0).getMaterialIdList();
        }
        redisUtil.set(authKey,JSONUtil.toJsonStr(idList));
        return idList;
    }

    @Override
    public void removeAuthKeyCache(String shortId) {
        String authKey=getAuthMaterialIdListFullKey(shortId);
        redisUtil.del(authKey);
    }

    @Override
    public Page<MaterialAuthDO> page(PageQueryCondition<MaterialAuthDO> queryCondition) {
        MaterialAuthDO query = queryCondition.getCondition();
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.from(MATERIAL_AUTH.getTableName())
                .where(MATERIAL_AUTH.SHORT_ID.like(query.getShortId(), StringUtils::hasLength))
                .and(MATERIAL_AUTH.USER_NAME.like(query.getUserName(), Objects::nonNull))
                .and(MATERIAL_AUTH.IS_DELETE.eq(false))
                .orderBy(MATERIAL_AUTH.CREATE_TIME.desc());
        return materialAuthGateway.page(PageUtil.of(queryCondition), queryWrapper);
    }



    private String getAuthMaterialIdListFullKey(String shortId) {
        return String.format(redisKeys.getAuthMaterialIdListKey(), shortId);
    }
}
