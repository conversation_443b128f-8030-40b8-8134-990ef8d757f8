package cn.com.voyah.material.service;

import cn.com.voyah.material.client.FileDetailTmpService;
import cn.com.voyah.material.domain.entity.FileDetailTmpDO;
import cn.com.voyah.material.domain.gateway.FileDetailTmpGateway;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static cn.com.voyah.domain.entity.def.FileDetailTmpDef.FILE_DETAIL_TMP;

@Service
public class FileDetailTmpServiceImpl implements FileDetailTmpService {

    @Autowired
    private FileDetailTmpGateway fileDetailTmpGateway;

    @Override
    public List<FileDetailTmpDO> selectList(int size,List<String> contentTypes) {
        QueryWrapper query = QueryWrapper.create();
        query.select(FILE_DETAIL_TMP.ALL_COLUMNS)
                .from(FILE_DETAIL_TMP.getTableName())
                .where(FILE_DETAIL_TMP.IS_DELETE.eq(false))
                .and(FILE_DETAIL_TMP.STATUS.eq(0))
                .and(FILE_DETAIL_TMP.CONTENT_TYPE.in(contentTypes))
                // 主文件夹不展示
                .orderBy(FILE_DETAIL_TMP.CREATE_TIME.getName(),false)
                .limit(size);
        return fileDetailTmpGateway.listAs(query,FileDetailTmpDO.class);
    }

    @Override
    public void updateList(List<FileDetailTmpDO> list) {
        fileDetailTmpGateway.updateBatch(list);
    }
}
