package cn.com.voyah.material.service;

import cn.com.voyah.material.client.MaterialTagService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.*;
import cn.com.voyah.material.domain.gateway.*;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.properties.IncInnerUserInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;
@Service
public class MaterialTagServiceImpl implements MaterialTagService {
    @Autowired
    private MaterialTagGateway materialTagGateway;
    @Autowired
    private OriginMaterialGateway originMaterialGateway;
    @Autowired
    private MaterialTagItemGateway materialTagItemGateway;
    @Autowired
    private TagCategoryGateway tagCategoryGateway;

    @Autowired
    private TagInfoGateway tagInfoGateway;

    @Autowired
    private MaterialFavoriteGateway materialFavoriteGateway;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(MaterialTagVO materialTagVO) {
        Long materialId=materialTagVO.getMaterialId();
        Long categoryId=materialTagVO.getCategoryId();
        if(ObjectUtil.isEmpty(materialId)||ObjectUtil.isEmpty(categoryId)){
            throw new BizException("素材Id和类目id不能为空");
        }
        MaterialTagDO materialTagDO=materialTagGateway.getByMaterialId(materialId);
        boolean tagStatus=ObjectUtil.isNotEmpty(materialTagDO);
        if(!tagStatus){
            materialTagDO=materialTagGateway.save(MaterialTagDO.buildFrom(materialTagVO));
        }
        if(CollectionUtils.isEmpty(materialTagVO.getTagList())){
            return false;
        }
        materialTagItemGateway.delete(materialId,categoryId);
        List<MaterialTagItemDO> tagItemDOList=convertToTagItemList(materialTagDO.getId(),materialTagVO);
        if(CollectionUtils.isEmpty(tagItemDOList)){
            return false;
        }
        materialTagItemGateway.saveBatch(tagItemDOList);
        if(!tagStatus){
            originMaterialGateway.updateTagStatus(materialTagVO.getMaterialId(), CommonConstants.TAG_STATUS_TAG);
        }
        return true;
    }

    public List<MaterialTagItemDO> convertToTagItemList(Long materialTagId, MaterialTagVO materialTagVO){
        if(CollectionUtils.isEmpty(materialTagVO.getTagList())){
            return List.of();
        }
        Long materialId=materialTagVO.getMaterialId();
        Long categoryId=materialTagVO.getCategoryId();
        List<MaterialTagItemDO> tagItemDOList=recursionAddTag(materialId,categoryId,materialTagId,materialTagVO.getTagList());
        return tagItemDOList;
    }


    public List<MaterialTagItemDO> recursionAddTag(Long materialId,Long categoryId,Long materialTagId,List<TagInfoDTO> tagList){
        List<MaterialTagItemDO> tagItemDOList=new ArrayList<MaterialTagItemDO>();
        for (TagInfoDTO each:tagList) {
            if(CommonConstants.CHOOSED.equals(each.getChoose())){
                MaterialTagItemDO materialTagItemDO=new MaterialTagItemDO();
                materialTagItemDO.setTagId(each.getId());
                materialTagItemDO.setTagValue(each.getTagValue());
                materialTagItemDO.setLevel(each.getLevel());
                materialTagItemDO.setName(each.getName());
                materialTagItemDO.setCode(each.getCode());
                materialTagItemDO.setTagId(each.getId());
                materialTagItemDO.setLevel(each.getLevel());
                materialTagItemDO.setMaterialTagId(materialTagId);
                materialTagItemDO.setCategoryId(categoryId);
                materialTagItemDO.setMaterialId(materialId);
                materialTagItemDO.setParentId(each.getParentId());
                tagItemDOList.add(materialTagItemDO);
                if(CollectionUtil.isNotEmpty(each.getChildren())){
                    List<MaterialTagItemDO> childList=recursionAddTag(materialId,categoryId,materialTagId,each.getChildren());
                    if(CollectionUtil.isNotEmpty(childList)){
                        tagItemDOList.addAll(childList);
                    }
                }
            }
        }
        return tagItemDOList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeById(Long id) {
        materialTagGateway.removeById(id);
        return originMaterialGateway.updateTagStatus(id, CommonConstants.TAG_STATUS_UNTAG);
    }

    @Override
    public Boolean updateById(MaterialTagVO materialTagVO) {
        Long materialId=materialTagVO.getMaterialId();
        Long categoryId=materialTagVO.getCategoryId();
        if(ObjectUtil.isEmpty(materialId)||ObjectUtil.isEmpty(categoryId)){
            throw new BizException("素材Id和类目id不能为空");
        }
        MaterialTagDO materialTagDO=materialTagGateway.getByMaterialId(materialId);
        if(ObjectUtil.isEmpty(materialTagDO)){
            return false;
        }
        if(CollectionUtils.isEmpty(materialTagVO.getTagList())){
            return false;
        }
        materialTagItemGateway.delete(materialId,categoryId);
        List<MaterialTagItemDO> tagItemDOList=convertToTagItemList(materialTagDO.getId(),materialTagVO);
        if(CollectionUtils.isEmpty(tagItemDOList)){
            return false;
        }
        materialTagItemGateway.saveBatch(tagItemDOList);
        originMaterialGateway.updateTagStatus(materialTagVO.getMaterialId(), CommonConstants.TAG_STATUS_TAG);
        return true;
    }

    @Override
    public MaterialTagVO getByMaterialId(Long materialId,Long categoryId) {
        MaterialTagDO materialTagDO=materialTagGateway.getByMaterialId(materialId);
        MaterialTagVO materialTagVO=new MaterialTagVO();
        if(ObjectUtil.isNotEmpty(materialTagDO)){
            materialTagVO.setMaterialTagId(materialTagDO.getId());
        }
        materialTagVO.setCategoryId(categoryId);
        materialTagVO.setMaterialId(materialId);
        List<MaterialTagItemDO> materialTagItemDOList=materialTagItemGateway.getList(materialId,categoryId);
        List<TagInfoDO> tagBaseList=tagInfoGateway.getList(categoryId);
        List<TagInfoDTO> tagList=convertToTagList(tagBaseList,materialTagItemDOList);
        materialTagVO.setTagList(tagList);
        return materialTagVO;
    }



    public List<TagInfoDTO> convertToTagList(List<TagInfoDO> tagBaseList,List<MaterialTagItemDO> materialTagItemDOList){
        if(CollectionUtils.isEmpty(tagBaseList)){
            return List.of();
        }
        List<Long> tagIdList=new ArrayList<>();
        if(!CollectionUtils.isEmpty(materialTagItemDOList)){
            tagIdList=materialTagItemDOList.stream().map(each->each.getTagId()).toList();
        }
        Map<Long,TagInfoDO> tagIdMap=tagBaseList.stream().collect(Collectors.toMap(TagInfoDO::getId, a->a));
        Map<Long,List<Long>> parentIdMap=new HashMap();
        for (TagInfoDO each:tagBaseList) {
            parentIdMap.computeIfAbsent(each.getParentId(),k->new ArrayList<Long>());
            parentIdMap.get(each.getParentId()).add(each.getId());
        }
        return recursion(0L,parentIdMap,tagIdMap,tagIdList);
    }

    public List<TagInfoDTO> recursion(Long parentId,Map<Long,List<Long>> parentIdMap,Map<Long,TagInfoDO> tagIdMap,List<Long> tagIdList){
        List<Long> ids=parentIdMap.get(parentId);
        if(CollectionUtils.isEmpty(ids)){
            return List.of();
        }
        List<TagInfoDTO> tagInfoDTOList=new ArrayList<TagInfoDTO>();
        for(Long id:ids){
            TagInfoDO each=tagIdMap.get(id);
            TagInfoDTO tagInfoDTO=new TagInfoDTO();
            BeanUtils.copyProperties(each,tagInfoDTO);
            List<TagInfoDTO> childList=recursion(id,parentIdMap,tagIdMap,tagIdList);
            if(!CollectionUtils.isEmpty(childList)){
                tagInfoDTO.setChildren(childList);
            }
            tagInfoDTOList.add(tagInfoDTO);
            tagInfoDTO.setChoose(tagIdList.contains(each.getId())?1:0);
        }
        return tagInfoDTOList;
    }

    @Override
    public Set<MaterialTagHeadingVO> getHeadingByMaterialId(Long materialId, Long categoryId) {
        List<TagInfoDO> tagHeadingList=tagInfoGateway.getTagNameAndLevelList(categoryId);
        List<MaterialTagItemDO> itemList=getBindTagsByMaterialId(materialId,categoryId);
        return convertItemListToHeadVO(tagHeadingList,itemList,categoryId);

    }

    private Set<MaterialTagHeadingVO> convertItemListToHeadVO(List<TagInfoDO> tagHeadingList,List<MaterialTagItemDO> itemList,Long categoryId){
        Map<String,Map<String,List<Long>>> map=new HashMap();
        if(CollectionUtil.isNotEmpty(itemList)){
            for (MaterialTagItemDO each:itemList) {
                String key=each.getName()+CommonConstants.FILE_DUPLICATION_SEPRATE+each.getLevel();
                map.putIfAbsent(key,new HashMap<String,List<Long>>());
                map.get(key).putIfAbsent(each.getTagValue(),new ArrayList<Long>());
                map.get(key).get(each.getTagValue()).add(each.getTagId());
            }
        }

        return convertToTagHeading(tagHeadingList,map,categoryId);
    }

    public Set<MaterialTagHeadingVO> convertToTagHeading(List<TagInfoDO> tagHeadingList, Map<String,Map<String,List<Long>>> map, Long categoryId){
        Set<MaterialTagHeadingVO> tagHeadingVOList=new TreeSet<MaterialTagHeadingVO>();
        for (TagInfoDO each:tagHeadingList) {
            if(!categoryId.equals(each.getCategoryId())){
                continue;
            }
            MaterialTagHeadingVO materialTagHeadingVO=new MaterialTagHeadingVO();
            materialTagHeadingVO.setLevel(each.getLevel());
            materialTagHeadingVO.setTagName(each.getName());
            materialTagHeadingVO.setCreateTime(each.getCreateTime());
            String key=each.getName()+CommonConstants.FILE_DUPLICATION_SEPRATE+each.getLevel();
            if(map.containsKey(key)){
                List<Long> tagIdlist=new ArrayList();
//                List<List<Long>> groupIds=new ArrayList<>();
                if(CollectionUtil.isNotEmpty(map.get(key))){
                    for (Map.Entry<String,List<Long>> entry:map.get(key).entrySet()) {
                        tagIdlist.addAll(entry.getValue());
//                        groupIds.add(entry.getValue());
                    }
                }
                materialTagHeadingVO.setTagIdlist(tagIdlist);
//                if(CollectionUtil.isNotEmpty(groupIds)){
//                    materialTagHeadingVO.setGroupIds(groupIds);
//                }
            }else{
                materialTagHeadingVO.setTagIdlist(List.of());
            }
            materialTagHeadingVO.setCategoryId(categoryId);
            tagHeadingVOList.add(materialTagHeadingVO);
        }
        return tagHeadingVOList;
    }

    @Override
    public List<MaterialTagItemDO> getBindTagsByMaterialId(Long materialId,Long categoryId) {
        MaterialTagDO materialTagDO=materialTagGateway.getByMaterialId(materialId);
        MaterialTagVO materialTagVO=new MaterialTagVO();
        if(ObjectUtil.isNotEmpty(materialTagDO)){
            materialTagVO.setMaterialTagId(materialTagDO.getId());
        }
        materialTagVO.setCategoryId(categoryId);
        materialTagVO.setMaterialId(materialId);
        List<MaterialTagItemDO> materialTagItemDOList=materialTagItemGateway.getList(materialId,categoryId);
        return materialTagItemDOList;
    }

    @Override
    public Boolean saveNew(MaterialTagNewDTO materialTagNewDTO) {
        Long materialId=materialTagNewDTO.getMaterialId();
        Long categoryId=materialTagNewDTO.getCategoryId();
        if(ObjectUtil.isEmpty(materialId)||ObjectUtil.isEmpty(categoryId)){
            throw new BizException("素材Id和类目id不能为空");
        }
        MaterialTagDO materialTagDO=materialTagGateway.getByMaterialId(materialId);
        boolean tagStatus=ObjectUtil.isNotEmpty(materialTagDO);
        if(!tagStatus){
            materialTagDO=materialTagGateway.save(MaterialTagDO.buildFrom(materialTagNewDTO));
        }
        if(CollectionUtils.isEmpty(materialTagNewDTO.getTagIdList())){
            return false;
        }
        List<MaterialTagItemDO> alreadyList=materialTagItemGateway.getBaseList(materialId,categoryId);
        List<Long> toDeleteIds=new ArrayList();
        List<Long> toAddIdList=new ArrayList();
        List<Long> alreadyTagIdList=new ArrayList();
        if(!CollectionUtils.isEmpty(alreadyList)){
            alreadyList.forEach(each->alreadyTagIdList.add(each.getTagId()));
        }
        for (Long each:materialTagNewDTO.getTagIdList()) {
            if(!alreadyTagIdList.contains(each)){
                toAddIdList.add(each);
            }
        }
        if(!CollectionUtils.isEmpty(alreadyTagIdList)){
            for (Long each:alreadyTagIdList) {
                if(!materialTagNewDTO.getTagIdList().contains(each)){
                    toDeleteIds.add(each);
                }
            }
        }
        if(!CollectionUtils.isEmpty(toAddIdList)){
            List<TagInfoDO> list=tagInfoGateway.getListByIdList(toAddIdList);
            if(!CollectionUtils.isEmpty(list)){
                List<MaterialTagItemDO> toAddList=recursionAddTags(materialId,categoryId,materialTagDO.getId(),list);
                materialTagItemGateway.saveBatch(toAddList);
            }
        }
        if(!CollectionUtils.isEmpty(toDeleteIds)){
            materialTagItemGateway.deleteByTagIds(categoryId,materialId,toDeleteIds);
        }
        if(!tagStatus){
            originMaterialGateway.updateTagStatus(materialId, CommonConstants.TAG_STATUS_TAG);
        }
        return true;
    }




    public List<MaterialTagItemDO> recursionAddTags(Long materialId,Long categoryId,Long materialTagId,List<TagInfoDO> tagInfos){
        List<MaterialTagItemDO> tagItemDOList=new ArrayList<MaterialTagItemDO>();
        for (TagInfoDO each:tagInfos) {
            MaterialTagItemDO materialTagItemDO=new MaterialTagItemDO();
            materialTagItemDO.setTagId(each.getId());
            materialTagItemDO.setTagValue(each.getTagValue());
            materialTagItemDO.setLevel(each.getLevel());
            materialTagItemDO.setName(each.getName());
            materialTagItemDO.setCode(each.getCode());
            materialTagItemDO.setTagId(each.getId());
            materialTagItemDO.setLevel(each.getLevel());
            materialTagItemDO.setMaterialTagId(materialTagId);
            materialTagItemDO.setCategoryId(each.getCategoryId());
            materialTagItemDO.setMaterialId(materialId);
            materialTagItemDO.setParentId(each.getParentId());
            tagItemDOList.add(materialTagItemDO);
        }
        return tagItemDOList;
    }


    @Override
    public Collection<MaterialTagHeadingVO2> getHeadingByMaterialId2(Long materialId, Long categoryId) {
        List<TagInfoDO> tagHeadingList=tagInfoGateway.getTagNameAndLevelList(categoryId);
        List<MaterialTagItemDO> itemList=getBindTagsByMaterialId(materialId,categoryId);
        Map<String,Map<String,List<Long>>> map=new HashMap();
        if(CollectionUtil.isNotEmpty(itemList)){
            for (MaterialTagItemDO each:itemList) {
                String key=each.getName()+CommonConstants.FILE_DUPLICATION_SEPRATE+each.getLevel();
                map.putIfAbsent(key,new HashMap<String,List<Long>>());
                map.get(key).putIfAbsent(each.getTagValue(),new ArrayList<Long>());
                map.get(key).get(each.getTagValue()).add(each.getTagId());
            }
        }
        return convertToTagHeading2(tagHeadingList,map,categoryId);
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean addRemark(MaterialTagDO materialTagNew) {

        Long materialId=materialTagNew.getMaterialId();
        if(ObjectUtil.isEmpty(materialId)){
            throw new BizException("素材Id不能为空");
        }
        MaterialTagDO materialTag=materialTagGateway.getByMaterialId(materialId);
        boolean tagStatus=ObjectUtil.isNotEmpty(materialTag);
        if(tagStatus){
            if(!ObjectUtils.isEmpty(materialTag.getId())){
                materialTagGateway.updateTagRemark(materialTag.getId(),materialTagNew.getRemark());
            }
        }else{
            materialTagGateway.save(materialTagNew);
            originMaterialGateway.updateTagStatus(materialId, CommonConstants.TAG_STATUS_TAG);
        }
        return true;
    }

    @Override
    public MaterialTagDO getBaseInfoByMaterialId(Long materialId) {
        return materialTagGateway.getByMaterialId(materialId);
    }

    @Override
    public List<Long> getCategoryIdList(Long materialId) {
        return materialTagItemGateway.getCategoryIdList(materialId);
    }



    public Set<MaterialTagHeadingVO2> convertToTagHeading2(List<TagInfoDO> tagHeadingList,Map<String,Map<String,List<Long>>> map, Long categoryId){
        Set<MaterialTagHeadingVO2> tagHeadingVOList=new TreeSet<MaterialTagHeadingVO2>();
        for (TagInfoDO each:tagHeadingList) {
            MaterialTagHeadingVO2 materialTagHeadingVO2=new MaterialTagHeadingVO2();
            materialTagHeadingVO2.setLevel(each.getLevel());
            materialTagHeadingVO2.setTagName(each.getName());
            String key=each.getName()+CommonConstants.FILE_DUPLICATION_SEPRATE+each.getLevel();
            if(map.containsKey(key)){
                materialTagHeadingVO2.setTagValueMap(map.get(key));
            }
            materialTagHeadingVO2.setCategoryId(categoryId);
            tagHeadingVOList.add(materialTagHeadingVO2);
        }
        return tagHeadingVOList;
    }


    @Override
    public boolean addViewCount(Long materialId) {

        if (ObjectUtil.isEmpty(materialId) || materialId == 0) {
            return false;
        }
        return materialTagGateway.addViewCount(materialId);
    }

    /**
     * 添加收藏记录
     * 素材收藏数量+1
     */
    @Override
    public boolean addFavoriteCount(Long materialId,boolean isIncr) {
        if (ObjectUtil.isEmpty(materialId) || materialId == 0) {
            throw new BizException("素材id不能为空!");
        }

        IncInnerUserInfo userInfo= IncUserThreadLocal.get();
        if(ObjectUtil.isEmpty(userInfo)){
            throw new BizException("请先登录!");
        }
        if(isIncr){
            MaterialFavoriteDO materialFavoriteDO=new MaterialFavoriteDO();
            materialFavoriteDO.setShortId(userInfo.getName());
            materialFavoriteDO.setUserName(userInfo.getUserName());
            materialFavoriteDO.setMaterialId(materialId);
            materialFavoriteDO.setIsDelete(false);
            materialFavoriteGateway.save(materialFavoriteDO);
        }else{
            materialFavoriteGateway.deleteByMaterialIdAndShortId(materialId,userInfo.getName());
        }
        return materialTagGateway.addFavoriteCount(materialId,isIncr);
    }

    @Override
    public MaterialTagDO getBaseByMaterialId(Long materialId) {
        return materialTagGateway.getByMaterialId(materialId);
    }

    @Override
    public List<Long> getMaterialIdListByTags(List<TagInfoMergeVO> tags) {

        return materialTagItemGateway.getMaterialIdListByTags(tags);
    }

    @Override
    public Set<MaterialTagHeading3VO> getHeadingByMaterialId3(Long materialId, Long categoryId) {
        List<TagInfoDO> tagHeadingList=tagInfoGateway.getTagNameAndLevelList(categoryId);
        List<MaterialTagItemDO> itemList=getBindTagsByMaterialId(materialId,categoryId);
        Map<Long,List<MaterialTagItemDO>> map=new HashMap();
        for (MaterialTagItemDO each:itemList) {
            if(ObjectUtil.isNotEmpty(each.getCategoryId())){
                map.putIfAbsent(each.getCategoryId(),new ArrayList<MaterialTagItemDO>());
                map.get(each.getCategoryId()).add(each);
            }
        }
        Set<MaterialTagHeading3VO> resultList=new TreeSet<>();
        for (Map.Entry<Long,List<MaterialTagItemDO>> entry:map.entrySet()) {
            Set<MaterialTagHeadingVO> set=convertItemListToHeadVO(tagHeadingList,itemList,entry.getKey());
            MaterialTagHeading3VO materialTagHeading3VO=new MaterialTagHeading3VO();
            TagCategoryDO tagCategory=tagCategoryGateway.getById(entry.getKey());
            if(ObjectUtil.isNotEmpty(tagCategory)){
                materialTagHeading3VO.setName(tagCategory.getName());
                materialTagHeading3VO.setSort(tagCategory.getSort());
                materialTagHeading3VO.setCategoryId(tagCategory.getId());
                materialTagHeading3VO.setList(set);
                resultList.add(materialTagHeading3VO);
            }

        }
        return resultList;

    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveBatch(Long materialId, List<MaterialTagNewDTO> allBatchList) {
        List<MaterialTagNewDTO> batchList=new ArrayList<>();
        for (MaterialTagNewDTO materialTagNewDTO:allBatchList) {
            Long categoryId=materialTagNewDTO.getCategoryId();
            materialTagNewDTO.setMaterialId(materialId);
            if(ObjectUtil.isEmpty(materialId)||ObjectUtil.isEmpty(categoryId)){
                throw new BizException("素材Id和类目id不能为空");
            }
            if(!CollectionUtils.isEmpty(materialTagNewDTO.getTagIdList())){
                batchList.add(materialTagNewDTO);
            }
        }
        if(CollectionUtils.isEmpty(batchList)){
            throw new BizException("标签不能为空");
        }
        MaterialTagDO materialTagDO=materialTagGateway.getByMaterialId(materialId);
        boolean tagStatus=ObjectUtil.isNotEmpty(materialTagDO);
        if(!tagStatus){
            materialTagDO=materialTagGateway.save(MaterialTagDO.buildFrom(materialId,null));
        }

        List<MaterialTagItemDO> alreadyList=materialTagItemGateway.getBaseList(materialId,null);
        List<Long> toDeleteIds=new ArrayList();
        List<Long> toAddIdList=new ArrayList();
        List<Long> alreadyTagIdList=new ArrayList();
        if(!CollectionUtils.isEmpty(alreadyList)){
            alreadyList.forEach(each->alreadyTagIdList.add(each.getTagId()));
        }
        for(MaterialTagNewDTO materialTagNewDTO:batchList){
            for (Long each:materialTagNewDTO.getTagIdList()) {
                if(!alreadyTagIdList.contains(each)){
                    toAddIdList.add(each);
                }
            }
            if(!CollectionUtils.isEmpty(alreadyTagIdList)){
                for (Long each:alreadyTagIdList) {
                    if(!materialTagNewDTO.getTagIdList().contains(each)){
                        toDeleteIds.add(each);
                    }
                }
            }
        }
        if(!CollectionUtils.isEmpty(toAddIdList)){
            List<TagInfoDO> list=tagInfoGateway.getListByIdList(toAddIdList);
            if(!CollectionUtils.isEmpty(list)){
                List<MaterialTagItemDO> toAddList=recursionAddTags(materialId,null,materialTagDO.getId(),list);
                materialTagItemGateway.saveBatch(toAddList);
            }
        }
        if(!CollectionUtils.isEmpty(toDeleteIds)){
            materialTagItemGateway.deleteByTagIds(null,materialId,toDeleteIds);
        }
        if(!tagStatus){
            originMaterialGateway.updateTagStatus(materialId, CommonConstants.TAG_STATUS_TAG);
        }
        return true;
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveBatchNew(MaterialTagBatchDTO materialTagBatchDTO) {
        List<MaterialTagNewDTO> allBatchList=materialTagBatchDTO.getList();
        Long materialId=materialTagBatchDTO.getMaterialId();
        List<MaterialTagNewDTO> batchList=new ArrayList<>();
        List<Long> fullTagIdList=new ArrayList<>();
        for (MaterialTagNewDTO materialTagNewDTO:allBatchList) {
            Long categoryId=materialTagNewDTO.getCategoryId();
            materialTagNewDTO.setMaterialId(materialId);
            if(ObjectUtil.isEmpty(materialId)||ObjectUtil.isEmpty(categoryId)){
                throw new BizException("素材Id和类目id不能为空");
            }
            if(!CollectionUtils.isEmpty(materialTagNewDTO.getTagIdList())){
                fullTagIdList.addAll(materialTagNewDTO.getTagIdList());
                batchList.add(materialTagNewDTO);
            }
        }
        MaterialTagDO materialTagDO=materialTagGateway.getByMaterialId(materialId);
        boolean tagStatus=ObjectUtil.isNotEmpty(materialTagDO);
        if(!tagStatus){
            materialTagDO=materialTagGateway.save(MaterialTagDO.buildFrom(materialId,materialTagBatchDTO.getRemark()));
        }else{
            if(!ObjectUtils.isEmpty(materialTagDO.getId())){
                materialTagGateway.updateTagRemark(materialTagDO.getId(),materialTagBatchDTO.getRemark());
            }
        }
        if(ObjectUtil.isNotEmpty(materialTagBatchDTO.getMaterialName())){
            OriginMaterialDO originMaterial = originMaterialGateway.getById(materialTagBatchDTO.getMaterialId());
            if(!originMaterial.getMaterialName().equals(materialTagBatchDTO.getMaterialName())){
                originMaterial.setMaterialName(materialTagBatchDTO.getMaterialName());
                originMaterialGateway.updateById(originMaterial);
            }
        }

        List<MaterialTagItemDO> alreadyList=materialTagItemGateway.getBaseList(materialId,null);
        List<Long> toDeleteIds=new ArrayList();
        List<Long> toAddIdList=new ArrayList();
        List<Long> alreadyTagIdList=new ArrayList();
        if(!CollectionUtils.isEmpty(alreadyList)){
            alreadyList.forEach(each->alreadyTagIdList.add(each.getTagId()));
        }
        for(MaterialTagNewDTO materialTagNewDTO:batchList){
            for (Long each:materialTagNewDTO.getTagIdList()) {
                if(!alreadyTagIdList.contains(each)){
                    toAddIdList.add(each);
                }
            }
        }
        if(!CollectionUtils.isEmpty(alreadyTagIdList)){
            for (Long each:alreadyTagIdList) {
                if(!fullTagIdList.contains(each)){
                    toDeleteIds.add(each);
                }
            }
        }
        if(!CollectionUtils.isEmpty(toAddIdList)){
            List<TagInfoDO> list=tagInfoGateway.getListByIdList(toAddIdList);
            if(!CollectionUtils.isEmpty(list)){
                List<MaterialTagItemDO> toAddList=recursionAddTags(materialId,null,materialTagDO.getId(),list);
                materialTagItemGateway.saveBatch(toAddList);
            }
        }
        if(!CollectionUtils.isEmpty(toDeleteIds)){
            materialTagItemGateway.deleteByTagIds(null,materialId,toDeleteIds);
        }
        if(!tagStatus){
            originMaterialGateway.updateTagStatus(materialId, CommonConstants.TAG_STATUS_TAG);
        }
        return true;
    }

    @Override
    public boolean addDownloadCount(Long materialId) {
        if (ObjectUtil.isEmpty(materialId) || materialId == 0) {
            return false;
        }
        return materialTagGateway.addDownloadCount(materialId);
    }

    @Override
    public List<Long> existTagIdList() {
        return materialTagGateway.existTagIdList();
    }
}
