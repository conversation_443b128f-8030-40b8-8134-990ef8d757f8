package cn.com.voyah.material.service;

import cn.com.voyah.material.client.FileService;
import cn.com.voyah.material.client.HistoryTransferService;
import cn.com.voyah.material.client.ThFileService;
import cn.com.voyah.material.config.MaterialConfig;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.gateway.FileDetailGateway;
import cn.com.voyah.material.dto.FileSourceType;
import cn.com.voyah.material.dto.SingleResponse;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.util.MaterialFileUtils;
import cn.com.voyah.material.util.SpireThumbnailUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
@Slf4j
@Service
public class ThFileServiceImpl implements ThFileService {

    @Autowired
    private MaterialConfig materialConfig;
    @Autowired
    private HistoryTransferService historyTransferService;
    @Autowired
    private FileDetailGateway fileDetailGateway;
    @Autowired
    @Qualifier("cosService")
    private FileService fileService;
    private static final List<String> suffixesList=new CopyOnWriteArrayList();
    @Value("${th.generate.content-types:pdf, doc, docx, excel, ppt, txt,xls,xlsx}")
    private List<String> thGenerateContenttypes;

    @Value("${dir.prefix.origin:/opt/work/}")
    private  String dirPrefixOrigin;
    @Value("${dir.prefix.th:/opt/work/}")
    private  String dirPrefixTh;
    public List<String> getThSuffix(){
        if(CollectionUtil.isNotEmpty(suffixesList)){
            return suffixesList;
        }
        List<String> typeKeyList=new ArrayList();
        typeKeyList.add("video");
        typeKeyList.add("picture");
        typeKeyList.add("document");
        List<String> tmpSuffixesList=new ArrayList();
        for (FileSourceType each:materialConfig.getFileSourceTypes()) {
            if(typeKeyList.contains(each.getKey())){
                tmpSuffixesList.addAll(each.getSuffixes());
            }
        }
        suffixesList.addAll(tmpSuffixesList);
        return suffixesList;
    }



    @Override
    public void generateThByFileId(String fileId) {
        FileDetailDO fileDetailDO=fileDetailGateway.getById(fileId);
        if(!thGenerateContenttypes.contains(fileDetailDO.getContentType())){
            return ;
        }
        ThThread thThread=new ThThread(historyTransferService,fileService,fileDetailGateway,fileDetailDO,dirPrefixOrigin,dirPrefixTh);
        ThreadUtil.execute(thThread);
    }

}
