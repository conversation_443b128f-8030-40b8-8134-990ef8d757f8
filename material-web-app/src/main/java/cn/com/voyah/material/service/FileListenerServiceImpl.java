package cn.com.voyah.material.service;

import cn.com.voyah.material.client.FileListenerService;
import cn.com.voyah.material.client.OriginMaterialService;
import cn.com.voyah.material.client.TaskInfoService;
import cn.com.voyah.material.domain.entity.OriginMaterialDO;
import cn.com.voyah.material.domain.event.DomainEvent;
import cn.com.voyah.material.dto.MaterialMoveDTO;
import cn.com.voyah.material.util.JsonUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class FileListenerServiceImpl implements FileListenerService {

    @Autowired
    private OriginMaterialService originMaterialService;
    @Autowired
    private TaskInfoService taskInfoService;
    /**
     * 如果是文件，这增加记录文件的个数
     * @param domainEvent
     */
    @Async
    @Override
    public void processAdd(DomainEvent domainEvent) {
        OriginMaterialDO originMaterialDO= JsonUtil.readValue((String)(domainEvent.getData()), OriginMaterialDO.class);
        originMaterialService.updatePublicStatusRecursion(originMaterialDO.getParentId());
        if(originMaterialDO.getIsDir()){
            return ;
        }
        originMaterialService.updateFileCountRecursion(originMaterialDO);
        if(CollectionUtil.isNotEmpty(originMaterialDO.getParentIdList())&&originMaterialDO.getParentIdList().size()>1){
            taskInfoService.taskFileCountAdd(originMaterialDO.getCreateShortId(),originMaterialDO.getParentIdList().get(1));
        }


    }

    @Async
    @Override
    public void processRemove(DomainEvent domainEvent) {
        OriginMaterialDO originMaterialDO= JsonUtil.readValue((String)(domainEvent.getData()), OriginMaterialDO.class);
        originMaterialService.updatePublicStatusRecursion(originMaterialDO.getParentId());
        // 如果是文件夹并且文件数量为0，直接返回
        if(originMaterialDO.getIsDir() && originMaterialDO.getFileCount() == 0){
            return ;
        }
        originMaterialService.updateFileCountRecursion(originMaterialDO);
    }
    @Override
    public void processUpdatePublic(DomainEvent<?> domainEvent) {
        OriginMaterialDO originMaterialDO= JsonUtil.readValue((String)(domainEvent.getData()), OriginMaterialDO.class);
        originMaterialService.updatePublicStatusRecursion(originMaterialDO.getParentId());

    }
    @Async
    @Override
    public void processMove(DomainEvent<?> domainEvent) {
        MaterialMoveDTO materialMoveDTO= JsonUtil.readValue((String)(domainEvent.getData()), MaterialMoveDTO.class);
        originMaterialService.updatePublicStatusRecursion(materialMoveDTO.getSourceParentId());
        originMaterialService.updatePublicStatusRecursion(materialMoveDTO.getToMaterialId());
        originMaterialService.updateFileCountRecursion(materialMoveDTO.getSourceParentId());
        originMaterialService.updateFileCountRecursion(materialMoveDTO.getToMaterialId());
    }


}
