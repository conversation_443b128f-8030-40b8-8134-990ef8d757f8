package cn.com.voyah.material.service;

import cn.com.voyah.material.client.TagCategoryService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.TagCategoryDO;
import cn.com.voyah.material.domain.entity.TagInfoDO;
import cn.com.voyah.material.domain.entity.migrate.TagBatchSaveEntity;
import cn.com.voyah.material.domain.gateway.TagCategoryGateway;
import cn.com.voyah.material.domain.gateway.TagInfoGateway;
import cn.com.voyah.material.dto.TagCategoryDTO;
import cn.com.voyah.material.dto.TagCategoryTreeDTO;
import cn.com.voyah.material.dto.TagInfoDTO;
import cn.com.voyah.material.dto.TagInfoMergeDTO;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.query.TagCategoryQuery;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.util.PageUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static cn.com.voyah.domain.entity.def.TagCategoryDef.TAG_CATEGORY;
@Service
public class TagCategoryServiceImpl implements TagCategoryService {

    @Autowired
    private TagCategoryGateway tagCategoryGateway;
    @Autowired
    private TagInfoGateway tagInfoGateway;
    @Override
    public Page<TagCategoryDTO> page(PageQueryCondition<TagCategoryQuery> page) {
        TagCategoryQuery tagCategoryQuery=page.getCondition();
        TagInfoDO tagInfo=new TagInfoDO();
        tagInfo.setLevel(1);
        tagInfo.setIsDelete(false);
        String queryTagValue=null;
        if(ObjectUtil.isNotEmpty(tagCategoryQuery)&&!StringUtils.isEmpty(tagCategoryQuery.getTagValue())){
            queryTagValue=tagCategoryQuery.getTagValue();
            tagInfo.setTagValue(queryTagValue);
        }
        List<TagInfoDO> tagList=tagInfoGateway.list(tagInfo);
        tagInfoGateway.setHiddenStatus(tagList);
        Set<Long> categroyIdSet=new HashSet<>();
        if(ObjectUtil.isNotEmpty(queryTagValue)){
            if(CollectionUtil.isEmpty(tagList)){
                return Page.of(page.getPageIndex(),page.getPageSize(),0);
            }else{
                categroyIdSet=tagList.stream().map(each->each.getCategoryId()).collect(Collectors.toSet());
            }
        }
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_CATEGORY.ALL_COLUMNS)
                .from(TAG_CATEGORY)
                .where(TAG_CATEGORY.IS_DELETE.eq(false))
                .orderBy(page.getOrderBy(), CommonConstants.ASC.equals(page.getOrderDirection()));
        if(CollectionUtil.isNotEmpty(categroyIdSet)){
            query.and(TAG_CATEGORY.ID.in(categroyIdSet));
        }
        if(ObjectUtil.isNotEmpty(tagCategoryQuery)&&ObjectUtil.isNotEmpty(tagCategoryQuery.getStatus())){
            query.and(TAG_CATEGORY.STATUS.eq(tagCategoryQuery.getStatus()));
        }
        Page<TagCategoryDO> pageResult=tagCategoryGateway.page(PageUtil.of(page),query);
        return convertToPageDTOResult(pageResult,tagList);
    }



    public Page<TagCategoryDTO> convertToPageDTOResult(Page<TagCategoryDO> pageResult,List<TagInfoDO> tagList){
        Page<TagCategoryDTO> result=new Page<>(pageResult.getPageNumber(),pageResult.getPageSize(),pageResult.getTotalRow());
        result.setRecords(convertToTagDtoList(pageResult.getRecords(),tagList));
        return result;
    }

    public List<TagCategoryDTO> convertToTagDtoList(List<TagCategoryDO> tagCategoryDOList,List<TagInfoDO> tagBaseList){
        if(CollectionUtils.isEmpty(tagCategoryDOList)){
            return List.of();
        }
        Map<Long,List<TagInfoDTO>> categoryIdMap=new HashMap();
        for (TagInfoDO each:tagBaseList) {
            categoryIdMap.computeIfAbsent(each.getCategoryId(),k->new ArrayList<TagInfoDTO>());
            TagInfoDTO tagInfoDTO=new TagInfoDTO();
            BeanUtils.copyProperties(each,tagInfoDTO);
            categoryIdMap.get(each.getCategoryId()).add(tagInfoDTO);
        }
        List<TagCategoryDTO> tagCategoryDTOList=new ArrayList<>();
        for (TagCategoryDO each:tagCategoryDOList) {
            tagCategoryDTOList.add(TagCategoryDO.buildToDTO(each,categoryIdMap.get(each.getId())));
        }
        return tagCategoryDTOList;
    }

    @Override
    public void batchSave(TagBatchSaveEntity tagBatchSaveEntity) {
        List<TagInfoDO> level1List=queryList(tagBatchSaveEntity.getCategoryId(),tagBatchSaveEntity.getQueryLevel1(),tagBatchSaveEntity.getQueryLevel1Value(),List.of());
        if(CollectionUtil.isEmpty(level1List)){
            return ;
        }
        Long categoryId=tagBatchSaveEntity.getCategoryId();
        List<Long> parentIdList=level1List.stream().map(each->each.getId()).collect(Collectors.toList());
        List<TagInfoDO> level2List=queryList(tagBatchSaveEntity.getCategoryId(),tagBatchSaveEntity.getQueryLevel2(),tagBatchSaveEntity.getQueryLevel2Value(),parentIdList);
        if(CollectionUtil.isEmpty(level2List)){
            return ;
        }
        Integer level2=tagBatchSaveEntity.getQueryLevel2();
        Integer addToLevel=tagBatchSaveEntity.getAddToLevel();
        int i=level2+1;
        List<TagInfoDO> tmpLevelList=level2List;
        parentIdList=tmpLevelList.stream().map(each->each.getId()).collect(Collectors.toList());
        for(;i<addToLevel;i++){
            tmpLevelList=queryList(categoryId,i,null,parentIdList);
            parentIdList=tmpLevelList.stream().map(each->each.getId()).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(parentIdList)){
                return ;
            }
        }
        List<TagInfoDO> addTagList=new ArrayList();
        for(TagInfoDO each:tmpLevelList){
            List<TagInfoDO> alreadyList=queryList(categoryId,addToLevel,tagBatchSaveEntity.getLevelName(),tagBatchSaveEntity.getValues(),List.of(each.getId()));
            List<String> tagsValueList=new ArrayList<>();
            if(!CollectionUtil.isEmpty(alreadyList)){
                tagsValueList=alreadyList.stream().map(item->item.getTagValue()).collect(Collectors.toList());
            }
            for (String value: tagBatchSaveEntity.getValues()) {
                if(CollectionUtil.isEmpty(tagsValueList)||!tagsValueList.contains(value)){
                    TagInfoDO tagInfoDO=new TagInfoDO();
                    tagInfoDO.setCategoryId(categoryId);
                    tagInfoDO.setLevel(addToLevel);
                    tagInfoDO.setName(tagBatchSaveEntity.getLevelName());
                    tagInfoDO.setCode(tagBatchSaveEntity.getLevelName());
                    tagInfoDO.setTagValue(value);
                    tagInfoDO.setParentId(each.getId());
                    addTagList.add(tagInfoDO);
                }
            }
        }
        if(!CollectionUtil.isEmpty(addTagList)){
            tagInfoGateway.saveBatch(addTagList);
        }

    }

    public List<TagInfoDO> queryList(Long categoryId,Integer level,String value,List<Long> parentIdList){
        TagInfoDO queryTagInfoDO1=new TagInfoDO();
        queryTagInfoDO1.setCategoryId(categoryId);
        queryTagInfoDO1.setLevel(level);
        queryTagInfoDO1.setTagValue(value);
        List<TagInfoDO> level1List=tagInfoGateway.getList(queryTagInfoDO1,parentIdList);
        if(CollectionUtil.isEmpty(level1List)){
            return List.of();
        }
        return level1List;
    }
    public List<TagInfoDO> queryList(Long categoryId,Integer level,String name,List<String> values,List<Long> parentIdList){
        List<TagInfoDO> levelList=tagInfoGateway.getList(categoryId,level,name,values,parentIdList);
        if(CollectionUtil.isEmpty(levelList)){
            return List.of();
        }
        return levelList;
    }


    @Override
    public void batchSave2(TagBatchSaveEntity tagBatchSaveEntity) {
        Long categoryId=tagBatchSaveEntity.getCategoryId();
        Integer addToLevel=tagBatchSaveEntity.getAddToLevel();
        if(tagBatchSaveEntity.getAddToLevel()==1){
            List<TagInfoDO> addTagList=new ArrayList();
            List<TagInfoDO> alreadyList=queryList(categoryId,addToLevel,tagBatchSaveEntity.getLevelName(),tagBatchSaveEntity.getValues(),List.of(0L));
            List<String> tagsValueList=new ArrayList<>();
            if(!CollectionUtil.isEmpty(alreadyList)){
                tagsValueList=alreadyList.stream().map(item->item.getTagValue()).collect(Collectors.toList());
            }
            for (String value: tagBatchSaveEntity.getValues()) {
                if(CollectionUtil.isEmpty(tagsValueList)||!tagsValueList.contains(value)){
                    TagInfoDO tagInfoDO=new TagInfoDO();
                    tagInfoDO.setCategoryId(categoryId);
                    tagInfoDO.setLevel(addToLevel);
                    tagInfoDO.setName(tagBatchSaveEntity.getLevelName());
                    tagInfoDO.setCode(tagBatchSaveEntity.getLevelName());
                    tagInfoDO.setTagValue(value);
                    tagInfoDO.setParentId(0L);
                    addTagList.add(tagInfoDO);
                }
            }
            if(!CollectionUtil.isEmpty(addTagList)){
                tagInfoGateway.saveBatch(addTagList);
            }
        }

    }

    @Override
    public List<TagCategoryDO> getList(TagCategoryDO tagCategoryDO) {
        return tagCategoryGateway.getList(tagCategoryDO);
    }

    @Override
    public TagCategoryDO save(TagCategoryDO tagCategoryDO) {
        if(StringUtils.isEmpty(tagCategoryDO.getName())){
            throw new BizException("标签类目名称不能为空!");
        }
        List<TagCategoryDO> list=tagCategoryGateway.getList(tagCategoryDO);
        if(CollectionUtil.isNotEmpty(list)){
            throw new BizException("标签类目名称已存在!");
        }

        return tagCategoryGateway.save(tagCategoryDO);
    }

    @Override
    public Boolean updateById(TagCategoryDO tagCategoryDO) {
        if(StringUtils.isEmpty(tagCategoryDO.getName())){
            throw new BizException("标签类目名称不能为空!");
        }
        List<TagCategoryDO> list=tagCategoryGateway.getList(tagCategoryDO);
        if(CollectionUtil.isNotEmpty(list)){
            Optional option=list.stream().filter(each->!each.getId().equals(tagCategoryDO.getId())).findAny();
            if(option.isPresent()){
                throw new BizException("标签类目名称已存在!");
            }
        }

        return tagCategoryGateway.updateById(tagCategoryDO);
    }

    @Override
    public List<TagCategoryTreeDTO> getAllTagMergeTreeList(Long categoryId) {
        List<TagInfoDO> allList=tagInfoGateway.getList(categoryId);
        if(CollectionUtil.isEmpty(allList)){
            return List.of();
        }
        Map<Long,List<TagInfoDO>> categoryIdListMap=new HashMap();
        for (TagInfoDO each:allList) {
            if(ObjectUtil.isNotEmpty(each.getCategoryId())){
                categoryIdListMap.putIfAbsent(each.getCategoryId(), new ArrayList<>());
                categoryIdListMap.get(each.getCategoryId()).add(each);
            }
        }

        List<TagCategoryTreeDTO> mergeList=new ArrayList<>();
        for (Map.Entry<Long, List<TagInfoDO>> entry:categoryIdListMap.entrySet()) {
            Long categoryIdKey=entry.getKey();
            TagCategoryDO tagCategoryDO=tagCategoryGateway.getById(categoryIdKey);
            if(ObjectUtil.isNotEmpty(tagCategoryDO)){
                TagCategoryTreeDTO each=convertToTagCategoryTreeDTO(tagCategoryDO,entry.getValue());
                mergeList.add(each);
            }

        }
        return mergeList;
    }


    public TagCategoryTreeDTO convertToTagCategoryTreeDTO(TagCategoryDO tagCategoryDO,List<TagInfoDO> tagBaseList){
        if(CollectionUtils.isEmpty(tagBaseList)){
            return null;
        }
        TagCategoryTreeDTO tagCategoryTreeDTO=new TagCategoryTreeDTO();
        BeanUtils.copyProperties(tagCategoryDO,tagCategoryTreeDTO);
        List<TagInfoMergeDTO> list=convertListToMergeList(tagBaseList);
        tagCategoryTreeDTO.setTagMergeList(list);
        return tagCategoryTreeDTO;
    }

    public List<TagInfoMergeDTO> convertListToMergeList(List<TagInfoDO> tagBaseList) {
        Map<String,List<TagInfoDO>> map=new HashMap();
        List<String> nameLevelList=new ArrayList<>();
        if(CollectionUtil.isNotEmpty(tagBaseList)){
            for (TagInfoDO each:tagBaseList) {
                String key=each.getName()+CommonConstants.FILE_DUPLICATION_SEPRATE+each.getLevel();
                if(!nameLevelList.contains(key)){
                    map.putIfAbsent(key,new ArrayList<>());
                    nameLevelList.add(key);
                }
                map.get(key).add(each);
            }
        }
        List<TagInfoMergeDTO> mergeList=new ArrayList<>();
        for (String nameLevelKey:nameLevelList) {
            List<TagInfoDO> taskInfoList=map.get(nameLevelKey);
            TagInfoMergeDTO tagInfoMergeDTO=new TagInfoMergeDTO();
            tagInfoMergeDTO.setTagName(taskInfoList.get(0).getName());
            tagInfoMergeDTO.setLevel(taskInfoList.get(0).getLevel());
            tagInfoMergeDTO.setTagList(TagInfoDO.buildToBaseList(taskInfoList));
            mergeList.add(tagInfoMergeDTO);
        }
        return mergeList;
    }



}
