package cn.com.voyah.material.service;

import cn.com.voyah.material.client.TagInfoService;
import cn.com.voyah.material.domain.entity.TagInfoDO;
import cn.com.voyah.material.domain.gateway.TagInfoGateway;
import cn.com.voyah.material.dto.query.TagInfoQuery;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.com.voyah.domain.entity.def.TagInfoDef.TAG_INFO;

@Service
public class TagInfoServiceImpl implements TagInfoService {

    @Autowired
    private TagInfoGateway tagInfoGateway;


    @Override
    public Map<String,List<TagInfoDO>> getByParentIdList2(TagInfoQuery tagInfoQuery) {
        List<TagInfoDO> list=tagInfoGateway.getByParentIdList(tagInfoQuery);
        if(CollectionUtil.isEmpty(list)){
            return Map.of();
        }
        Map<String,List<TagInfoDO>> map=new HashMap<>();
        for (TagInfoDO each:list) {
            String key=each.getTagValue();
            map.putIfAbsent(key,new ArrayList<TagInfoDO>());
            map.get(key).add(each);
        }
        return map;
    }

    @Override
    public List<TagInfoMergeVO> getAllTagMergeVoList(Long categoryId,boolean filterHiden) {
        List<TagInfoDO> allList=tagInfoGateway.getTagNameAndValueList(categoryId,filterHiden);
        if(CollectionUtil.isEmpty(allList)){
            return List.of();
        }
        Map<Long,List<TagInfoDO>> categoryIdListMap=new HashMap();
        for (TagInfoDO each:allList) {
            if(ObjectUtil.isNotEmpty(each.getCategoryId())){
                categoryIdListMap.putIfAbsent(each.getCategoryId(), new ArrayList<>());
                categoryIdListMap.get(each.getCategoryId()).add(each);
            }
        }

        List<TagInfoMergeVO> mergeList=new ArrayList<>();
        for (Map.Entry<Long, List<TagInfoDO>> entry:categoryIdListMap.entrySet()) {
            Long categoryIdKey=entry.getKey();
            Map<String,Set<String>> nameValueSetmap=new HashMap<>();
            List<String> nameList=new ArrayList<String>();
            for (TagInfoDO each:entry.getValue()) {
                if(nameList.contains(each.getName())){
                    nameValueSetmap.get(each.getName()).add(each.getTagValue());
                }else{
                    nameList.add(each.getName());
                    HashSet set=new HashSet();
                    set.add(each.getTagValue());
                    nameValueSetmap.put(each.getName(),set);
                }
            }
            for (String name:nameList) {
                TagInfoMergeVO tagInfoMergeVO=new TagInfoMergeVO();
                tagInfoMergeVO.setTagName(name);
                tagInfoMergeVO.setCategoryId(categoryIdKey);
                tagInfoMergeVO.setTagValueList(nameValueSetmap.get(name));
                mergeList.add(tagInfoMergeVO);
            }
            
        }
        return mergeList;
    }

    @Override
    public List<TagInfoDO> likeValueList(String query) {
        QueryWrapper wrapper =QueryWrapper.create().where(TAG_INFO.TAG_VALUE.like(query).and(TAG_INFO.IS_DELETE.eq(false)));
        List<TagInfoDO> list = tagInfoGateway.list(wrapper);
        return list;
    }



}
