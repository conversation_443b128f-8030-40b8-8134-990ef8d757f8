package cn.com.voyah.material.service;


import cn.com.voyah.material.client.MaterialAuthService;
import cn.com.voyah.material.client.MaterialTagService;
import cn.com.voyah.material.client.OriginMaterialService;
import cn.com.voyah.material.config.MaterialUserPlatformConfig;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.entity.MaterialTagItemDO;
import cn.com.voyah.material.domain.entity.OriginMaterialDO;
import cn.com.voyah.material.domain.gateway.FileDetailGateway;
import cn.com.voyah.material.domain.gateway.MaterialLibraryGateway;
import cn.com.voyah.material.domain.gateway.OriginMaterialGateway;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.mapper.OriginMaterialMapper;
import cn.com.voyah.material.mybatisflex.query.QueryExt;
import cn.com.voyah.material.util.AiChatUtils;
import cn.com.voyah.material.util.HanLPUtils;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.utils.RedisKeys;
import cn.com.voyah.material.utils.RedisUtil;
import cn.com.voyah.material.vo.user.OriginMaterialExtensionVO;
import cn.com.voyah.material.vo.user.OriginMaterialInfoV2VO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.properties.IncInnerUserInfo;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.query.SelectQueryColumn;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static cn.com.voyah.domain.entity.def.FileDetailDef.FILE_DETAIL;
import static cn.com.voyah.domain.entity.def.MaterialFavoriteDef.MATERIAL_FAVORITE;
import static cn.com.voyah.domain.entity.def.MaterialTagDef.MATERIAL_TAG;
import static cn.com.voyah.domain.entity.def.MaterialTagItemDef.MATERIAL_TAG_ITEM;
import static cn.com.voyah.domain.entity.def.OriginMaterialDef.ORIGIN_MATERIAL;
import static com.mybatisflex.core.query.QueryMethods.groupConcat;
import static com.mybatisflex.core.query.QueryMethods.select;

@Service
@AllArgsConstructor
@Slf4j
public class OriginMaterialServiceImpl implements OriginMaterialService {

    private OriginMaterialGateway originMaterialGateway;

    private MaterialLibraryGateway materialLibraryGateway;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private RedisKeys redisKeys;
    @Autowired
    private MaterialAuthService materialAuthService;

    @Autowired
    private MaterialTagService materialTagService;

    @Autowired
    private MaterialUserPlatformConfig materialUserPlatformConfig;

    @Autowired
    private OriginMaterialMapper originMaterialMapper;

    @Autowired
    private FileDetailGateway fileDetailGateway;

    @Override
    public OriginMaterialDO save(OriginMaterialDTO originMaterialDTO) {
        if (ObjectUtil.isNotEmpty(originMaterialDTO.getParentId()) && originMaterialDTO.getParentId() != 0) {
            OriginMaterialDO parent = originMaterialGateway.getById(originMaterialDTO.getParentId());
            if (ObjectUtil.isNotEmpty(parent)) {
                List<Long> parentList = parent.getParentIdList();
                if (CollectionUtil.isEmpty(parentList)) {
                    parentList = new ArrayList<>();
                }
                parentList.add(parent.getId());
                originMaterialDTO.setParentIdList(parentList);
                originMaterialDTO.setLevel(parent.getLevel() + 1);
                originMaterialDTO.setLibraryId(parent.getLibraryId());
            }
        } else {
            originMaterialDTO.setLevel(0);
        }
        originMaterialDTO.setIsShow(CommonConstants.ENABLE);
        originMaterialDTO.setTagStatus(CommonConstants.TAG_STATUS_UNTAG);
        // 文件夹上传，重新构建重复的名称
        OriginMaterialExtensionDTO extensionDTO = new OriginMaterialExtensionDTO();
        extensionDTO.setOriginalFilename(originMaterialDTO.getMaterialName());
        extensionDTO.setParentId(originMaterialDTO.getParentId());
        OriginMaterialExtensionDTO generateFileNameExtensionDTO = this.generateFileName(extensionDTO);
        originMaterialDTO.setMaterialName(generateFileNameExtensionDTO.getMaterialName());
//        if (originMaterialDTO.getIsDir() && existFileName(originMaterialDTO.getMaterialName(), originMaterialDTO.getParentId())) {
//            Integer source = originMaterialDTO.getSource();
//            if (originMaterialDTO.getSource() == null || source == 1) {
//                throw new BizException("文件名或者文件夹名称已存在!");
//            } else if (originMaterialDTO.getSource() == 2) {
//                // 文件夹上传，重新构建重复的名称
//                OriginMaterialExtensionDTO extensionDTO = new OriginMaterialExtensionDTO();
//                extensionDTO.setOriginalFilename(originMaterialDTO.getMaterialName());
//                extensionDTO.setParentId(originMaterialDTO.getParentId());
//                OriginMaterialExtensionDTO generateFileNameExtensionDTO = this.generateFileName(extensionDTO);
//                originMaterialDTO.setMaterialName(generateFileNameExtensionDTO.getMaterialName());
//            }
//        }
        if (originMaterialDTO.getIsDir()) {
            originMaterialDTO.setFileCount(0L);
        }
        originMaterialDTO.setTagStatus(CommonConstants.TAG_STATUS_UNTAG);
        originMaterialDTO.setIsPublic(CommonConstants.PUBLIC_STATUS_PUBLIC);
        return originMaterialGateway.save(OriginMaterialDO.buildFrom(originMaterialDTO));
    }

    @Override
    public PageResponse<OriginMaterialExtensionDTO> getPageInfo(PageQueryCondition<OriginMaterialExtensionDTO> pageQuery) {
        List<Long> authMaterialIdList = materialAuthService.getAuthParentIdList();
        if (CollectionUtil.isEmpty(authMaterialIdList)) {
            return PageResponse.of(List.of(), 0, pageQuery.getPageSize(), pageQuery.getPageIndex());
        }
        if (ObjectUtils.isEmpty(pageQuery.getCondition().getParentId())) {
            return PageUtil.of(originMaterialGateway.getPageInfo(PageUtil.of(pageQuery), pageQuery.getCondition(), authMaterialIdList));
        }
        Long parentId = pageQuery.getCondition().getParentId();
        if (authMaterialIdList.contains(pageQuery.getCondition().getParentId())) {
            return PageUtil.of(originMaterialGateway.getPageInfo(PageUtil.of(pageQuery), pageQuery.getCondition(), List.of()));
        }
        if (CommonConstants.HOME_FOLD_PARENT_ID.equals(parentId)) {
            return PageUtil.of(originMaterialGateway.getPageInfo(PageUtil.of(pageQuery), pageQuery.getCondition(), authMaterialIdList));
        }
        OriginMaterialDO originMaterial = originMaterialGateway.getById(parentId);
        if (ObjectUtil.isEmpty(originMaterial)) {
            return PageResponse.of(List.of(), 0, pageQuery.getPageSize(), pageQuery.getPageIndex());
        }

        if (CollectionUtil.isNotEmpty(originMaterial.getParentIdList())) {
            for (Long each : originMaterial.getParentIdList()) {
                if (authMaterialIdList.contains(each)) {
                    return PageUtil.of(originMaterialGateway.getPageInfo(PageUtil.of(pageQuery), pageQuery.getCondition(), List.of()));
                }
            }
        }
        return PageResponse.of(List.of(), 0, pageQuery.getPageSize(), pageQuery.getPageIndex());
    }

    @Override
    public JSONObject getAiName(Long id) {
        OriginMaterialDO originMaterial = originMaterialGateway.getById(id);
        if (originMaterial.getIsDir()) {
            throw new BizException("请选中素材，无法给文件夹生成");
        }
        List<MaterialTagItemDO> tagItemDOList = materialTagService.getBindTagsByMaterialId(id, null);
        FileDetailDO fileDetail = fileDetailGateway.getById(originMaterial.getFileId());

        List<String> collect = tagItemDOList.stream().map(MaterialTagItemDO::getTagValue).collect(Collectors.toList());
        String tag = CollectionUtil.isEmpty(collect) ? null : CollectionUtil.join(collect, ",");
        return AiChatUtils.getTagName(tag, fileDetail.getContentType());
    }

    @Override
    public JSONObject getAiName(String tag) {
        return AiChatUtils.getTagName(tag);
    }

    @Override
    public JSONObject getAiDesc(Long id) {
        List<MaterialTagItemDO> tagItemDOList = materialTagService.getBindTagsByMaterialId(id, null);
        List<String> collect = tagItemDOList.stream().map(MaterialTagItemDO::getTagValue).collect(Collectors.toList());
        String tag = CollectionUtil.isEmpty(collect) ? null : CollectionUtil.join(collect, ",");
        return AiChatUtils.getTagDesc(tag);
    }

    @Override
    public JSONObject getAiDesc(String tag) {
        return AiChatUtils.getTagDesc(tag);
    }

    @Override
    public PageResponse<OriginMaterialExtensionDTO> getGlobalSearchPageInfo(PageQueryCondition<GlobalOriginMaterialQuery> pageQuery, TimeQueryInterval createTimeQueryInterval) {
        return this.getGlobalSearchPageInfo(pageQuery, createTimeQueryInterval, null);
    }

    @Override

    public PageResponse<OriginMaterialExtensionDTO> getGlobalSearchPageInfo(PageQueryCondition<GlobalOriginMaterialQuery> pageQuery, TimeQueryInterval createTimeQueryInterval, TimeQueryInterval tagCreateTimeQueryInterval) {
        List<Long> authMaterialIdList = materialAuthService.getAuthParentIdList();
        if (CollectionUtil.isEmpty(authMaterialIdList)) {
            return PageResponse.of(List.of(), 0, pageQuery.getPageSize(), pageQuery.getPageIndex());
        }
        GlobalOriginMaterialQuery condition = pageQuery.getCondition();
        QueryWrapper query = QueryWrapper.create();
        query.select(ORIGIN_MATERIAL.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL, FILE_DETAIL.UPLOAD_ID, FILE_DETAIL.UPLOAD_STATUS
                        , FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL, FILE_DETAIL.TH_CONTENT_TYPE,
                        MATERIAL_TAG.CREATE_BY.as("tag_create_by"),
                        MATERIAL_TAG.CREATE_SHORT_ID.as("tag_create_short_id"),
                        MATERIAL_TAG.CREATE_TIME.as("tag_create_time")
                )
                .from(ORIGIN_MATERIAL)
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(ORIGIN_MATERIAL.FILE_ID))
                .leftJoin(MATERIAL_TAG.getTableName()).on(MATERIAL_TAG.MATERIAL_ID.eq(ORIGIN_MATERIAL.ID))
                .where(ORIGIN_MATERIAL.IS_SHOW.eq(CommonConstants.ENABLE))
                // 主文件夹不展示
                .and(ORIGIN_MATERIAL.LEVEL.gt(0))
                .orderBy(pageQuery.getOrderBy(), CommonConstants.ASC.equals(pageQuery.getOrderDirection()));
        if (Objects.nonNull(condition.getTagStatus())) {
            query.and(ORIGIN_MATERIAL.TAG_STATUS.eq(condition.getTagStatus()));
        }
        if (Objects.nonNull(condition.getIsDir())) {
            query.and(ORIGIN_MATERIAL.IS_DIR.eq(condition.getIsDir()));
        }
        if (Objects.nonNull(condition.getIsPublic())) {
            query.and(ORIGIN_MATERIAL.IS_PUBLIC.eq(condition.getIsPublic()));
        }
        // 修改时间查询
        if (Objects.nonNull(createTimeQueryInterval) && Objects.nonNull(createTimeQueryInterval.getStartTime()) && Objects.nonNull(createTimeQueryInterval.getEndTime())) {
            query.and(ORIGIN_MATERIAL.CREATE_TIME.ge(createTimeQueryInterval.getStartTime()))
                    .and(ORIGIN_MATERIAL.CREATE_TIME.le(createTimeQueryInterval.getEndTime()));
        }
        if (Objects.nonNull(tagCreateTimeQueryInterval) && Objects.nonNull(tagCreateTimeQueryInterval.getStartTime()) && Objects.nonNull(tagCreateTimeQueryInterval.getEndTime())) {
            query.and(MATERIAL_TAG.CREATE_TIME.ge(tagCreateTimeQueryInterval.getStartTime()))
                    .and(MATERIAL_TAG.CREATE_TIME.le(tagCreateTimeQueryInterval.getEndTime()));
        }

        // 文件类型查询
        List<String> matchSuffixesList = condition.getFileTypes();
        if (CollectionUtil.isNotEmpty(matchSuffixesList)) {
            query.and(ORIGIN_MATERIAL.FILE_TYPE.in(matchSuffixesList));
        }
        //如果主文件夹不为空，则不做冗余过滤
        if (!ObjectUtil.isEmpty(condition.getHomeFolderId())) {
            Long homeFolderId = condition.getHomeFolderId();
            // 主文件查询
            query.and(QueryExt.conditionJsonContains(homeFolderId));
        } else {
            if (CollectionUtil.isNotEmpty(authMaterialIdList)) {
                query.and(ORIGIN_MATERIAL.ID.in(authMaterialIdList).or(QueryExt.conditionJsonContains(authMaterialIdList)));
            }
        }
        if (StringUtils.hasLength(condition.getQueryParam1())) {
            // 文件名/创建人或者工号
            query.and(ORIGIN_MATERIAL.MATERIAL_NAME.like(condition.getQueryParam1())
                    .or(ORIGIN_MATERIAL.CREATE_BY.like(condition.getQueryParam1()))
                    .or(ORIGIN_MATERIAL.CREATE_SHORT_ID.like(condition.getQueryParam1())));
        }
        if (StringUtils.hasLength(condition.getTagCreateQueryParam())) {
            // 文件名/创建人或者工号
            query.and(MATERIAL_TAG.CREATE_BY.like(condition.getTagCreateQueryParam())
                    .or(MATERIAL_TAG.CREATE_SHORT_ID.like(condition.getTagCreateQueryParam())));
        }

        Page<OriginMaterialExtensionDTO> page = PageUtil.of(pageQuery);
        return PageUtil.of(originMaterialGateway.pageAs(page, query, OriginMaterialExtensionDTO.class));
    }

    @Override
    public OriginMaterialExtensionDTO getDetailById(Long id) {
        return originMaterialGateway.getDetailById(id);
    }

    @Override
    public Boolean updateById(OriginMaterialDO originMaterialDO) {
        OriginMaterialDO originMaterialDOBefore = originMaterialGateway.getById(originMaterialDO.getId());
        if (ObjectUtil.isEmpty(originMaterialDOBefore)) {
            throw new BizException("该文件不存在!");
        }
//        Long parentId = originMaterialDOBefore.getParentId();
//        if (originMaterialDOBefore.getIsDir() && existExcludeDir(originMaterialDO.getMaterialName(), parentId, originMaterialDO.getId())) {
//            throw new BizException("该文件夹已存在!");
//        }
        // 文件夹上传，重新构建重复的名称
        OriginMaterialExtensionDTO extensionDTO = new OriginMaterialExtensionDTO();
        extensionDTO.setOriginalFilename(originMaterialDO.getMaterialName());
        extensionDTO.setParentId(originMaterialDO.getParentId());
        OriginMaterialExtensionDTO generateFileNameExtensionDTO = this.generateFileName(extensionDTO);
        originMaterialDO.setMaterialName(generateFileNameExtensionDTO.getMaterialName());
        return originMaterialGateway.updateEntity(originMaterialDO) > 0;
    }

    @Override
    public List<OriginMaterialExtensionDTO> getList(OriginMaterialExtensionDTO originMaterialExtensionDTO, Boolean isSuper) {
        List<Long> authMaterialIdList = new ArrayList<>();
        if (!isSuper) {
            authMaterialIdList = materialAuthService.getAuthParentIdList();
            if (CollectionUtil.isEmpty(authMaterialIdList)) {
                return List.of();
            }
        }
        return originMaterialGateway.getList(originMaterialExtensionDTO, authMaterialIdList);
    }

    @Override
    public List<OriginMaterialDO> getParentList(Long id) {
        List<OriginMaterialDO> result = getEachParent(id);
        if (!CollectionUtil.isEmpty(result)) {
            return CollectionUtil.reverse(result);
        }
        return Collections.emptyList();
    }

    @Override
    public OriginMaterialExtensionDTO generateFileName(OriginMaterialExtensionDTO originMaterialExtensionDTO) {
        String originName = originMaterialExtensionDTO.getOriginalFilename();
        if (StringUtils.isEmpty(originName)) {
            throw new BizException("文件名不能为空");
        }
        int index = originName.lastIndexOf(CommonConstants.FILE_SEPRATE_SUFFIX);
        String prefix;
        String suffix = "";
        if (index > 0) {
            prefix = originName.substring(0, index);
            suffix = originName.substring(index);
        } else {
            prefix = originName;
        }
        String finalName = originName;
        Long parentId = originMaterialExtensionDTO.getParentId();
        OriginMaterialExtensionDTO originMaterialDO = new OriginMaterialExtensionDTO();
        originMaterialDO.setParentId(parentId);
        boolean existFlag = false;
        int i = 0;
        do {
            if (i > 0) {
                StringBuilder stringBuilder = new StringBuilder(prefix);
                stringBuilder.append(CommonConstants.FILE_DUPLICATION_SEPRATE);
                stringBuilder.append(i);
                if (!StringUtils.isEmpty(suffix)) {
                    stringBuilder.append(suffix);
                }
                finalName = stringBuilder.toString();
            }
            existFlag = existFileName(finalName);
            i++;
        } while (existFlag);
        originMaterialDO.setMaterialName(finalName);
        String fileNameKey = getFileNameRedisKey(finalName, parentId);
        redisUtil.set(fileNameKey, System.currentTimeMillis(), redisKeys.getDefaultExpire());
        return originMaterialDO;
    }

    @Override
    public void foldUpload(Long parentId, List<OriginMaterialExtensionDTO> list) {

    }

    @Override
    public void updateFileCountRecursion(Long parentId) {
        if (parentId == null ||
                parentId == 0) {
            return;
        }
        OriginMaterialDO originMaterialDO = null;
        do {
            // 更新父ID文件数量
            this.updateParentFileCount(parentId);
            originMaterialDO = originMaterialGateway.getById(parentId);

            // 如果元素存在父目录，赋值 parentId
            parentId = originMaterialDO != null ? originMaterialDO.getParentId() : null;
        } while (parentId != null && parentId != 0);
    }

    @Override
    public void updateFileCountRecursion(OriginMaterialDO originMaterialDO) {
        if (originMaterialDO == null || originMaterialDO.getParentId() == null ||
                originMaterialDO.getParentId() == 0) {
            return;
        }
        updateFileCountRecursion(originMaterialDO.getParentId());
    }

    private void updateParentFileCount(Long parentId) {
        if (ObjectUtil.isNotEmpty(parentId) && parentId != 0) {
            originMaterialGateway.updateChildFileCountById(parentId);
        }
    }

    private boolean existExclude(String materialName, Long parentId, Long id) {
        QueryWrapper query = QueryWrapper.create();
        query.from(ORIGIN_MATERIAL.getTableName())
                .where(ORIGIN_MATERIAL.PARENT_ID.eq(parentId)
                        .and(ORIGIN_MATERIAL.MATERIAL_NAME.eq(materialName))
                        .and(ORIGIN_MATERIAL.ID.ne(id)));
        long count = originMaterialGateway.count(query);
        return count > 0;
    }

    private boolean existExcludeDir(String materialName, Long parentId, Long id) {
        QueryWrapper query = QueryWrapper.create();
        query.from(ORIGIN_MATERIAL.getTableName())
                .where(ORIGIN_MATERIAL.PARENT_ID.eq(parentId)
                        .and(ORIGIN_MATERIAL.MATERIAL_NAME.eq(materialName))
                        .and(ORIGIN_MATERIAL.IS_DIR.eq(true))
                        .and(ORIGIN_MATERIAL.ID.ne(id)));
        long count = originMaterialGateway.count(query);
        return count > 0;
    }

    private boolean existFileName(String materialName, Long parentId) {
        return existFileNameDb(materialName, parentId) || existFileNameInRedis(materialName, parentId);
    }

    private boolean existFileName(String materialName) {
        return existFileNameDb(materialName, null) || existFileNameInRedis(materialName, null);
    }

    private boolean existFileNameDb(String materialName, Long parentId) {
        QueryWrapper query = QueryWrapper.create();
        query.from(ORIGIN_MATERIAL.getTableName())
                .where(ORIGIN_MATERIAL.PARENT_ID.eq(parentId)
                        .and(ORIGIN_MATERIAL.MATERIAL_NAME.eq(materialName)));
        long count = originMaterialGateway.count(query);
        return count > 0;
    }

    private String getFileNameRedisKey(String materialName, Long parentId) {
        return String.format(redisKeys.getFileNameKey(), parentId, materialName);
    }

    private boolean existFileNameInRedis(String materialName, Long parentId) {
        String fileNameKey = getFileNameRedisKey(materialName, parentId);
        return redisUtil.hasKey(fileNameKey);
    }

    private List<OriginMaterialDO> getEachParent(Long id) {
        List<OriginMaterialDO> list = new ArrayList<>();
        OriginMaterialDO originMaterialDO = originMaterialGateway.getById(id);
        if (ObjectUtil.isNotEmpty(originMaterialDO)) {
            if (originMaterialDO.getIsDir()) {
                list.add(originMaterialDO);
            }
            if (ObjectUtil.isNotEmpty(originMaterialDO.getParentId()) && originMaterialDO.getParentId() != 0) {
                list.addAll(getEachParent(originMaterialDO.getParentId()));
            }
        }
        return list;
    }

    @Override
    public void removeIdAndChildAll(OriginMaterialDO before) {
        originMaterialGateway.removeIdAndChildAll(before.getId());

        // 删除缓存KEY
        String fileNameRedisKey = getFileNameRedisKey(before.getMaterialName(), before.getParentId());
        redisUtil.del(fileNameRedisKey);
        if (before.getIsDir()) {
            // 删除下面的所有数据文件
            fileNameRedisKey = getFileNameRedisKey("", before.getId());
            redisUtil.delKeyWithPrefix(fileNameRedisKey);
        }
    }


    @Override
    public Boolean updatePublicStatusById(Long id, Integer publicStatus) {
        OriginMaterialDO originMaterialDOBefore = originMaterialGateway.getById(id);
        if (ObjectUtil.isEmpty(originMaterialDOBefore)) {
            throw new BizException("该文件不存在!");
        }
        if (Objects.equals(originMaterialDOBefore.getParentId(), 0)) {
            materialLibraryGateway.updatePublicByMaterialId(id, publicStatus);
            if (originMaterialDOBefore.getIsDir()) {
                originMaterialGateway.updatePublicStatusBatchByParentIdList(id, publicStatus);
            }
        } else {
            OriginMaterialDO originMaterialDO = new OriginMaterialDO();
            originMaterialDO.setId(id);
            originMaterialDO.setIsPublic(publicStatus);
            originMaterialGateway.updateEntity(originMaterialDO);
            if (originMaterialDOBefore.getIsDir()) {
                originMaterialGateway.updatePublicStatusBatchByParentIdList(id, publicStatus);
            }
        }
        updatePublicStatusRecursion(originMaterialDOBefore.getParentId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePublicStatusBatch(UpdatePublicBatchDTO dto) {
        dto.getIdList().stream().forEach(t -> {
            this.updatePublicStatusById(t, dto.getIsPublic());
        });
        return true;
    }


    @Override
    public void updatePublicStatusRecursion(Long id) {
        if (ObjectUtil.isEmpty(id) || id == 0) {
            return;
        }
        OriginMaterialDO originMaterialDO = originMaterialGateway.getById(id);
        if (ObjectUtil.isEmpty(originMaterialDO)) {
            throw new BizException("该文件不存在!");
        }
        // 更新父ID文件数量
        this.updatePublicStatusById(id);
        updatePublicStatusRecursion(originMaterialDO.getParentId());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public Boolean moveList(MaterialMoveListDTO dto) {
        dto.getMoveList().stream().forEach(t -> {
            if (t.getSourceParentId().equals(CommonConstants.HOME_FOLD_PARENT_ID)) {
                throw new BizException("不可移动主文件夹！");
            }
            this.move(t);
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean move(MaterialMoveDTO materialMoveDTO) {
        if (ObjectUtil.isEmpty(materialMoveDTO.getSourceId()) || ObjectUtil.isEmpty(materialMoveDTO.getToMaterialId())) {
            throw new BizException("源素材id和移动到的素材文件夹id不能为空");
        }
        Long sourceId = materialMoveDTO.getSourceId();
        Long toMaterialId = materialMoveDTO.getToMaterialId();
        if (ObjectUtil.equals(sourceId, toMaterialId)) {
            throw new BizException("源素材和移动到的素材文件夹不能是同一个");
        }

        OriginMaterialDO sourceOriginMaterialDO = originMaterialGateway.getById(sourceId);
        OriginMaterialDO toMaterialDO = originMaterialGateway.getById(toMaterialId);

        if (ObjectUtil.isEmpty(sourceOriginMaterialDO) || ObjectUtil.isEmpty(toMaterialDO)) {
            throw new BizException("源素材和移动到的素材文件夹不能为空");
        }
        if (!Boolean.FALSE.equals(sourceOriginMaterialDO.getIsDelete()) || !Boolean.FALSE.equals(toMaterialDO.getIsDelete())) {
            throw new BizException("源素材和移动到的素材文件夹不能为空");
        }
        if (CollectionUtil.isEmpty(toMaterialDO.getParentIdList())) {
            throw new BizException("移动到的文件夹数据异常!");
        }
        if (!Boolean.TRUE.equals(toMaterialDO.getIsDir())) {
            throw new BizException("必须移动到文件夹！");
        }
        if (toMaterialDO.getParentIdList().contains(sourceId)) {
            throw new BizException("源素材和移动到的素材文件夹不能有父子关系!");
        }
        if (ObjectUtil.equals(sourceOriginMaterialDO.getParentId(), toMaterialDO.getId())) {
            throw new BizException("当前文件已经再目标文件夹中");
        }
        if (sourceOriginMaterialDO.getIsDir()) {
            //自己的父级替换及其parentIdList 前缀替换,及其
            selfMove(sourceOriginMaterialDO, toMaterialDO);
            //父级为自己的，则整体前缀替换
            childMove(sourceOriginMaterialDO, toMaterialDO);
        } else {
            selfMove(sourceOriginMaterialDO, toMaterialDO);
        }
        return true;
    }


    public void selfMove(OriginMaterialDO sourceOriginMaterialDO, OriginMaterialDO toMaterialDO) {
        Long toMaterialId = toMaterialDO.getId();
        List<Long> parentIdList = new ArrayList<>();
        parentIdList.addAll(toMaterialDO.getParentIdList());
        parentIdList.add(toMaterialId);
        log.info("selfMove {}", sourceOriginMaterialDO);
        OriginMaterialDO updateEntity = new OriginMaterialDO();
        updateEntity.setParentId(toMaterialId);
        updateEntity.setParentIdList(parentIdList);
        QueryWrapper query = QueryWrapper.create()
                .from(ORIGIN_MATERIAL.getTableName())
                .where(ORIGIN_MATERIAL.ID.eq(sourceOriginMaterialDO.getId()));
        originMaterialGateway.update(updateEntity, query);
        log.info("selfMove id,{},{}", sourceOriginMaterialDO.getId(), updateEntity);
    }

    public void childMove(OriginMaterialDO sourceOriginMaterialDO, OriginMaterialDO toMaterialDO) {
        List<Long> parentIdPrefixBefore = new ArrayList();
        parentIdPrefixBefore.addAll(sourceOriginMaterialDO.getParentIdList());
        parentIdPrefixBefore.add(sourceOriginMaterialDO.getId());
        List<Long> parentIdPrefixAfter = new ArrayList();
        parentIdPrefixAfter.addAll(toMaterialDO.getParentIdList());
        parentIdPrefixAfter.add(toMaterialDO.getId());
        parentIdPrefixAfter.add(sourceOriginMaterialDO.getId());
        List<OriginMaterialDO> list = null;
        QueryWrapper query = QueryWrapper.create()
                .from(ORIGIN_MATERIAL.getTableName())
                .where(ORIGIN_MATERIAL.IS_DELETE.eq(Boolean.FALSE))
                .and(QueryExt.conditionAndJsonContains(parentIdPrefixBefore))
                .limit(10);
        list = originMaterialGateway.list(query);
        if (CollectionUtil.isNotEmpty(list)) {
            for (OriginMaterialDO each : list) {
                log.info("before {}", each);
                List<Long> before = each.getParentIdList();
                if (CollectionUtil.isEmpty(before)) {
                    throw new BizException("素材id为" + each.getId() + "的ParentIdList 不能为空!");
                }
                int length = before.size();
                List<Long> after = new ArrayList<>();
                after.addAll(parentIdPrefixAfter);
                if (parentIdPrefixBefore.size() < length) {
                    after.addAll(before.subList(parentIdPrefixBefore.size(), length));
                }
                each.setParentIdList(after);
                log.info("after {}", each);
                originMaterialGateway.updateById(each);
            }
        }
//        boolean flag=true;
//        do{
//            QueryWrapper query = QueryWrapper.create()
//                    .from(ORIGIN_MATERIAL.getTableName())
//                    .where(ORIGIN_MATERIAL.IS_DELETE.eq(Boolean.FALSE))
//                    .and(QueryExt.conditionAndJsonContains(parentIdPrefixBefore))
//                    .limit(10);
//            list=originMaterialGateway.list(query);
//            if(CollectionUtil.isNotEmpty(list)){
//                for (OriginMaterialDO each:list) {
//                    log.info("before {}",each);
//                    List<Long> before=each.getParentIdList();
//                    if(CollectionUtil.isEmpty(before)){
//                        throw new BizException("素材id为"+each.getId()+"的ParentIdList 不能为空!");
//                    }
//                    int length=before.size();
//                    List<Long> after=new ArrayList<>();
//                    after.addAll(parentIdPrefixAfter);
//                    if(parentIdPrefixBefore.size()<length){
//                        after.addAll(before.subList(parentIdPrefixBefore.size(),length));
//                    }
//                    each.setParentIdList(after);
//                    log.info("after {}",each);
//                    originMaterialGateway.updateById(each);
//                }
////                originMaterialGateway.updateBatch(list);
//            }else{
//                flag=false;
//            }
//        }while(flag);
    }

    private void updatePublicStatusById(Long parentId) {
        if (ObjectUtil.isEmpty(parentId)) {
            return;
        }
        if (parentId == 0) {
            originMaterialGateway.updatePublicStatusByChildStatus(parentId);
            OriginMaterialDO originMaterialDO = originMaterialGateway.getById(parentId);
            materialLibraryGateway.updatePublicByMaterialId(parentId, originMaterialDO.getIsPublic());
        } else {
            originMaterialGateway.updatePublicStatusByChildStatus(parentId);
        }

    }


    @Override
    public Page<OriginMaterialExtensionVO> getGlobalTagSearchPageInfo(PageQueryCondition<GlobalOriginMaterialQuery> pageQuery, List<Long> materialIdList, TimeQueryInterval createTimeQueryInterval, TimeQueryInterval tagCreateTimeQueryInterval) {

        GlobalOriginMaterialQuery condition = pageQuery.getCondition();


        SelectQueryColumn selectQueryColumn = new SelectQueryColumn(
                select(groupConcat(MATERIAL_TAG_ITEM.TAG_VALUE)
                ).from(MATERIAL_TAG.getTableName()).leftJoin(MATERIAL_TAG_ITEM.getTableName()).on(MATERIAL_TAG.MATERIAL_ID.eq(MATERIAL_TAG_ITEM.ID))
                        .where(MATERIAL_TAG.MATERIAL_ID.eq(MATERIAL_TAG.MATERIAL_ID)));

        QueryWrapper query = QueryWrapper.create();
        query.select(ORIGIN_MATERIAL.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL, FILE_DETAIL.UPLOAD_ID, FILE_DETAIL.UPLOAD_STATUS
                        , FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL, FILE_DETAIL.TH_CONTENT_TYPE,
                        MATERIAL_TAG.CREATE_BY.as("tag_create_by"),
                        MATERIAL_TAG.CREATE_SHORT_ID.as("tag_create_short_id"),
                        MATERIAL_TAG.CREATE_TIME.as("tag_create_time"),
                        MATERIAL_TAG.USER_FAVORITE_COUNT.as("user_favorite_count"),
                        MATERIAL_TAG.USER_VIEW_COUNT.as("user_view_count"),
                        selectQueryColumn.as("tag_value"))
                .from(MATERIAL_TAG)
                .leftJoin(ORIGIN_MATERIAL.getTableName()).on(MATERIAL_TAG.MATERIAL_ID.eq(ORIGIN_MATERIAL.ID))
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(ORIGIN_MATERIAL.FILE_ID));

        query.where(ORIGIN_MATERIAL.IS_SHOW.eq(CommonConstants.ENABLE));

        if (Objects.nonNull(condition.getTagStatus())) {
            query.and(ORIGIN_MATERIAL.TAG_STATUS.eq(condition.getTagStatus()));
        }
        query.and(ORIGIN_MATERIAL.IS_DIR.eq(false));
        if (Objects.nonNull(condition.getIsPublic())) {
            query.and(ORIGIN_MATERIAL.IS_PUBLIC.eq(condition.getIsPublic()));
        }
        if (CollectionUtil.isNotEmpty(materialIdList)) {
            query.and(ORIGIN_MATERIAL.ID.in(materialIdList));
        }

        // 修改时间查询
        if (Objects.nonNull(createTimeQueryInterval) && Objects.nonNull(createTimeQueryInterval.getStartTime()) && Objects.nonNull(createTimeQueryInterval.getEndTime())) {
            query.and(ORIGIN_MATERIAL.CREATE_TIME.ge(createTimeQueryInterval.getStartTime()))
                    .and(ORIGIN_MATERIAL.CREATE_TIME.le(createTimeQueryInterval.getEndTime()));
        }
        if (Objects.nonNull(tagCreateTimeQueryInterval) && Objects.nonNull(tagCreateTimeQueryInterval.getStartTime()) && Objects.nonNull(tagCreateTimeQueryInterval.getEndTime())) {
            query.and(MATERIAL_TAG.CREATE_TIME.ge(tagCreateTimeQueryInterval.getStartTime()))
                    .and(MATERIAL_TAG.CREATE_TIME.le(tagCreateTimeQueryInterval.getEndTime()));
        }

        // 文件类型查询
        List<String> matchSuffixesList = condition.getFileTypes();
        if (CollectionUtil.isNotEmpty(matchSuffixesList)) {
            query.and(ORIGIN_MATERIAL.FILE_TYPE.in(matchSuffixesList));
        }
        if (condition.getFileSourceTypes().contains("other")) {
            Set<String> allSuffixes = materialUserPlatformConfig.getAllSuffixes();
            query.and(ORIGIN_MATERIAL.FILE_TYPE.notIn(allSuffixes));
        }

        if (StringUtils.hasLength(condition.getQueryParam1())) {
            // 文件名/创建人或者工号
            query.and(ORIGIN_MATERIAL.MATERIAL_NAME.like(condition.getQueryParam1())
                    .or(MATERIAL_TAG.REMARK.like(condition.getQueryParam1())))
//                    .or("tag_value like '%?%' ",condition.getQueryParam1())
            ;
        }
        query.orderBy(pageQuery.getOrderBy(), CommonConstants.ASC.equals(pageQuery.getOrderDirection()));
        Page<OriginMaterialExtensionVO> page = PageUtil.of(pageQuery);

        return originMaterialGateway.pageAs(page, query, OriginMaterialExtensionVO.class);
    }

    @Override
    public Page<OriginMaterialInfoV2VO> getGlobalTagSearchPageInfoV2(PageQueryCondition<GlobalOriginMaterialQuery> pageQuery) {
        List<Long> materialIdList = null;
        if (CollectionUtil.isNotEmpty(pageQuery.getCondition().getTags())) {
            materialIdList = materialTagService.getMaterialIdListByTags(pageQuery.getCondition().getTags());
            if (CollectionUtil.isEmpty(materialIdList)) {
                return new Page(List.of(), pageQuery.getPageIndex(), pageQuery.getPageSize(), 0);
            }
        }
        List<String> keyList =null;
        if(StrUtil.isNotEmpty(pageQuery.getCondition().getQueryParam1())){
            keyList = HanLPUtils.getKeyWord(pageQuery.getCondition().getQueryParam1());
            log.info("查询后分词结果：{}", JSONUtil.toJsonStr(keyList));
        }
        List<String> matchSuffixesList = new ArrayList<>();
        List<String> matchNotInSuffixesList = new ArrayList<>();
        // 文件类型查询

        if (CollectionUtil.isNotEmpty(pageQuery.getCondition().getFileSourceTypes())) {
            if (pageQuery.getCondition().getFileSourceTypes().contains("other")) {
                matchNotInSuffixesList.addAll(materialUserPlatformConfig.getAllSuffixes());
            } else {
                List<String> fileSourceTypeList = pageQuery.getCondition().getFileSourceTypes();
                List<FileSourceType> fileSourceTypes = materialUserPlatformConfig.getFileSourceTypes();
                fileSourceTypes.stream().forEach(o -> {
                    if (fileSourceTypeList.contains(o.getKey())) {
                        matchSuffixesList.addAll(o.getSuffixes());
                    }
                });
            }
        }

        //设置其他参数
        Map<String, Object> otherParams = new HashMap<>();
        otherParams.put("query", pageQuery);
        otherParams.put("keyList", keyList);
        otherParams.put("materialIdList", materialIdList);
        otherParams.put("matchSuffixesList", matchSuffixesList);
        otherParams.put("matchNotInSuffixesList", matchNotInSuffixesList);
        Set<String> matchFileTypeList = new HashSet<>();
        if (CollectionUtil.isNotEmpty(pageQuery.getCondition().getFileTypeList())) {
            pageQuery.getCondition().getFileTypeList().stream().forEach(t -> {
                matchFileTypeList.addAll(t.getTagValueList());
            });
        }
        otherParams.put("matchFileTypeList", matchFileTypeList);
        Page<OriginMaterialInfoV2VO> res = originMaterialMapper
                .xmlPaginate("getGlobalTagSearchPageInfo", PageUtil.of(pageQuery), otherParams);

        return res;
    }


    public List<MaterialTreeDTO> convertToFoldList(Long parentId, List<OriginMaterialExtensionDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        Map<Long, OriginMaterialExtensionDTO> idMap = list.stream().collect(Collectors.toMap(OriginMaterialExtensionDTO::getId, a -> a));
        Map<Long, List<Long>> parentIdMap = new HashMap();
        for (OriginMaterialExtensionDTO each : list) {
            parentIdMap.computeIfAbsent(each.getParentId(), k -> new ArrayList<Long>());
            parentIdMap.get(each.getParentId()).add(each.getId());
        }
        return recursion(parentId, parentIdMap, idMap);
    }

    public List<MaterialTreeDTO> recursion(Long parentId, Map<Long, List<Long>> parentIdMap, Map<Long, OriginMaterialExtensionDTO> idMap) {
        List<Long> ids = parentIdMap.get(parentId);
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        List<MaterialTreeDTO> foldMaterialDTOList = new ArrayList<MaterialTreeDTO>();
        for (Long id : ids) {
            OriginMaterialExtensionDTO each = idMap.get(id);
            MaterialTreeDTO foldMaterialDTO = new MaterialTreeDTO();
            BeanUtils.copyProperties(each, foldMaterialDTO);
            List<MaterialTreeDTO> childList = recursion(id, parentIdMap, idMap);
            if (!CollectionUtils.isEmpty(childList)) {
                foldMaterialDTO.setChildren(childList);
            }
            foldMaterialDTOList.add(foldMaterialDTO);
        }
        return foldMaterialDTOList;
    }


    @Override
    public MaterialTreeDTO getChildrenTree(Long id, boolean onlyDir) {
        if (ObjectUtil.isEmpty(id) || id == 0) {
            throw new BizException("请选择文件夹");
        }
        OriginMaterialDO originMaterialDO = originMaterialGateway.getById(id);
        if (ObjectUtil.isEmpty(originMaterialDO)) {
            throw new BizException("该文件夹不存在！");
        }
        if (!originMaterialDO.getIsDir()) {
            throw new BizException("请选择文件夹！");
        }
        MaterialTreeDTO parent = new MaterialTreeDTO();
        BeanUtils.copyProperties(originMaterialDO, parent);
        List<OriginMaterialExtensionDTO> list = originMaterialGateway.getChildrenList(id, onlyDir);
        if (CollectionUtil.isNotEmpty(list)) {
            List<MaterialTreeDTO> children = convertToFoldList(id, list);
            if (CollectionUtil.isNotEmpty(children)) {
                parent.setChildren(children);
            }
        }
        return parent;
    }

    @Override
    public Page<OriginMaterialExtensionVO> getFavoriteSearchPage(PageQueryCondition<GlobalOriginMaterialQuery> pageQuery, TimeQueryInterval createTimeQueryInterval) {
        IncInnerUserInfo userInfo = IncUserThreadLocal.get();
        if (ObjectUtil.isEmpty(userInfo)) {
            throw new BizException("请先登录!");
        }
        String shortId = userInfo.getName();
        GlobalOriginMaterialQuery condition = pageQuery.getCondition();

        QueryWrapper query = QueryWrapper.create();
        query.select(ORIGIN_MATERIAL.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL, FILE_DETAIL.UPLOAD_ID, FILE_DETAIL.UPLOAD_STATUS
                        , FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL, FILE_DETAIL.TH_CONTENT_TYPE,
                        MATERIAL_TAG.CREATE_BY.as("tag_create_by"),
                        MATERIAL_TAG.CREATE_SHORT_ID.as("tag_create_short_id"),
                        MATERIAL_TAG.CREATE_TIME.as("tag_create_time"),
                        MATERIAL_TAG.USER_FAVORITE_COUNT.as("user_favorite_count"),
                        MATERIAL_TAG.USER_VIEW_COUNT.as("user_view_count")
                )
                .from(MATERIAL_FAVORITE.getTableName())
                .leftJoin(ORIGIN_MATERIAL.getTableName()).on(MATERIAL_FAVORITE.MATERIAL_ID.eq(ORIGIN_MATERIAL.ID))
                .leftJoin(MATERIAL_TAG.getTableName()).on(MATERIAL_TAG.MATERIAL_ID.eq(ORIGIN_MATERIAL.ID))
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(ORIGIN_MATERIAL.FILE_ID));

        query.where(ORIGIN_MATERIAL.IS_SHOW.eq(CommonConstants.ENABLE));
        query.and(MATERIAL_FAVORITE.SHORT_ID.eq(shortId));
        if (Objects.nonNull(condition.getTagStatus())) {
            query.and(ORIGIN_MATERIAL.TAG_STATUS.eq(condition.getTagStatus()));
        }
        query.and(ORIGIN_MATERIAL.IS_DIR.eq(false));
        if (Objects.nonNull(condition.getIsPublic())) {
            query.and(ORIGIN_MATERIAL.IS_PUBLIC.eq(condition.getIsPublic()));
        }


        // 修改时间查询
        if (Objects.nonNull(createTimeQueryInterval) && Objects.nonNull(createTimeQueryInterval.getStartTime()) && Objects.nonNull(createTimeQueryInterval.getEndTime())) {
            query.and(ORIGIN_MATERIAL.CREATE_TIME.ge(createTimeQueryInterval.getStartTime()))
                    .and(ORIGIN_MATERIAL.CREATE_TIME.le(createTimeQueryInterval.getEndTime()));
        }
        // 文件类型查询
        List<String> matchSuffixesList = condition.getFileTypes();
        if (CollectionUtil.isNotEmpty(matchSuffixesList)) {
            if(matchSuffixesList.contains("other")){
                Set<String> allSuffixes = materialUserPlatformConfig.getAllSuffixes();
                query.and(ORIGIN_MATERIAL.FILE_TYPE.notIn(allSuffixes));
            }else {
                query.and(ORIGIN_MATERIAL.FILE_TYPE.in(matchSuffixesList));
            }

        }

        if (StringUtils.hasLength(condition.getQueryParam1())) {
            // 文件名/创建人或者工号
            query.and(ORIGIN_MATERIAL.MATERIAL_NAME.like(condition.getQueryParam1())
                    .or(ORIGIN_MATERIAL.CREATE_BY.like(condition.getQueryParam1()))
                    .or(ORIGIN_MATERIAL.CREATE_SHORT_ID.like(condition.getQueryParam1())));
        }
        query.orderBy(pageQuery.getOrderBy(), CommonConstants.ASC.equals(pageQuery.getOrderDirection()));
        Page<OriginMaterialExtensionVO> page = PageUtil.of(pageQuery);
        Page<OriginMaterialExtensionVO> pageResult = originMaterialGateway.pageAs(page, query, OriginMaterialExtensionVO.class);
        if (CollectionUtil.isNotEmpty(pageResult.getRecords())) {
            for (OriginMaterialExtensionVO each : pageResult.getRecords()) {
                each.setHaveFavorite(true);
            }
        }
        return pageResult;
    }

}
