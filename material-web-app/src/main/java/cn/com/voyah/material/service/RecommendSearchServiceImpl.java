package cn.com.voyah.material.service;

import cn.com.voyah.material.client.RecommendSearchService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.RecommendSearchDO;
import cn.com.voyah.material.domain.gateway.RecommendSearchGateway;
import cn.com.voyah.material.dto.RecommendSearchDTO;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.update.SortChangeDTO;
import cn.com.voyah.material.pojo.RecommendSearchPO;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigInteger;
import java.util.Collection;
import java.util.Objects;

import static cn.com.voyah.domain.entity.def.RecommendSearchDef.RECOMMEND_SEARCH;

@Service
public class RecommendSearchServiceImpl implements RecommendSearchService {


    @Autowired
    private RecommendSearchGateway recommendSearchGateway;
    @Override
    public Collection<RecommendSearchDTO> list(PageQueryCondition<RecommendSearchDTO> pageQuery, TimeQueryInterval createTimeQueryInterval) {
        RecommendSearchDTO condition = pageQuery.getCondition();
        QueryWrapper query = QueryWrapper.create();
        query.select(RECOMMEND_SEARCH.ALL_COLUMNS)
                .from(RECOMMEND_SEARCH.getTableName())
                .where(RECOMMEND_SEARCH.IS_DELETE.eq(false))
                // 主文件夹不展示
                .orderBy(RECOMMEND_SEARCH.SORT.getName(),false);
        if(Objects.nonNull(condition.getStatus())){
            query.and(RECOMMEND_SEARCH.STATUS.eq(condition.getStatus()));
        }
        if(!StringUtils.isEmpty(condition.getName())){
            query.and(RECOMMEND_SEARCH.NAME.like(condition.getName()));
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(pageQuery.getUserNameOrShortId())) {
            query.and(RECOMMEND_SEARCH.CREATE_BY.like(pageQuery.getUserNameOrShortId())
                    .or(RECOMMEND_SEARCH.CREATE_SHORT_ID.like(pageQuery.getUserNameOrShortId())));
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(pageQuery.getCondition().getCreateBy())) {
            query.and(RECOMMEND_SEARCH.CREATE_BY.like(pageQuery.getCondition().getCreateBy())
                    .or(RECOMMEND_SEARCH.CREATE_SHORT_ID.like(pageQuery.getCondition().getCreateBy())));
        }
        // 添加时间查询
        if (Objects.nonNull(createTimeQueryInterval) &&Objects.nonNull(createTimeQueryInterval.getStartTime()) && Objects.nonNull(createTimeQueryInterval.getEndTime())) {
            query.and(RECOMMEND_SEARCH.CREATE_TIME.ge(createTimeQueryInterval.getStartTime()))
                    .and(RECOMMEND_SEARCH.CREATE_TIME.le(createTimeQueryInterval.getEndTime()));
        }
        return recommendSearchGateway.listAs(query,RecommendSearchDTO.class);
    }

    @Override
    public Collection<RecommendSearchDTO> listByUser() {
        QueryWrapper query = QueryWrapper.create();
        query.select(RECOMMEND_SEARCH.ALL_COLUMNS)
                .from(RECOMMEND_SEARCH.getTableName())
                .where(RECOMMEND_SEARCH.IS_DELETE.eq(false).and(RECOMMEND_SEARCH.STATUS.eq(true)))
                // 主文件夹不展示
                .orderBy(RECOMMEND_SEARCH.SORT.getName(),false);
        return recommendSearchGateway.listAs(query,RecommendSearchDTO.class);
    }

    @Override
    public Page<RecommendSearchDTO> page(PageQueryCondition<RecommendSearchDO> pageQuery, TimeQueryInterval createTimeQueryInterval) {
        
        RecommendSearchDO condition = pageQuery.getCondition();
        QueryWrapper query = QueryWrapper.create();
        query.select(RECOMMEND_SEARCH.ALL_COLUMNS)
                .from(RECOMMEND_SEARCH.getTableName())
                .where(RECOMMEND_SEARCH.IS_DELETE.eq(false))
                // 主文件夹不展示
                .orderBy(pageQuery.getOrderBy(), CommonConstants.ASC.equals(pageQuery.getOrderDirection()));
        if(Objects.nonNull(condition.getStatus())){
            query.and(RECOMMEND_SEARCH.STATUS.eq(condition.getStatus()));
        }
        if(!StringUtils.isEmpty(condition.getName())){
            query.and(RECOMMEND_SEARCH.NAME.like(condition.getName()));
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(pageQuery.getUserNameOrShortId())) {
            query.and(RECOMMEND_SEARCH.CREATE_BY.like(pageQuery.getUserNameOrShortId())
                    .or(RECOMMEND_SEARCH.CREATE_SHORT_ID.like(pageQuery.getUserNameOrShortId())));
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(pageQuery.getCondition().getCreateBy())) {
            query.and(RECOMMEND_SEARCH.CREATE_BY.like(pageQuery.getCondition().getCreateBy())
                    .or(RECOMMEND_SEARCH.CREATE_SHORT_ID.like(pageQuery.getCondition().getCreateBy())));
        }
        // 添加时间查询
        if (Objects.nonNull(createTimeQueryInterval) &&Objects.nonNull(createTimeQueryInterval.getStartTime()) && Objects.nonNull(createTimeQueryInterval.getEndTime())) {
            query.and(RECOMMEND_SEARCH.CREATE_TIME.ge(createTimeQueryInterval.getStartTime()))
                    .and(RECOMMEND_SEARCH.CREATE_TIME.le(createTimeQueryInterval.getEndTime()));
        }
        Page<RecommendSearchDTO> page = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        return recommendSearchGateway.pageAs(page, query, RecommendSearchDTO.class);
    }

    @Override
    public RecommendSearchDTO getById(Long id) {
        QueryWrapper query = QueryWrapper.create();
        query.select(RECOMMEND_SEARCH.ALL_COLUMNS)
                .from(RECOMMEND_SEARCH.getTableName())
                .where(RECOMMEND_SEARCH.IS_DELETE.eq(false))
                .and(RECOMMEND_SEARCH.ID.eq(id));
        return recommendSearchGateway.getOneAs(query,RecommendSearchDTO.class);
    }

//    @Override
//    public String generateFileName(String dir,String originName) {
//        if (StringUtils.isEmpty(originName)) {
//            throw new BizException("文件名不能为空");
//        }
//        int index = originName.lastIndexOf(CommonConstants.FILE_SEPRATE_SUFFIX);
//        String prefix=dir+"quickaa"+CommonConstants.DIR_SEPRATE_STR;
//        String suffix = "";
//        if (index > 0) {
//            prefix = prefix+originName.substring(0, index);
//            suffix = originName.substring(index);
//        } else {
//            prefix = prefix+originName;
//        }
//        String finalName = originName;
//        boolean existFlag = false;
//        int i = 0;
//        do {
//            if (i > 0) {
//                StringBuilder stringBuilder = new StringBuilder(prefix);
//                stringBuilder.append(CommonConstants.FILE_DUPLICATION_SEPRATE);
//                stringBuilder.append(i);
//                if (!StringUtils.isEmpty(suffix)) {
//                    stringBuilder.append(suffix);
//                }
//                finalName = stringBuilder.toString();
//            }
//            existFlag = existFileNameDb(finalName);
//            i++;
//        } while (existFlag);
//        return finalName;
//    }

    @Override
    public Boolean sortChange(SortChangeDTO sortChangeDTO) {
        Long beforeId=sortChangeDTO.getBeforeId();
        Long afterId=sortChangeDTO.getAfterId();
        if(ObjectUtils.isEmpty(beforeId)&&ObjectUtils.isEmpty(afterId)){
            return false;
        }
        Long beforeSort=null;
        if(!ObjectUtils.isEmpty(beforeId)){
            RecommendSearchDO recommendSearchDO=recommendSearchGateway.getById(beforeId);
            beforeSort=recommendSearchDO.getSort();
        }
        Long afterSort=null;
        if(!ObjectUtils.isEmpty(afterId)){
            RecommendSearchDO recommendSearchDO=recommendSearchGateway.getById(afterId);
            afterSort=recommendSearchDO.getSort();
        }
        if(ObjectUtils.isEmpty(beforeSort)&&!ObjectUtils.isEmpty(afterSort)){
            RecommendSearchPO recommendSearchDO=recommendSearchGateway.getNearerDOBySort(afterSort,true);
            if(ObjectUtil.isNotEmpty(recommendSearchDO)){
                beforeSort=recommendSearchDO.getSort();
            }
        }
        if(ObjectUtils.isEmpty(afterSort)&&!ObjectUtils.isEmpty(beforeSort)){
            RecommendSearchPO recommendSearchDO=recommendSearchGateway.getNearerDOBySort(beforeSort,false);
            if(ObjectUtil.isNotEmpty(recommendSearchDO)){
                afterSort=recommendSearchDO.getSort();
            }
        }
        Long sort= avgSort(beforeSort,afterSort);
        return recommendSearchGateway.updateSort(sortChangeDTO.getId(),sort);
    }

    private Long avgSort(Long beforeSort,Long afterSort){
        if(ObjectUtil.isEmpty(beforeSort)){
            beforeSort=Long.MAX_VALUE;
        }
        if(ObjectUtil.isEmpty(afterSort)){
            afterSort=0L;
        }
        return BigInteger.valueOf(beforeSort).add(BigInteger.valueOf(afterSort)).divide(BigInteger.valueOf(2)).longValue();
    }
//    private boolean existFileNameDb(String finalName) {
//        QueryWrapper query = QueryWrapper.create();
//        query.from(RECOMMEND_SEARCH.getTableName())
//                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(RECOMMEND_SEARCH.FILE_ID))
//                .where(FILE_DETAIL.FILENAME.eq(finalName)
//                        .and(RECOMMEND_SEARCH.IS_DELETE.eq(false)));
//        long count = recommendSearchGateway.count(query);
//        return count > 0;
//    }

}
