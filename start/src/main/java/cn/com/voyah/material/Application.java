package cn.com.voyah.material;

import com.iov.tencent.inc.access.config.IncRedisConfig;
import com.iov.tencent.inc.access.config.IncRestTemplateConfig;
import lombok.extern.slf4j.Slf4j;

//import org.dromara.easyes.spring.annotation.EsMapperScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Spring Boot Starter
 *
 * <AUTHOR>
 * <p>
 * exclude = SpringDataWebAutoConfiguration.class
 * 处理告警 Bean 'x' of type [x] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for
 * auto-proxying).
 * </p>
 */
@EnableAsync
@Slf4j
@MapperScan("cn.com.voyah.material.mapper")
@ComponentScan(
        value = {
                "cn.com.voyah.material",
                "com.iov.tencent.inc.access.aop",
                "com.iov.tencent.inc.access.config",
                "com.iov.tencent.inc.access.service"
        },
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
                        value = {IncRedisConfig.class, IncRestTemplateConfig.class})
        })
@SpringBootApplication(exclude = {SpringDataWebAutoConfiguration.class})
@EnableScheduling
//@EsMapperScan("cn.com.voyah.material")
public class Application {

    public static void main(String[] args) {
        log.info("Begin to start Spring Boot Application");
        long startTime = System.currentTimeMillis();

        SpringApplication.run(Application.class, args);

        long endTime = System.currentTimeMillis();
        log.info("End starting Spring Boot Application, Time used: " + (endTime - startTime) + "ms");
    }
}
