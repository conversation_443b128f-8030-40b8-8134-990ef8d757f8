spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: ****************************************************************************
#    username: ${MYSQL_USER:root}
#    password: ${MYSQL_PASSWORD:12345678}

    url: *******************************************************************************
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWORD:WMS_db_dev@qwer@123}

#    url: ********************************************************************************
#    username: ${MYSQL_USER:root}
#    password: ${MYSQL_PASSWORD:Nacos@2023!}
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    port: 6379
#    host: 127.0.0.1
#    database: 0

    host: ************
    database: 10
    password: hzx@123

#    host: ************
#    database: 0
#    password: Tr@20!24Q
    lettuce:
      # redis 数据库连接池参数
      pool:
        enabled: true
        # 最大活跃连接
        max-active: 80
        # 获取连接 等待时间
        max-wait: -1
        # 最大连接
        max-idle: 80
        # 最小连接
        min-idle: 20
#  auth:
#    permitted-url:
#      - /use-platform/*


#      - /origin-material/*
#      - /file/*
#      - /file-part/*
user:
  auth: true
rest:
  # 用户信息，模拟mock true开启
  mock: true

#密钥
#cos:
#  ##mumu
#  secretId: AKIDlQ5mK0ra6QqFVfo03xv6GKLH4g9fPI1Z
#  secretKey: WpxrY9Xkl6OeicQ3SecRKRRH0Xs0xhVK
#  appId: 1307512833
#  bucket: mumutest-1307512833
#  region: ap-guangzhou
#  url: https://mumutest-1307512833.cos.ap-guangzhou.myqcloud.com/
#  prefix: test/
#  duration: 7200
cos:
  secretId: AKIDgJsU1sc8PTL0SbbS7Yqz0rY5cFoIafYJ
  secretKey: d2KPL4nSzLUSEpDRTM2Z1JPJ0UntoY6U
  appId: 1307512833
  bucket: voyahub-1301141550
  region: ap-nanjing
  url: https://voyahub-1301141550.cos.ap-nanjing.myqcloud.com/
  prefix: test/
  duration: 7200
inc-config:
  appId: 111
  #Center系统的appSecret
  appSecret: 111
  #Center系统的请求地址
  incServer: "http://127.0.0.1:81"
  # session key 前缀
  sessionKey: "sessionId"
  # 登陆session有效期,单位s
  sessionTime: 3000
  # cookie domain
  cookieDomain: "127.0.0.1"
material:
  file-source-types:
    - key: picture
      label: 图片
      suffixes: [png, jpg, jpeg, gif, bmp,heic,tif]
    - key: video
      label: 视频
      suffixes: [mp4, mov,m4a,wav,mp3,m4v]
    - key: document
      label: 文档
      suffixes: [pdf, doc, docx, excel, ppt, txt,xls]
    - key: package
      label: 压缩包
      suffixes: [zip, rar]
    - key: design
      label: 设计源文件
      suffixes: [sketch, psd]
  user-platform:
    file-source-types:
      - key: picture
        label: 图片
        suffixes: [png, jpg, jpeg, gif, bmp,heic,tif]
      - key: video
        label: 视频
        suffixes: [mp4, mov,m4a,wav,mp3,m4v]
      - key: document
        label: 文档
        suffixes: [pdf, doc, docx, excel, ppt, txt,xls]
      - key: package
        label: 压缩包
        suffixes: [zip, rar]
      - key: design
        label: 设计文件
        suffixes: [sketch, psd]
      - key: other
        label: 其他
        suffixes: []
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan:
        # 配置接口文档扫描包路径，每个服务的路径不同
        - cn.com.voyah.material


# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: false
  setting:
    language: zh_cn
api:
  test:
    allow: true
operation:
  log:
    ignore: true
    export-limit-count: 10000
topic:
  log: operation_log_local
th:
  generate:
    content-types: pdf, doc, docx, excel, ppt, txt,xls,xlsx
manage:
  scheduling:
    active: true
    tag:
      active: true
  task:
    cron:
      express: 0 1 0 * * ?
  tag:
    cron:
      express: 0 10 0 * * ?
#easy-es:
#  address: ************:9200
#  keep-alive-millis: 30000 # 心跳策略时间 单位:ms
#  connect-timeout: 1200000 # 连接超时时间 单位:ms
#  socket-timeout: 1200000 # 通信超时时间 单位:ms
#  request-timeout: 1200000 # 请求超时时间 单位:ms
#  connection-request-timeout: 1200000 # 连接请求超时时间 单位:ms
#  max-conn-total: 100 # 最大连接数 单位:个
#  max-conn-per-route: 100 # 最大连接路由数 单位:个
#  enable: true

ai:
  host: 'https://voyahgpt-gateway.voyah.cn/api/gateway'
  path: '/open/api/chat/maas'
  key: 'VOYAHUB'
  secret: '1456vsa098b04c6687659605sdgfe4v6'
