spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver

    url: ****************************************************************************
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWORD:12345678}

#    url: ********************************************************************************
#    username: ${MYSQL_USER:root}
#    password: ${MYSQL_PASSWORD:SerIGYRyutrfeuwdyq@33jghwfewr}
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    port: 6379
    host: 127.0.0.1
    database: 0

#    host: *************
#    database: 0
#    password: FQwbErDWXaXTBzt5VRvs5aFE2dbr2SBCzCxGBdZCvS2RGDxc
    lettuce:
      # redis 数据库连接池参数
      pool:
        enabled: true
        # 最大活跃连接
        max-active: 80
        # 获取连接 等待时间
        max-wait: -1
        # 最大连接
        max-idle: 80
        # 最小连接
        min-idle: 20
user:
  auth: true
rest:
  # 用户信息，模拟mock true开启
  mock: true

#密钥
cos:
  ##mumu
  secretId: AKIDlQ5mK0ra6QqFVfo03xv6GKLH4g9fPI1Z
  secretKey: WpxrY9Xkl6OeicQ3SecRKRRH0Xs0xhVK
  appId: 1307512833
  bucket: mumutest-1307512833
  region: ap-guangzhou
  url: https://mumutest-1307512833.cos.ap-guangzhou.myqcloud.com/
  prefix: test/
  duration: 7200
#cos:
#  secretId: AKIDgJsU1sc8PTL0SbbS7Yqz0rY5cFoIafYJ
#  secretKey: d2KPL4nSzLUSEpDRTM2Z1JPJ0UntoY6U
#  appId: 1307512833
#  bucket: voyahub-1301141550
#  region: ap-nanjing
#  url: https://voyahub-1301141550.cos.ap-nanjing.myqcloud.com/
#  prefix: test/
#  duration: 7200
inc-config:
  appId: 111
  #Center系统的appSecret
  appSecret: 111
  #Center系统的请求地址
  incServer: "http://127.0.0.1:81"
  # session key 前缀
  sessionKey: "sessionId"
  # 登陆session有效期,单位s
  sessionTime: 3000
  # cookie domain
  cookieDomain: "127.0.0.1"
material:
  file-source-types:
    - key: picture
      label: 图片
      suffixes: [png, jpg, jpeg, gif, bmp,heic,tif]
    - key: video
      label: 视频
      suffixes: [mp4, mov,m4a,wav,mp3,m4v]
    - key: document
      label: 文档
      suffixes: [pdf, doc, docx, excel, ppt, txt,xls]
    - key: package
      label: 压缩包
      suffixes: [zip, rar]
    - key: design
      label: 设计源文件
      suffixes: [sketch, psd]
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: false
  show-actuator: false
knife4j:
  production: true
api:
  test:
    allow: true