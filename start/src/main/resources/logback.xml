<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!-- 读取 spring配置文件 -->
<!--    <springProperty scope="context" name="APP_NAME" source="spring.application.name" />-->

    <!-- 定义属性 -->
    <property name="LOG_PATH" value="logs" />
    <property name="LOG_FILE" value="${LOG_PATH}/application.log" />
    <property name="LOG_PATTERN" value="%date{HH:mm:ss.SSS} [%thread] %-5level %logger{35} - %msg%n" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!-- 日志文件 -->
    <appender name="APPLICATION"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件地址 -->
        <file>${LOG_FILE}</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 文件地址，及文件名称生成规则 -->
            <fileNamePattern>${LOG_PATH}/%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 最大保留文件数 跟据 %d 来计算文件数 -->
            <maxHistory>7</maxHistory>
            <!-- 单文件最大空间 -->
            <maxFileSize>50MB</maxFileSize>
            <!-- 总日志文件保留大小 -->
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="APPLICATION" />
    </root>

    <!--应用日志-->
    <!--这个logger没有指定appender，它会继承root节点中定义的那些appender-->
    <logger name="cn.com.voyah.material" level="DEBUG"/>
    <logger name="com.iov.tencent.inc.access.util" level="ERROR"/>

    <!-- 设置Spring的日志级别为DEBUG -->
<!--    <logger name="org.springframework" level="DEBUG"/>-->

</configuration>
