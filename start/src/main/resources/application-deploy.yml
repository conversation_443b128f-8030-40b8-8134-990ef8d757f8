spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWORD:12345678}

#    url: ********************************************************************************
#    username: ${MYSQL_USER:root}
#    password: ${MYSQL_PASSWORD:Nacos@2023!}
  servlet:
    multipart:
      max-file-size: 600MB
      max-request-size: 600MB
  redis:
    port: 6379
    host: 127.0.0.1
    database: 0

#  redis:
#    port: 6379
#    host: ************
#    database: 0
#    password: Tr@20!24Q
  auth:
    permitted-url:
      - /material-library/*
      - /origin-material/*
      - /file/*
      - /file-part/*
#密钥
cos:
  secretId: AKIDgJsU1sc8PTL0SbbS7Yqz0rY5cFoIafYJ
  secretKey: d2KPL4nSzLUSEpDRTM2Z1JPJ0UntoY6U
  appId: 1307512833
  #存储桶
  bucket: voyahub-1301141550
  #区域设置
  region: ap-nanjing
  #域名
  url: https://voyahub-1301141550.cos.ap-nanjing.myqcloud.com/
  #默认文件夹
  prefix: deploy/
  duration: 7200
inc-config:
  # Center系统的appId
  appId: 111
  #Center系统的appSecret
  appSecret: 111
  #Center系统的请求地址
  incServer: "http://127.0.0.1:81"
  # session key 前缀
  sessionKey: "inc-test"
  # 登陆session有效期,单位s
  sessionTime: 300
  # cookie domain
  cookieDomain: "127.0.0.1"
user:
  auth: false
rest:
  # 用户信息，模拟mock true开启
  mock: true
material:
  file-source-types:
    - key: picture
      label: 图片
      suffixes: [png, jpg, jpeg, gif, bmp,heic,tif]
    - key: video
      label: 视频
      suffixes: [mp4, mov,m4a,wav,mp3,m4v]
    - key: document
      label: 文档
      suffixes: [pdf, doc, docx, excel, ppt, txt,xls]
    - key: package
      label: 压缩包
      suffixes: [zip, rar,gz,tar]
    - key: design
      label: 设计源文件
      suffixes: [sketch, psd]