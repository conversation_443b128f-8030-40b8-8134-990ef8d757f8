services:
  material-server:
    image: material-server:latest
    container_name: material-server
    ports:
      - "23580:8080"
    volumes:
      - "./config:/config"
    environment:
      - TZ=Asia/Shanghai
      - spring.application.name=material-server
      - spring.profiles.active=prod
      - spring.config.location=file:/config/material-server-prod.yml
  material-portal:
    image: material-portal:latest
    container_name: material-portal
    ports:
      - "23581:80"
    volumes:
      - "./config/:/etc/nginx/conf.d/"
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - material-server
    networks:
      - net-material
networks:
  net-material:
    external: true