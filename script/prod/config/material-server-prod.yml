spring:
  application:
    name: material-server
  #  servlet:
  #    multipart:
  #      max-file-size: 10MB
  #      max-request-size: 10MB
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************
    username: ${MYSQL_USER:mysqladmin}
    password: ${MYSQL_PASSWORD:PaaS2025!@#}
  redis:
    port: 6379
    host: ***********
    database: 0
    password: FQwbErDWXaXTBzt5VRvs5aFE2dbr2SBCzCxGBdZCvS2RGDxc
  auth:
    permitted-url:
  #    - /material-library/*
  #    - /origin-material/*
  #    - /file/*
  #    - /file-part/*
mybatis:
  configuration:
    map-underscore-to-camel-case: true

logging:
  config: classpath:logback.xml
cos:
  secretId: AKIDgJsU1sc8PTL0SbbS7Yqz0rY5cFoIafYJ
  secretKey: d2KPL4nSzLUSEpDRTM2Z1JPJ0UntoY6U
  appId: 1307512833
  bucket: voyahub-1301141550
  region: ap-nanjing
  url: https://voyahub-1301141550.cos.ap-nanjing.myqcloud.com/
  prefix: prod/
  duration: 7200
inc-config:
  appId: 00750593
  appSecret: 5c72defd-2b67-410c-8099-3dcef8e72517
  incServer: https://center.dsmp.voyah.com.cn
  sessionKey : "sessionId"
  sessionTime : 300
  cookieDomain: "voyahub.voyah.cn"
  redis:
    port: 6379
    host: ***********
    database: 0
    password: FQwbErDWXaXTBzt5VRvs5aFE2dbr2SBCzCxGBdZCvS2RGDxc
    maxActive: 80
    maxWait: -1
    maxIdle: 80
    minIdle: 20
    timeout: 20000