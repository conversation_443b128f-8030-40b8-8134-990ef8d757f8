
ALTER TABLE file_detail MODIFY COLUMN th_url varchar(1024) ;
ALTER TABLE material_tag add COLUMN  `user_view_count` bigint DEFAULT 0;
ALTER TABLE material_tag add COLUMN  `user_favorite_count` bigint DEFAULT 0;

update material_tag set user_favorite_count=0;
update material_tag set user_view_count=0;

CREATE TABLE `banner_info` (
                               `id` bigint NOT NULL,
                               `forward_url` varchar(4999)  DEFAULT NULL,
                               `file_id` bigint DEFAULT NULL,
                               `name` varchar(255) DEFAULT NULL,
                               `remark` varchar(4999) DEFAULT NULL,
                               `create_short_id` varchar(32) DEFAULT NULL COMMENT '创建人短ID',
                               `update_short_id` varchar(32) DEFAULT NULL,
                               `create_by` varchar(32) DEFAULT NULL,
                               `create_time` timestamp NULL DEFAULT NULL,
                               `update_by` varchar(32) DEFAULT NULL,
                               `update_time` timestamp NULL DEFAULT NULL,
                               `version` bigint DEFAULT NULL,
                               `is_delete` tinyint(1) DEFAULT '0',
                               `sort` bigint DEFAULT NULL,
                               `start_time` timestamp NULL DEFAULT NULL,
                               `end_time` timestamp NULL DEFAULT NULL,
                               `position` int DEFAULT NULL COMMENT '位置',
                               `status` int DEFAULT NULL COMMENT '0下架 1 上架',
                               `file_type` varchar(255) DEFAULT NULL,
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='banner表';



CREATE TABLE `quick_access_area` (
                                     `id` bigint NOT NULL,
                                     `tags` json DEFAULT NULL,
                                     `file_id` bigint DEFAULT NULL,
                                     `name` varchar(255) DEFAULT NULL,
                                     `remark` varchar(4999)  DEFAULT NULL,
                                     `create_short_id` varchar(32) DEFAULT NULL COMMENT '创建人短ID',
                                     `update_short_id` varchar(32) DEFAULT NULL,
                                     `create_by` varchar(32) DEFAULT NULL,
                                     `create_time` timestamp NULL DEFAULT NULL,
                                     `update_by` varchar(32) DEFAULT NULL,
                                     `update_time` timestamp NULL DEFAULT NULL,
                                     `version` bigint DEFAULT NULL,
                                     `is_delete` tinyint(1) DEFAULT '0',
                                     `sort` bigint DEFAULT NULL,
                                     `start_time` timestamp NULL DEFAULT NULL,
                                     `end_time` timestamp NULL DEFAULT NULL,
                                     `position` int DEFAULT NULL COMMENT '位置',
                                     `status` int DEFAULT NULL COMMENT '0下架 1 上架',
                                     `file_type` varchar(255) DEFAULT NULL,
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='金刚位表';


CREATE TABLE `task_info` (
                             `id` bigint NOT NULL,
                             `task_name` varchar(255) DEFAULT NULL COMMENT '任务名称',
                             `task_end_time` timestamp NULL DEFAULT NULL COMMENT '任务期望完成完成时间',
                             `dispatch_by` varchar(255) DEFAULT NULL COMMENT '派发人姓名',
                             `dispatch_short_id` varchar(255) DEFAULT NULL COMMENT '派发人短id',
                             `execute_by` varchar(255) DEFAULT NULL COMMENT '执行人姓名',
                             `execute_short_id` varchar(255) DEFAULT NULL COMMENT '执行人短id',
                             `task_status` int DEFAULT NULL,
                             `create_time` timestamp NULL DEFAULT NULL COMMENT '创建人',
                             `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                             `create_by` varchar(255) DEFAULT NULL COMMENT '创建人姓名',
                             `update_by` varchar(255) DEFAULT NULL COMMENT '更新人姓名',
                             `is_delete` tinyint DEFAULT NULL COMMENT '是否已删除',
                             `version` bigint DEFAULT NULL,
                             `update_short_id` varchar(255) DEFAULT NULL,
                             `create_short_id` varchar(255) DEFAULT NULL,
                             `material_fold_id` bigint DEFAULT NULL COMMENT '素材库id',
                             `material_file_count` int DEFAULT NULL COMMENT '素材数量',
                             `task_finish_time` timestamp NULL DEFAULT NULL,
                             `actual_file_count` int DEFAULT NULL,
                             `task_start_time` timestamp NULL DEFAULT NULL COMMENT '任务期望开始时间',
                             `remark` varchar(499)  DEFAULT NULL,
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='任务基本信息表';


CREATE TABLE `notice_message` (
                                  `id` bigint NOT NULL,
                                  `type` int DEFAULT NULL,
                                  `content` text,
                                  `short_id` varchar(255) DEFAULT NULL,
                                  `user_name` varchar(255) DEFAULT NULL,
                                  `send_time` timestamp NULL DEFAULT NULL,
                                  `source` int DEFAULT NULL,
                                  `state` int DEFAULT NULL,
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `is_delete` tinyint(1) DEFAULT NULL,
                                  `create_by` varchar(255) DEFAULT NULL,
                                  `update_by` varchar(255) DEFAULT NULL,
                                  `update_time` timestamp NULL DEFAULT NULL,
                                  `version` varchar(255) DEFAULT NULL,
                                  `create_short_id` varchar(32) DEFAULT NULL,
                                  `update_short_id` varchar(32) DEFAULT NULL,
                                  `relation_id` bigint DEFAULT NULL,
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='通知表';


CREATE TABLE `operation_log` (
                                 `id` bigint NOT NULL,
                                 `short_id` varchar(255) DEFAULT NULL,
                                 `user_name` varchar(255) DEFAULT NULL,
                                 `state` int DEFAULT NULL,
                                 `interface_remark` varchar(255) DEFAULT NULL,
                                 `operation_time` timestamp NULL DEFAULT NULL,
                                 `ip` varchar(499) DEFAULT NULL,
                                 `path` varchar(499) DEFAULT NULL,
                                 `method` varchar(499) DEFAULT NULL,
                                 `request_params` varchar(499) DEFAULT NULL,
                                 `response` varchar(499) DEFAULT NULL,
                                 `error_msg` varchar(499) DEFAULT NULL,
                                 `time_interval` bigint DEFAULT NULL,
                                 `request_time` timestamp NULL DEFAULT NULL,
                                 `response_time` timestamp NULL DEFAULT NULL,
                                 `is_delete` tinyint DEFAULT NULL,
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `create_by` varchar(255) DEFAULT NULL,
                                 `update_by` varchar(255) DEFAULT NULL,
                                 `update_time` timestamp NULL DEFAULT NULL,
                                 `version` varchar(255) DEFAULT NULL,
                                 `create_short_id` varchar(32) DEFAULT NULL,
                                 `update_short_id` varchar(32) DEFAULT NULL,
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='日志表';




CREATE TABLE `material_favorite` (
                                     `id` int NOT NULL,
                                     `material_id` bigint DEFAULT NULL COMMENT '素材库id',
                                     `short_id` varchar(255) NOT NULL,
                                     `user_name` varchar(255) NOT NULL,
                                     `create_time` timestamp NULL DEFAULT NULL COMMENT '创建人',
                                     `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                     `create_by` varchar(255) DEFAULT NULL COMMENT '创建人姓名',
                                     `update_by` varchar(255) DEFAULT NULL COMMENT '更新人姓名',
                                     `is_delete` tinyint DEFAULT NULL COMMENT '是否已删除',
                                     `version` bigint DEFAULT NULL,
                                     `update_short_id` varchar(255) DEFAULT NULL,
                                     `create_short_id` varchar(255) DEFAULT NULL,
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='收藏表';


CREATE TABLE `client_info` (
                               `id` bigint NOT NULL,
                               `name` varchar(255) NOT NULL,
                               `current_version` varchar(255) NOT NULL,
                               `type` int NOT NULL,
                               `active` int NOT NULL,
                               `remark` varchar(255) DEFAULT NULL,
                               `file_id` bigint DEFAULT NULL,
                               `create_time` timestamp NULL DEFAULT NULL COMMENT '创建人',
                               `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                               `create_by` varchar(255) DEFAULT NULL COMMENT '创建人姓名',
                               `update_by` varchar(255) DEFAULT NULL COMMENT '更新人姓名',
                               `is_delete` tinyint DEFAULT NULL COMMENT '是否已删除',
                               `version` bigint DEFAULT NULL,
                               `update_short_id` varchar(255) DEFAULT NULL,
                               `create_short_id` varchar(255) DEFAULT NULL,
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB ;

ALTER TABLE material_tag add COLUMN  `user_download_count` bigint DEFAULT 0;



ALTER TABLE file_detail MODIFY COLUMN th_filename varchar(2048) ;
ALTER TABLE file_detail MODIFY COLUMN url varchar(2048) ;
ALTER TABLE file_detail MODIFY COLUMN filename varchar(2048) ;
ALTER TABLE file_detail MODIFY COLUMN base_path varchar(2048) ;
ALTER TABLE file_detail MODIFY COLUMN `path` varchar(2048) ;
ALTER TABLE file_detail MODIFY COLUMN original_filename varchar(2048) ;

ALTER TABLE operation_log MODIFY COLUMN error_msg text ;