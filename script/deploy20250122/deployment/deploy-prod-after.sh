#!/bin/bash
set -e
function download_and_load(){
download_url=$1$2
tar_name=$2
curl -O $download_url
docker load -i $tar_name
}
download_prefix="https://voyahub-1301141550.cos.ap-nanjing.myqcloud.com/deploy/"
download_file_list=("material-portal.tar.gz" "material-server.tar.gz")
cd /data/voyahub/bak
for each in ${download_file_list[@]}
do
	download_and_load $download_prefix $each 
done
cd /data/voyahub/material
docker-compose stop
docker-compose rm -f
docker-compose up -d
