#!/bin/bash
set -e
function download_and_load(){
download_url=$1$2
tar_name=$2
curl -O $download_url
docker load -i $tar_name
}
function restart_images(){
container_name=$1
docker-compose stop $container_name
docker-compose rm -f $container_name
docker-compose up -d $container_name
}
download_prefix="https://voyahub-1301141550.cos.ap-nanjing.myqcloud.com/deploy/"
download_file_list=("material-portal.tar.gz" "material-server.tar.gz" "user-portal.tar.gz")
cd /data/voyahub/bak
for each in ${download_file_list[@]}
do
	download_and_load $download_prefix $each 
done
cd /data/voyahub/material
restart_images_list=("material-portal" "material-server" "material-user-server" "user-portal")
for each in ${restart_images_list[@]}
do
	restart_images $each
done