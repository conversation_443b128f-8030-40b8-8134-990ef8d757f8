services:
  kkfileview:
    image: keking/kkfileview:4.1.0
    container_name: kkfileview
    ports:
      - "8012:8012"
    environment:
      - TZ=Asia/Shanghai
      - KK_BASE_URL=https://voyahub-test.voyah.cn/preview
      - KK_CONTEXT_PATH=/preview
  material-server:
    image: material-server:1.0
    container_name: material-server
    # ports:
    #   - "23580:8080"
    volumes:
      - "./config/material-server-test.yml:/config/material-server-test.yml"
    environment:
      - TZ=Asia/Shanghai
      - spring.application.name=material-server
      - spring.profiles.active=test
      - spring.config.location=file:/config/material-server-test.yml
      - spring.main.allow-bean-definition-overriding=true
  material-portal:
    image: material-portal:1.0
    container_name: material-portal
    # ports:
    #   - "23581:80"
    volumes:
      - "./config/material-portal:/etc/nginx/conf.d"
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - material-server
  material-user-server:
    image: material-server:1.0
    container_name: material-user-server
    # ports:
    #   - "23590:8080"
    volumes:
      - "./config/material-user-server-test.yml:/config/material-user-server-test.yml"
    environment:
      - TZ=Asia/Shanghai
      - spring.main.allow-bean-definition-overriding = true
      - spring.application.name=material-user-server
      - spring.profiles.active=test
      - spring.config.location=file:/config/material-user-server-test.yml
  user-portal:
    image: user-portal:1.0
    container_name: user-portal
    ports:
      - "23581:80"
    volumes:
      - "./config/user-portal:/etc/nginx/conf.d"
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - material-user-server
      - material-portal
      - material-server
      - kkfileview