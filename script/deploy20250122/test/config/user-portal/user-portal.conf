server {
        listen 80;
        charset utf-8;
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options nosniff;
        location /preview/ {
                proxy_pass http://kkfileview:8012/preview/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
        }


        location /api/voyahub/user/ {
                proxy_pass http://material-user-server:8080/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
        }

    location /api/voyahub/ {
                proxy_pass http://material-server:8080/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
        }
        location /manage/ {
                proxy_pass http://material-portal:80/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
             #   proxy_http_version 1.1;
             #   proxy_set_header Upgrade $http_upgrade;
             #   proxy_set_header Connection "upgrade";
        }
        error_page 404 /404.html;
                location = /40x.html {
        }
        error_page 500 502 503 504 /50x.html;
                location = /50x.html {
        }
        location  /material/assets/ {
                include /usr/local/openresty/nginx/conf/mime.types;
                default_type application/octet-stream;
                alias   /var/www/dist/assets/;
                index  index.html;
        }
        location  /user/assets/ {
                include /usr/local/openresty/nginx/conf/mime.types;
                default_type application/octet-stream;
                alias   /var/www/dist/assets/;
                index  index.html;
        }

        location  / {
                include /usr/local/openresty/nginx/conf/mime.types;
                default_type application/octet-stream;

                try_files $uri $uri/ /index.html;
                #rewrite ^/(.*) /material-portal/ permanent;
                alias   /var/www/dist/;
                index  index.html;
        }
}