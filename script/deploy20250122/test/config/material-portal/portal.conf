server {
        listen 80;
        charset utf-8;
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options nosniff;


        error_page 404 /404.html;
                location = /40x.html {
        }
        error_page 500 502 503 504 /50x.html;
                location = /50x.html {
        }

        location  /rawMaterials/ {
                include /usr/local/openresty/nginx/conf/mime.types;
                default_type application/octet-stream;
                alias   /var/www/dist/;
                index  index.html;
        }
        location  /annotation/ {
                include /usr/local/openresty/nginx/conf/mime.types;
                default_type application/octet-stream;
                alias   /var/www/dist/;
                index  index.html;
        }
        location  /dictionary/ {
                include /usr/local/openresty/nginx/conf/mime.types;
                default_type application/octet-stream;
                alias   /var/www/dist/;
                index  index.html;
        }
        location  /spotCheck/ {
                include /usr/local/openresty/nginx/conf/mime.types;
                default_type application/octet-stream;
                alias   /var/www/dist/;
                index  index.html;
        }
        location  /advertisement/ {
                include /usr/local/openresty/nginx/conf/mime.types;
                default_type application/octet-stream;
                alias   /var/www/dist/;
                index  index.html;
        }
        location  /systemManagement/ {
                include /usr/local/openresty/nginx/conf/mime.types;
                default_type application/octet-stream;
                alias   /var/www/dist/;
                index  index.html;
        }

        location  /{
                include /usr/local/openresty/nginx/conf/mime.types;
                default_type application/octet-stream;

                try_files $uri $uri/ /index.html;
                #rewrite ^/(.*) /material-portal/ permanent;
                alias   /var/www/dist/;
                index  index.html;
        }
}
