
spring:
  #  servlet:
  #    multipart:
  #      max-file-size: 10MB
  #      max-request-size: 10MB
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************
    username: ${MYSQL_USER:mydba}
    password: ${MYSQL_PASSWORD:Mydba123!!}
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    port: 16379
    host: *********
    database: 0
    password: tke@dongfeng123
  auth:
    permitted-url:
  #    - /material-library/*
  #    - /origin-material/*
  #    - /file/*
  #    - /file-part/*
mybatis:
  configuration:
    map-underscore-to-camel-case: true

logging:
  config: classpath:logback.xml
#密钥
cos:
  secretId: AKIDgJsU1sc8PTL0SbbS7Yqz0rY5cFoIafYJ
  secretKey: d2KPL4nSzLUSEpDRTM2Z1JPJ0UntoY6U
  appId: 1307512833
  #存储桶
  bucket: voyahub-1301141550
  #区域设置
  region: ap-nanjing
  #域名
  url: https://voyahub-1301141550.cos.ap-nanjing.myqcloud.com/
  #默认文件夹
  prefix: test/
  duration: 7200
inc-config:
  # Center系统的appId
  appId: 70910786
  #Center系统的appSecret
  appSecret: a1427ebb-345e-46e4-8aaf-3c10e5b9b084
  #Center系统的请求地址
  incServer: https://center.idsmp.voyah.com.cn
  # session key 前缀
  sessionKey : "sessionId"
  # 登陆session有效期,单位s
  sessionTime : 1800
  # cookie domain
  cookieDomain: "voyahub-test.voyah.cn"
  redis:
    port: 16379
    host: *********
    database: 0
    password: tke@dongfeng123
    maxActive: 80 # redis 数据库连接池参数 -- 最大活跃连接
    maxWait: -1 # redis 数据库连接池参数 -- 获取连接 等待时间
    maxIdle: 80 # redis 数据库连接池参数 -- 最大连接
    minIdle: 20 # redis 数据库连接池参数 -- 最小连接
    timeout: 20000 # redis 数据库连接池参数 -- 超时参数

material:
  file-source-types:
    - key: picture
      label: 图片
      suffixes: [png, jpg, jpeg, gif, bmp]
    - key: video
      label: 视频
      suffixes: [mp4, mov,m4v]
    - key: document
      label: 文档
      suffixes: [pdf, doc, docx, excel, ppt, txt,xls,xlsx]
    - key: package
      label: 压缩包
      suffixes: [zip, rar]
    - key: design
      label: 设计源文件
      suffixes: [sketch, psd]
  user-platform:
    file-source-types:
      - key: picture
        label: 图片
        suffixes: [png, jpg, jpeg, gif, bmp,heic,tif]
      - key: video
        label: 视频
        suffixes: [mp4, mov,m4a,wav,mp3,m4v]
      - key: ppt
        label: PPT
        suffixes: [ ppt ]
      - key: excel
        label: EXCEL
        suffixes: [ excel,xls,xlsx]
      - key: word
        label: WORD
        suffixes: [ doc, docx ]
      - key: pdf
        label: PDF
        suffixes: [ pdf ]
operation:
  log:
    ignore: false
topic:
  log: operation_log_manage
th:
  generate:
    content-types: pdf, doc, docx, excel, ppt, txt,xls,xlsx
manage:
  scheduling:
    active: true
  task:
    cron:
      express: 0 1 0 * * ?
dir:
  prefix:
    origin: /opt/work/
    th: /opt/work/