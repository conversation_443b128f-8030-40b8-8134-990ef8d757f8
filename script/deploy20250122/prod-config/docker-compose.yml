version: '2'
services:
  redis:
    image: redis:7.0.8
    container_name: redis
    environment:
      TZ: Asia/Shanghai
    command: redis-server /data/redis.conf
    volumes:
      - ./data/redis:/data
    restart: unless-stopped
    ports:
      - 6379:6379/tcp
  kkfileview:
    image: keking/kkfileview:4.1.0
    container_name: kkfileview
    ports:
      - "8012:8012"
    environment:
      - TZ=Asia/Shanghai
      - KK_BASE_URL=https://voyahub.voyah.cn/preview
      - KK_CONTEXT_PATH=/preview
  material-server:
    image: material-server:1.0
    container_name: material-server
    ports:
      - "23580:8080"
    volumes:
      - "./config/server:/config"
    environment:
      - TZ=Asia/Shanghai
      - spring.application.name=material-server
      - spring.profiles.active=prod
      - spring.config.location=file:/config/material-server-prod.yml
      - spring.main.allow-bean-definition-overriding=true
  material-portal:
    image: material-portal:1.0
    container_name: material-portal
    ports:
      - "80:80"
    volumes:
      - "./config/material-portal:/etc/nginx/conf.d"
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - material-server
  material-user-server:
    image: material-server:1.0
    container_name: material-user-server
    ports:
      - "23580:8080"
    volumes:
      - "./config/server:/config"
    environment:
      - TZ=Asia/Shanghai
      - spring.application.name=material-user-server
      - spring.profiles.active=prod
      - spring.config.location=file:/config/material-user-server-prod.yml
      - spring.main.allow-bean-definition-overriding=true
  user-portal:
    image: user-portal:1.0
    container_name: user-portal
    ports:
      - "443:443"
    volumes:
      - "./config/user-portal:/etc/nginx/conf.d"
      - /data/ssl/voyah_cn.cer:/etc/nginx/voyah_cn.cer
      - /data/ssl/voyah_cn_private.key:/etc/nginx/voyah_cn_private.key
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - material-user-server
      - material-portal
      - material-server
      - kkfileview