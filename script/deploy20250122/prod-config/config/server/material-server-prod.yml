spring:
  application:
    name: material-server
  #  servlet:
  #    multipart:
  #      max-file-size: 10MB
  #      max-request-size: 10MB
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://************:13306/voyahub?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true
    username: ${MYSQL_USER:mysqladmin}
    password: ${MYSQL_PASSWORD:PaaS2025!@#}
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    port: 6379
    host: redis
    database: 0
    password: FQwbErDWXaXTBzt5VRvs5aFE2dbr2SBCzCxGBdZCvS2RGDxc
  auth:
    permitted-url:
  #    - /material-library/*
  #    - /origin-material/*
  #    - /file/*
  #    - /file-part/*
mybatis:
  configuration:
    map-underscore-to-camel-case: true
logging:
  config: classpath:logback.xml
cos:
  secretId: AKIDgJsU1sc8PTL0SbbS7Yqz0rY5cFoIafYJ
  secretKey: d2KPL4nSzLUSEpDRTM2Z1JPJ0UntoY6U
  appId: 1307512833
  bucket: voyahub-1301141550
  region: ap-nanjing
  url: https://voyahub-1301141550.cos.ap-nanjing.myqcloud.com/
  prefix: prod/
  duration: 7200
inc-config:
  appId: 00750593
  appSecret: 5c72defd-2b67-410c-8099-3dcef8e72517
  incServer: https://center.dsmp.voyah.com.cn
  sessionKey : "sessionId"
  sessionTime : 1800
  cookieDomain: "voyahub.voyah.cn"
  redis:
    port: 6379
    host: redis
    database: 0
    password: FQwbErDWXaXTBzt5VRvs5aFE2dbr2SBCzCxGBdZCvS2RGDxc
    maxActive: 80
    maxWait: -1
    maxIdle: 80
    minIdle: 20
    timeout: 20000
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
  show-actuator: false
knife4j:
  production: true
material:
  file-source-types:
    - key: picture
      label: 图片
      suffixes: [png, jpg, jpeg, gif, bmp]
    - key: video
      label: 视频
      suffixes: [mp4, mov,m4v]
    - key: document
      label: 文档
      suffixes: [pdf, doc, docx, excel, ppt, txt]
    - key: package
      label: 压缩包
      suffixes: [zip, rar]
    - key: design
      label: 设计源文件
      suffixes: [sketch, psd]
  user-platform:
    file-source-types:
      - key: picture
        label: 图片
        suffixes: [png, jpg, jpeg, gif, bmp,heic,tif]
      - key: video
        label: 视频
        suffixes: [mp4, mov,m4a,wav,mp3,m4v]
      - key: ppt
        label: PPT
        suffixes: [ ppt ]
      - key: excel
        label: EXCEL
        suffixes: [ excel,xls,xlsx]
      - key: word
        label: WORD
        suffixes: [ doc, docx ]
      - key: pdf
        label: PDF
        suffixes: [ pdf ]
operation:
  log:
    ignore: false
topic:
  log: operation_log_manage
th:
  generate:
    content-types: pdf, doc, docx, excel, ppt, txt,xls,xlsx
manage:
  scheduling:
    active: true
  task:
    cron:
      express: 0 1 0 * * ?
dir:
  prefix:
    origin: /opt/work/
    th: /opt/work/