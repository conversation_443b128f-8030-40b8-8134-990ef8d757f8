-- material.file_detail definition

CREATE TABLE `file_detail` (
  `id` bigint NOT NULL COMMENT '文件id',
  `url` varchar(512) NOT NULL COMMENT '文件访问地址',
  `size` bigint DEFAULT NULL COMMENT '文件大小，单位字节',
  `filename` varchar(256) DEFAULT NULL COMMENT '文件名称',
  `original_filename` varchar(256) DEFAULT NULL COMMENT '原始文件名',
  `base_path` varchar(256) DEFAULT NULL COMMENT '基础存储路径',
  `path` varchar(256) DEFAULT NULL COMMENT '存储路径',
  `ext` varchar(32) DEFAULT NULL COMMENT '文件扩展名',
  `content_type` varchar(128) DEFAULT NULL COMMENT 'MIME类型',
  `platform` varchar(32) DEFAULT NULL COMMENT '存储平台',
  `th_url` varchar(512) DEFAULT NULL COMMENT '缩略图访问路径',
  `th_filename` varchar(256) DEFAULT NULL COMMENT '缩略图名称',
  `th_size` bigint DEFAULT NULL COMMENT '缩略图大小，单位字节',
  `th_content_type` varchar(128) DEFAULT NULL COMMENT '缩略图MIME类型',
  `object_id` varchar(32) DEFAULT NULL COMMENT '文件所属对象id',
  `object_type` varchar(32) DEFAULT NULL COMMENT '文件所属对象类型，例如用户头像，评价图片',
  `metadata` text COMMENT '文件元数据',
  `user_metadata` text COMMENT '文件用户元数据',
  `th_metadata` text COMMENT '缩略图元数据',
  `th_user_metadata` text COMMENT '缩略图用户元数据',
  `attr` text COMMENT '附加属性',
  `file_acl` varchar(32) DEFAULT NULL COMMENT '文件ACL',
  `th_file_acl` varchar(32) DEFAULT NULL COMMENT '缩略图文件ACL',
  `hash_info` text COMMENT '哈希信息',
  `upload_id` varchar(128) DEFAULT NULL COMMENT '上传ID，仅在手动分片上传时使用',
  `upload_status` int DEFAULT NULL COMMENT '上传状态，仅在手动分片上传时使用，1：初始化完成，2：上传完成',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `is_delete` tinyint DEFAULT NULL,
  `create_by` varchar(255) DEFAULT NULL,
  `update_by` varchar(255) DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `version` varchar(255) DEFAULT NULL,
  `create_short_id` varchar(32) DEFAULT NULL COMMENT '创建人短ID',
  `update_short_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='文件记录表';


-- material.file_part definition

CREATE TABLE `file_part` (
  `id` bigint NOT NULL COMMENT '分片id',
  `platform` varchar(32) DEFAULT NULL COMMENT '存储平台',
  `upload_id` varchar(128) DEFAULT NULL COMMENT '上传ID，仅在手动分片上传时使用',
  `e_tag` varchar(255) DEFAULT NULL COMMENT '分片 ETag',
  `part_number` int DEFAULT NULL COMMENT '分片号。每一个上传的分片都有一个分片号，一般情况下取值范围是1~10000',
  `part_size` bigint DEFAULT NULL COMMENT '文件大小，单位字节',
  `hash_info` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '哈希信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `is_delete` tinyint(1) DEFAULT NULL,
  `create_by` varchar(255) DEFAULT NULL,
  `update_by` varchar(255) DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `version` varchar(255) DEFAULT NULL,
  `create_short_id` varchar(32) DEFAULT NULL,
  `update_short_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='文件分片信息表，仅在手动分片上传时使用';


-- material.material_library definition

CREATE TABLE `material_library` (
  `id` bigint NOT NULL COMMENT '素材ID',
  `library_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '素材库名称',
  `is_public` tinyint DEFAULT NULL COMMENT '是否公开 0：否  1：是',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `version` bigint DEFAULT NULL,
  `is_delete` tinyint DEFAULT '0',
  `status` tinyint DEFAULT '0' COMMENT '启用状态 0：禁用 1：启用',
  `sort` int DEFAULT NULL COMMENT '排序',
  `origin_material_id` bigint DEFAULT NULL COMMENT '原始素材主文件夹ID',
  `library_no` varchar(10) DEFAULT NULL COMMENT '素材库编号',
  `create_short_id` varchar(32) DEFAULT NULL COMMENT '创建人短ID',
  `update_short_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='素材库（类型）';


-- material.origin_material definition

CREATE TABLE `origin_material` (
  `id` bigint NOT NULL COMMENT '素材ID',
  `material_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '素材名称',
  `library_id` bigint DEFAULT '0' COMMENT '素材类型 1：产品素材 2：传播素材',
  `is_public` tinyint DEFAULT NULL COMMENT '是否公开 0：否  1：是',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `version` bigint DEFAULT NULL,
  `is_delete` tinyint(1) DEFAULT '0',
  `is_dir` tinyint DEFAULT '0' COMMENT '是否是目录 0：否  1：是',
  `level` tinyint DEFAULT '0' COMMENT '素材层级 0级为素材库',
  `is_show` tinyint DEFAULT '0' COMMENT '是否显示',
  `parent_id` bigint DEFAULT '0' COMMENT '层级父级ID,当前层级为素材库的话,parentId为0',
  `file_size` bigint DEFAULT '0' COMMENT '目录及文件大小 Byte',
  `file_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '文件类型 如；pdf、img',
  `file_id` bigint DEFAULT '0' COMMENT '文件本身的id，如果为文件夹的话，默认为0',
  `last_upload_user` varchar(255) DEFAULT NULL COMMENT '最后上传人',
  `last_upload_time` timestamp NULL DEFAULT NULL COMMENT '最后上传时间',
  `sort` int DEFAULT '0' COMMENT '排序',
  `parent_id_list` json DEFAULT NULL COMMENT '父级parentIdList',
  `create_short_id` varchar(32) DEFAULT NULL COMMENT '创建人短ID',
  `update_short_id` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='素材';





ALTER TABLE `origin_material`
    add COLUMN `file_count` bigint(0) NULL DEFAULT NULL COMMENT '文件个数' ;