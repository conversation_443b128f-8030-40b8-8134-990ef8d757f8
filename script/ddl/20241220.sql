
ALTER TABLE `origin_material`
    add COLUMN `tag_status` tinyint NULL DEFAULT NULL COMMENT '标注状态' ;
UPDATE `origin_material` SET `tag_status` = 0 where tag_status is null;

CREATE TABLE `material_auth` (
                                 `id` bigint NOT NULL,
                                 `short_id` varchar(255) NOT NULL,
                                 `user_name` varchar(255)  NOT NULL,
                                 `material_id_list` json DEFAULT NULL,
                                 `create_time` timestamp NULL DEFAULT NULL,
                                 `update_time` timestamp NULL DEFAULT NULL,
                                 `create_short_id` varchar(255) DEFAULT NULL,
                                 `update_short_id` varchar(255) DEFAULT NULL,
                                 `create_by` varchar(255) DEFAULT NULL,
                                 `update_by` varchar(255) DEFAULT NULL,
                                 `is_delete` tinyint DEFAULT NULL,
                                 `version` bigint DEFAULT NULL,
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='数据权限表';

CREATE TABLE `tag_category` (
                                `id` bigint NOT NULL,
                                `name` varchar(255) DEFAULT NULL,
                                `sort` bigint DEFAULT NULL COMMENT '排序',
                                `desc` varchar(255) DEFAULT NULL,
                                `required` tinyint DEFAULT NULL,
                                `create_by` varchar(32)  DEFAULT NULL,
                                `create_time` timestamp NULL DEFAULT NULL,
                                `update_by` varchar(32)  DEFAULT NULL,
                                `update_time` timestamp NULL DEFAULT NULL,
                                `version` bigint DEFAULT NULL,
                                `is_delete` tinyint DEFAULT '0',
                                `status` tinyint DEFAULT NULL COMMENT '状态 0：停用  1：启用',
                                `create_short_id` varchar(255) DEFAULT NULL,
                                `update_short_id` varchar(255) DEFAULT NULL,
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='标签分类表';
CREATE TABLE `tag_info` (
                            `id` bigint NOT NULL COMMENT '字典主键',
                            `name` varchar(50) NOT NULL COMMENT '字典名称',
                            `code` varchar(50)  NOT NULL COMMENT '编码',
                            `tag_value` varchar(512)  NOT NULL COMMENT '字典描述',
                            `category_id` bigint NOT NULL COMMENT '类目id',
                            `parent_id` bigint NOT NULL COMMENT '父级id',
                            `level` tinyint NOT NULL COMMENT '层级',
                            `required` tinyint DEFAULT NULL COMMENT '状态 0：非必填  1：必填',
                            `create_by` varchar(32)  DEFAULT NULL,
                            `create_time` timestamp NULL DEFAULT NULL,
                            `update_by` varchar(32)  DEFAULT NULL,
                            `update_time` timestamp NULL DEFAULT NULL,
                            `version` bigint DEFAULT NULL,
                            `is_delete` tinyint DEFAULT '0',
                            `create_short_id` varchar(255) DEFAULT NULL,
                            `update_short_id` varchar(255) DEFAULT NULL,
                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';
CREATE TABLE `material_tag` (
                                `id` bigint NOT NULL,
                                `material_id` bigint NOT NULL,
                                `remark` varchar(255) DEFAULT NULL,
                                `create_by` varchar(255) DEFAULT NULL,
                                `update_by` varchar(255) DEFAULT NULL,
                                `create_time` timestamp NULL DEFAULT NULL,
                                `update_time` timestamp NULL DEFAULT NULL,
                                `create_short_id` varchar(255) DEFAULT NULL,
                                `update_short_id` varchar(255)  DEFAULT NULL,
                                `is_delete` tinyint DEFAULT '0',
                                `version` bigint DEFAULT NULL,
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `pk_material_id` (`material_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='素材标签表';

CREATE TABLE `material_tag_item` (
                                     `id` bigint NOT NULL COMMENT '字典主键',
                                     `name` varchar(50)  NOT NULL COMMENT '字典名称',
                                     `tag_value` varchar(512)  DEFAULT NULL COMMENT '字典描述',
                                     `tag_id` bigint NOT NULL COMMENT '字典主键',
                                     `material_id` bigint NOT NULL,
                                     `material_tag_id` bigint NOT NULL COMMENT '素材标签表Id',
                                     `category_id` bigint NOT NULL COMMENT '类目id',
                                     `parent_id` bigint NOT NULL COMMENT '父级id',
                                     `level` tinyint NOT NULL COMMENT '层级',
                                     `required` tinyint DEFAULT NULL COMMENT '状态 0：非必填  1：必填',
                                     `create_by` varchar(32)  DEFAULT NULL,
                                     `create_time` timestamp NULL DEFAULT NULL,
                                     `update_by` varchar(32)  DEFAULT NULL,
                                     `update_time` timestamp NULL DEFAULT NULL,
                                     `version` bigint DEFAULT NULL,
                                     `is_delete` tinyint DEFAULT '0',
                                     `create_short_id` varchar(255) DEFAULT NULL,
                                     `update_short_id` varchar(255) DEFAULT NULL,
                                     `code` varchar(255) DEFAULT NULL,
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='素材标签明细表';

INSERT INTO `tag_category` (`id`, `name`, `sort`, `desc`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `status`, `create_short_id`, `update_short_id`) VALUES (227595881758564352, '车辆属性', 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL);
INSERT INTO `tag_category` (`id`, `name`, `sort`, `desc`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `status`, `create_short_id`, `update_short_id`) VALUES (227596140471623680, '产品亮点', 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL);
INSERT INTO `tag_category` (`id`, `name`, `sort`, `desc`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `status`, `create_short_id`, `update_short_id`) VALUES (227596211024011264, '运营活动', 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL);
INSERT INTO `tag_category` (`id`, `name`, `sort`, `desc`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `status`, `create_short_id`, `update_short_id`) VALUES (227596285976223744, '其他', 4, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, NULL, NULL);


INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227612996385361920, '车系名称', '车系名称', '梦想家', 227595881758564352, 0, 1, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227613537962188800, '车系名称', '车系名称', 'FREE', 227595881758564352, 0, 1, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227614306929106944, '年代款', '年代款', '2022', 227595881758564352, 227612996385361920, 2, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227614355020996608, '年代款', '年代款', '2024', 227595881758564352, 227612996385361920, 2, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227614377426968576, '年代款', '年代款', '2025', 227595881758564352, 227612996385361920, 2, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227614512030572544, '车系名称', '车系名称', '追光', 227595881758564352, 0, 1, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227614799252316160, '车系名称', '车系名称', '知音', 227595881758564352, 0, 1, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227615036696059904, '年代款', '年代款', '2024', 227595881758564352, 227614799252316160, 2, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227615255273824256, '能源形式', '能源形式', 'PHEV/插电式混动', 227595881758564352, 227614306929106944, 3, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227615302266806272, '能源形式', '能源形式', 'EV/纯电', 227595881758564352, 227614306929106944, 3, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (227615535944065024, '能源形式', '能源形式', 'EV/纯电', 227595881758564352, 227615036696059904, 3, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL);
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229506074893307904, '外观亮点', '外观亮点', '外观亮点', 227596140471623680, 0, 1, NULL, '', '2024-12-25 20:36:12', '', '2024-12-25 20:36:12', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229506204811874304, '内饰亮点', '内饰亮点', '内饰亮点', 227596140471623680, 229506074893307904, 2, NULL, '', '2024-12-25 20:36:43', '', '2024-12-25 20:36:43', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229535472195444736, '车展', '车展', '车展', 227596211024011264, 0, 1, NULL, '', '2024-12-25 22:33:01', '', '2024-12-25 22:33:01', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229537120934404096, '车型名称', '车型名称', '低碳版 梦', 227595881758564352, 227615255273824256, 4, NULL, '', '2024-12-25 22:39:34', '', '2024-12-25 22:39:34', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229537208163344384, '车型名称', '车型名称', '低碳版 想', 227595881758564352, 227615255273824256, 4, NULL, '', '2024-12-25 22:39:55', '', '2024-12-25 22:39:55', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229537236663640064, '车型名称', '车型名称', '低碳版 家', 227595881758564352, 227615255273824256, 4, NULL, '', '2024-12-25 22:40:02', '', '2024-12-25 22:40:02', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229537358826938368, '车型名称', '车型名称', '0碳版 梦', 227595881758564352, 227615302266806272, 4, NULL, '', '2024-12-25 22:40:31', '', '2024-12-25 22:40:31', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229537388371615744, '车型名称', '车型名称', '0碳版 想', 227595881758564352, 227615302266806272, 4, NULL, '', '2024-12-25 22:40:38', '', '2024-12-25 22:40:38', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229537418050510848, '车型名称', '车型名称', '0碳版 家', 227595881758564352, 227615302266806272, 4, NULL, '', '2024-12-25 22:40:45', '', '2024-12-25 22:40:45', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229538594603114496, '能源形式', '能源形式', 'PHEV/插电式混动', 227595881758564352, 227614355020996608, 3, NULL, '', '2024-12-25 22:45:26', '', '2024-12-25 22:45:26', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229538673388920832, '能源形式', '能源形式', 'EV/纯电', 227595881758564352, 227614355020996608, 3, NULL, '', '2024-12-25 22:45:44', '', '2024-12-25 22:45:44', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229538872475754496, '能源形式', '能源形式', 'PHEV/插电式混动', 227595881758564352, 227614377426968576, 3, NULL, '', '2024-12-25 22:46:32', '', '2024-12-25 22:46:32', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229538937227419648, '能源形式', '能源形式', 'EV/纯电', 227595881758564352, 227614377426968576, 3, NULL, '', '2024-12-25 22:46:47', '', '2024-12-25 22:46:47', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229539742399569920, '年代款', '年代款', '2021', 227595881758564352, 227613537962188800, 2, NULL, '', '2024-12-25 22:49:59', '', '2024-12-25 22:49:59', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229539790860558336, '年代款', '年代款', '2022', 227595881758564352, 227613537962188800, 2, NULL, '', '2024-12-25 22:50:11', '', '2024-12-25 22:50:11', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229539887434407936, '年代款', '年代款', '2023', 227595881758564352, 227613537962188800, 2, NULL, '', '2024-12-25 22:50:34', '', '2024-12-25 22:50:34', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229539937145298944, '年代款', '年代款', '2024', 227595881758564352, 227613537962188800, 2, NULL, '', '2024-12-25 22:50:46', '', '2024-12-25 22:50:46', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229539979184807936, '年代款', '年代款', '2025', 227595881758564352, 227613537962188800, 2, NULL, '', '2024-12-25 22:50:56', '', '2024-12-25 22:50:56', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229540154506715136, '年代款', '年代款', '2023', 227595881758564352, 227614512030572544, 2, NULL, '', '2024-12-25 22:51:37', '', '2024-12-25 22:51:37', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229540240624164864, '年代款', '年代款', '2024', 227595881758564352, 227614512030572544, 2, NULL, '', '2024-12-25 22:51:58', '', '2024-12-25 22:51:58', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229541100175466496, '能源形式', '能源形式', 'REEV/增程式', 227595881758564352, 229539742399569920, 3, NULL, '', '2024-12-25 22:55:23', '', '2024-12-25 22:55:23', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229541173848416256, '能源形式', '能源形式', 'EV/纯电', 227595881758564352, 229539742399569920, 3, NULL, '', '2024-12-25 22:55:41', '', '2024-12-25 22:55:41', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229541284951334912, '能源形式', '能源形式', 'REEV/增程式', 227595881758564352, 229539790860558336, 3, NULL, '', '2024-12-25 22:56:07', '', '2024-12-25 22:56:07', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229541354140573696, '能源形式', '能源形式', 'EV/纯电', 227595881758564352, 229539790860558336, 3, NULL, '', '2024-12-25 22:56:23', '', '2024-12-25 22:56:23', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229541438894874624, '能源形式', '能源形式', 'REEV/增程式', 227595881758564352, 229539887434407936, 3, NULL, '', '2024-12-25 22:56:44', '', '2024-12-25 22:56:44', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229541574215704576, '能源形式', '能源形式', 'REEV/增程式', 227595881758564352, 229539937145298944, 3, NULL, '', '2024-12-25 22:57:16', '', '2024-12-25 22:57:16', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229541694739030016, '能源形式', '能源形式', 'REEV/增程式', 227595881758564352, 229539979184807936, 3, NULL, '', '2024-12-25 22:57:45', '', '2024-12-25 22:57:45', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229541822535278592, '能源形式', '能源形式', 'EV/纯电', 227595881758564352, 229540154506715136, 3, NULL, '', '2024-12-25 22:58:15', '', '2024-12-25 22:58:15', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229541931612348416, '能源形式', '能源形式', 'PHEV/插电式混动', 227595881758564352, 229540240624164864, 3, NULL, '', '2024-12-25 22:58:41', '', '2024-12-25 22:58:41', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229542933392498688, '车型名称', '车型名称', '超长续航卓越版', 227595881758564352, 229538594603114496, 4, NULL, '', '2024-12-25 23:02:40', '', '2024-12-25 23:02:40', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229542959158108160, '车型名称', '车型名称', '超长续航尊贵版', 227595881758564352, 229538594603114496, 4, NULL, '', '2024-12-25 23:02:46', '', '2024-12-25 23:02:46', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229543026220834816, '车型名称', '车型名称', '超长续航旗舰版', 227595881758564352, 229538594603114496, 4, NULL, '', '2024-12-25 23:03:02', '', '2024-12-25 23:03:02', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229543268135706624, '车型名称', '车型名称', '长续航卓越版', 227595881758564352, 229538673388920832, 4, NULL, '', '2024-12-25 23:04:00', '', '2024-12-25 23:04:00', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229543348477599744, '车型名称', '车型名称', '长续航尊贵版', 227595881758564352, 229538673388920832, 4, NULL, '', '2024-12-25 23:04:19', '', '2024-12-25 23:04:19', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229543401049006080, '车型名称', '车型名称', '超长续航卓越版', 227595881758564352, 229538673388920832, 4, NULL, '', '2024-12-25 23:04:32', '', '2024-12-25 23:04:32', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229543492950401024, '车型名称', '车型名称', '超长续航尊贵版', 227595881758564352, 229538673388920832, 4, NULL, '', '2024-12-25 23:04:53', '', '2024-12-25 23:04:53', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229543543760199680, '车型名称', '车型名称', '超长续航旗舰版', 227595881758564352, 229538673388920832, 4, NULL, '', '2024-12-25 23:05:06', '', '2024-12-25 23:05:06', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229543763839524864, '车型名称', '车型名称', '四驱卓越鲲鹏版', 227595881758564352, 229538872475754496, 4, NULL, '', '2024-12-25 23:05:58', '', '2024-12-25 23:05:58', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229543840238772224, '车型名称', '车型名称', '四驱尊贵鲲鹏版', 227595881758564352, 229538872475754496, 4, NULL, '', '2024-12-25 23:06:16', '', '2024-12-25 23:06:16', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229543897830760448, '车型名称', '车型名称', '四驱旗舰鲲鹏版', 227595881758564352, 229538872475754496, 4, NULL, '', '2024-12-25 23:06:30', '', '2024-12-25 23:06:30', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229543949903044608, '车型名称', '车型名称', '四驱卓越乾坤版', 227595881758564352, 229538872475754496, 4, NULL, '', '2024-12-25 23:06:42', '', '2024-12-25 23:06:42', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544102265331712, '车型名称', '车型名称', '四驱卓越鲲鹏版', 227595881758564352, 229538937227419648, 4, NULL, '', '2024-12-25 23:07:19', '', '2024-12-25 23:07:19', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544185396436992, '车型名称', '车型名称', '四驱尊贵鲲鹏版', 227595881758564352, 229538937227419648, 4, NULL, '', '2024-12-25 23:07:39', '', '2024-12-25 23:07:39', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544258029199360, '车型名称', '车型名称', '四驱旗舰鲲鹏版', 227595881758564352, 229538937227419648, 4, NULL, '', '2024-12-25 23:07:56', '', '2024-12-25 23:07:56', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544321103142912, '车型名称', '车型名称', '四驱卓越乾坤版', 227595881758564352, 229538937227419648, 4, NULL, '', '2024-12-25 23:08:11', '', '2024-12-25 23:08:11', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544467874422784, '车型名称', '车型名称', '长续航智享版', 227595881758564352, 227615535944065024, 4, NULL, '', '2024-12-25 23:08:46', '', '2024-12-25 23:08:46', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544520802344960, '车型名称', '车型名称', '长续航超充版', 227595881758564352, 227615535944065024, 4, NULL, '', '2024-12-25 23:08:58', '', '2024-12-25 23:08:58', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544573755432960, '车型名称', '车型名称', '超长续航智享版', 227595881758564352, 227615535944065024, 4, NULL, '', '2024-12-25 23:09:11', '', '2024-12-25 23:09:11', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544643930333184, '车型名称', '车型名称', '四驱全球版', 227595881758564352, 227615535944065024, 4, NULL, '', '2024-12-25 23:09:28', '', '2024-12-25 23:09:28', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544749345775616, '车型名称', '车型名称', '四驱标准增程版', 227595881758564352, 229541100175466496, 4, NULL, '', '2024-12-25 23:09:53', '', '2024-12-25 23:09:53', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544805452980224, '车型名称', '车型名称', '四驱增程首发纪念版', 227595881758564352, 229541100175466496, 4, NULL, '', '2024-12-25 23:10:06', '', '2024-12-25 23:10:06', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544886805700608, '车型名称', '车型名称', '四驱标准增程专属豪华套装', 227595881758564352, 229541100175466496, 4, NULL, '', '2024-12-25 23:10:26', '', '2024-12-25 23:10:26', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229544943449776128, '车型名称', '车型名称', '四驱增程首发纪念版尊享包', 227595881758564352, 229541100175466496, 4, NULL, '', '2024-12-25 23:10:39', '', '2024-12-25 23:10:39', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545030389309440, '车型名称', '车型名称', '两驱纯电首发纪念版', 227595881758564352, 229541173848416256, 4, NULL, '', '2024-12-25 23:11:00', '', '2024-12-25 23:11:00', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545108260757504, '车型名称', '车型名称', '两驱标准纯电版', 227595881758564352, 229541173848416256, 4, NULL, '', '2024-12-25 23:11:19', '', '2024-12-25 23:11:19', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545163894005760, '车型名称', '车型名称', '两驱标准纯电城市版', 227595881758564352, 229541173848416256, 4, NULL, '', '2024-12-25 23:11:32', '', '2024-12-25 23:11:32', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545217480433664, '车型名称', '车型名称', '四驱纯电首发纪念版尊享包', 227595881758564352, 229541173848416256, 4, NULL, '', '2024-12-25 23:11:45', '', '2024-12-25 23:11:45', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545273486974976, '车型名称', '车型名称', '四驱标准纯电专属豪华套装', 227595881758564352, 229541173848416256, 4, NULL, '', '2024-12-25 23:11:58', '', '2024-12-25 23:11:58', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545366432751616, '车型名称', '车型名称', 'DNA增程版', 227595881758564352, 229541284951334912, 4, NULL, '', '2024-12-25 23:12:20', '', '2024-12-25 23:12:20', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545446745284608, '车型名称', '车型名称', 'DNA纯电版', 227595881758564352, 229541354140573696, 4, NULL, '', '2024-12-25 23:12:39', '', '2024-12-25 23:12:39', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545526537723904, '车型名称', '车型名称', '四驱超长续航纯电版', 227595881758564352, 229541354140573696, 4, NULL, '', '2024-12-25 23:12:58', '', '2024-12-25 23:12:58', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545649514717184, '车型名称', '车型名称', '四驱超长续航纯电版', 227595881758564352, 229541438894874624, 4, NULL, '', '2024-12-25 23:13:28', '', '2024-12-25 23:13:28', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545719563788288, '车型名称', '车型名称', '超长续航智驾版', 227595881758564352, 229541574215704576, 4, NULL, '', '2024-12-25 23:13:44', '', '2024-12-25 23:13:44', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545810798288896, '车型名称', '车型名称', '后驱环游版', 227595881758564352, 229541694739030016, 4, NULL, '', '2024-12-25 23:14:06', '', '2024-12-25 23:14:06', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545876812439552, '车型名称', '车型名称', '四驱探索版', 227595881758564352, 229541694739030016, 4, NULL, '', '2024-12-25 23:14:22', '', '2024-12-25 23:14:22', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229545979182817280, '车型名称', '车型名称', '标准续航版', 227595881758564352, 229541822535278592, 4, NULL, '', '2024-12-25 23:14:46', '', '2024-12-25 23:14:46', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229546057716965376, '车型名称', '车型名称', '长续航版', 227595881758564352, 229541822535278592, 4, NULL, '', '2024-12-25 23:15:05', '', '2024-12-25 23:15:05', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229546148557201408, '车型名称', '车型名称', '超长续航四驱行政版', 227595881758564352, 229541931612348416, 4, NULL, '', '2024-12-25 23:15:27', '', '2024-12-25 23:15:27', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229546252743712768, '车型名称', '车型名称', '超长续航四驱旗舰版', 227595881758564352, 229541931612348416, 4, NULL, '', '2024-12-25 23:15:51', '', '2024-12-25 23:15:51', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229546566616064000, '外观颜色', '外观颜色', '杜若白', 227595881758564352, 229542933392498688, 5, NULL, '', '2024-12-25 23:17:06', '', '2024-12-25 23:17:06', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229546643527016448, '外观颜色', '外观颜色', '玄英黑', 227595881758564352, 229542959158108160, 5, NULL, '', '2024-12-25 23:17:25', '', '2024-12-25 23:17:25', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229546720479911936, '外观颜色', '外观颜色', '旭日紫', 227595881758564352, 229543026220834816, 5, NULL, '', '2024-12-25 23:17:43', '', '2024-12-25 23:17:43', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229546791690805248, '外观颜色', '外观颜色', '日曜金', 227595881758564352, 229543268135706624, 5, NULL, '', '2024-12-25 23:18:00', '', '2024-12-25 23:18:00', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229546880937205760, '外观颜色', '外观颜色', '杜若白', 227595881758564352, 229543763839524864, 5, NULL, '', '2024-12-25 23:18:21', '', '2024-12-25 23:18:21', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229546958234034176, '外观颜色', '外观颜色', '玄英黑', 227595881758564352, 229543840238772224, 5, NULL, '', '2024-12-25 23:18:40', '', '2024-12-25 23:18:40', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229547029927272448, '外观颜色', '外观颜色', '旭日紫', 227595881758564352, 229543897830760448, 5, NULL, '', '2024-12-25 23:18:57', '', '2024-12-25 23:18:57', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229547124785651712, '外观颜色', '外观颜色', '辉月青', 227595881758564352, 229543949903044608, 5, NULL, '', '2024-12-25 23:19:19', '', '2024-12-25 23:19:19', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229547193098280960, '外观颜色', '外观颜色', '玄夜金', 227595881758564352, 229544102265331712, 5, NULL, '', '2024-12-25 23:19:36', '', '2024-12-25 23:19:36', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229547258835607552, '外观颜色', '外观颜色', '海月金', 227595881758564352, 229544185396436992, 5, NULL, '', '2024-12-25 23:19:51', '', '2024-12-25 23:19:51', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229547354444767232, '外观颜色', '外观颜色', '日夜辉', 227595881758564352, 229544258029199360, 5, NULL, '', '2024-12-25 23:20:14', '', '2024-12-25 23:20:14', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229547454676049920, '外观颜色', '外观颜色', '曼波绿', 227595881758564352, 229544467874422784, 5, NULL, '', '2024-12-25 23:20:38', '', '2024-12-25 23:20:38', 0, 0, '', '');
INSERT INTO `tag_info` (`id`, `name`, `code`, `tag_value`, `category_id`, `parent_id`, `level`, `required`, `create_by`, `create_time`, `update_by`, `update_time`, `version`, `is_delete`, `create_short_id`, `update_short_id`) VALUES (229547525865971712, '外观颜色', '外观颜色', '星云灰', 227595881758564352, 229544520802344960, 5, NULL, '', '2024-12-25 23:20:55', '', '2024-12-25 23:20:55', 0, 0, '', '');
