CREATE TABLE `client_info` (
                               `id` bigint NOT NULL,
                               `name` varchar(255) NOT NULL,
                               `current_version` varchar(255) NOT NULL,
                               `type` int NOT NULL,
                               `active` int NOT NULL,
                               `remark` varchar(255) DEFAULT NULL,
                               `file_id` bigint DEFAULT NULL,
                               `create_time` timestamp NULL DEFAULT NULL COMMENT '创建人',
                               `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                               `create_by` varchar(255) DEFAULT NULL COMMENT '创建人姓名',
                               `update_by` varchar(255) DEFAULT NULL COMMENT '更新人姓名',
                               `is_delete` tinyint DEFAULT NULL COMMENT '是否已删除',
                               `version` bigint DEFAULT NULL,
                               `update_short_id` varchar(255) DEFAULT NULL,
                               `create_short_id` varchar(255) DEFAULT NULL,
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB ;