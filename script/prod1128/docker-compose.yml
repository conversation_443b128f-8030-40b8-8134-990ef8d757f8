services:
  redis:
    image: redis:7.0.8
    container_name: redis
    environment:
      TZ: Asia/Shanghai
    command: redis-server /data/redis.conf
    volumes:
      - ./data/redis:/data
    restart: unless-stopped
    ports:
      - 6379:6379/tcp
  material-server:
    image: material-server:1.0
    container_name: material-server
    ports:
      - "23580:8080"
    volumes:
      - "./config/server:/config"
    environment:
      - TZ=Asia/Shanghai
      - spring.application.name=material-server
      - spring.profiles.active=prod
      - spring.config.location=file:/config/material-server-prod.yml
      - spring.main.allow-bean-definition-overriding=true
  material-portal:
    image: material-portal:1.0
    container_name: material-portal
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - "./config/conf.d/:/etc/nginx/conf.d/"
      - /data/ssl/voyah_cn.cer:/etc/nginx/voyah_cn.cer
      - /data/ssl/voyah_cn_private.key:/etc/nginx/voyah_cn_private.key
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - material-server