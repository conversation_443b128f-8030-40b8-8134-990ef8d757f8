<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.voyah.material.mapper.MaterialAuthMapper">


    <select id="getAllAuth" resultType="cn.com.voyah.material.pojo.MaterialAuthPO">
        select * from material_auth main where
            main.is_delete = 0
        <foreach collection="materialIdList" item="item" >
            and JSON_CONTAINS(material_id_list, #{item})
        </foreach>
    </select>
</mapper>