<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.voyah.material.mapper.OriginMaterialMapper">
    <resultMap type="cn.com.voyah.material.pojo.OriginMaterialPO" id="originMaterial">
        <id column="id" property="id"/>
        <result column="material_name" property="materialName"/>
        <result column="library_id" property="libraryId"/>
        <result column="is_public" property="isPublic"/>
        <result column="is_dir" property="isDir"/>
        <result column="level" property="level"/>
        <result column="is_show" property="isShow"/>
        <result column="parent_id" property="parentId"/>
        <result column="file_size" property="fileSize"/>
        <result column="file_type" property="fileType"/>
        <result column="file_id" property="fileId"/>
        <result column="file_count" property="fileCount"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="library_id" property="libraryId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_short_id" property="createShortId"/>
        <result column="last_upload_time" property="lastUploadTime"/>
        <result column="parent_id_list" property="parentIdList"  javaType="java.util.List"  typeHandler="cn.com.voyah.material.mybatisflex.handle.LongListTypeHandler"/>
        <result column="last_upload_time" property="lastUploadTime"/>
        <result column="tag_status" property="tagStatus"/>
    </resultMap>
    <resultMap type="cn.com.voyah.material.dto.OriginMaterialExtensionDTO" id="materialExtensionDTO" extends="originMaterial">
        <result column="platform" property="platform"/>
        <result column="base_path" property="basePath"/>
        <result column="filename" property="filename"/>
        <result column="url" property="url"/>
        <result column="upload_id" property="uploadId"/>
        <result column="upload_status" property="uploadStatus"/>
        <result column="th_filename" property="thFilename"/>
        <result column="th_url" property="thUrl"/>
        <result column="th_content_type" property="thContentType"/>
    </resultMap>
    <sql id="relationColumns">
        om.*,f.platform,f.base_path,f.filename,f.url,f.upload_id,f.upload_status,f.th_filename,f.th_url,f.th_content_type
    </sql>
    <sql id="pageInfoSql">
        from origin_material om
                 left join file_detail f on om.file_id=f.id
        where  om.is_delete=0
    </sql>
    <sql id="wherePageSql">
        <if test="originMaterialDTO.isShow!=null">
            AND om.is_show=#{originMaterialDTO.isShow}
        </if>
        <if test="originMaterialDTO.isPublic!=null">
            AND om.is_public=#{originMaterialDTO.isPublic}
        </if>
        <if test="originMaterialDTO.isDir!=null">
            AND om.is_dir=#{originMaterialDTO.isDir}
        </if>
        <if test="originMaterialDTO.tagStatus!=null">
            AND om.tag_status=#{originMaterialDTO.tagStatus}
        </if>
        <if test="originMaterialDTO.materialName!=null and originMaterialDTO.materialName!=''">
            AND om.material_name like concat('%',#{originMaterialDTO.materialName},'%')
        </if>
        <choose>
            <when test="originMaterialDTO.queryParam1!=null and originMaterialDTO.queryParam1!=''">
                and JSON_CONTAINS(`parent_id_list`, JSON_ARRAY(#{originMaterialDTO.parentId}))
                AND
                (om.material_name like concat('%',#{originMaterialDTO.queryParam1},'%')
                or om.create_by like concat('%',#{originMaterialDTO.queryParam1},'%')
                or om.create_short_id like concat('%',#{originMaterialDTO.queryParam1},'%')
                )
            </when>
            <otherwise>
                AND  om.parent_id = #{originMaterialDTO.parentId}
            </otherwise>
        </choose>
        <if test="authMaterialIdList!=null and authMaterialIdList.size()>0">
            AND (
            <foreach collection="authMaterialIdList" item="item" >
                JSON_CONTAINS(om.`parent_id_list`, JSON_ARRAY(#{item})) or
            </foreach>
             om.id in
            <foreach collection="authMaterialIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
    </sql>
    <select id="getList" resultMap="materialExtensionDTO">
        select
            <include refid="relationColumns"></include>
            <include refid="pageInfoSql"></include>
            <include refid="wherePageSql"></include>
        order by om.create_time desc
        limit #{offset},#{size}
    </select>
    <select id="getPageInfoCount" resultType="java.lang.Long">
        select count(*)
        <include refid="pageInfoSql"></include>
        <include refid="wherePageSql"></include>
    </select>
    <select id="getGlobalTagSearchPageInfo"
            resultType="cn.com.voyah.material.vo.user.OriginMaterialInfoV2VO">
        SELECT
        <if test="keyList != null  and keyList.size>0">
            a.remark_case,
            a.material_name_case ,
            a.tag_value_case,
        </if>
        a.id,
        a.material_name as materialName,
        a.url,
        a.th_url AS thUrl,
        a.file_type AS fileType,
        a.file_size as fileSize,
        a.create_time as createTime,
        a.user_view_count as userViewCount,
        a.tag_value as tagValue
        FROM
        (
        select * from (
        SELECT
        t.id,
        t.remark,
        t.material_name,

        <if test="keyList != null  and keyList.size>0">
            SUM(
            <foreach collection="keyList" index="index" item="item" open="(" separator=" " close=")">
                ( CASE WHEN LOCATE( #{item}, t.remark ) > 0 THEN 1 ELSE 0 END )
                <if test="index != (keyList.size() - 1)">
                    +
                </if>
            </foreach>
            ) AS remark_case,

            SUM(
            <foreach collection="keyList" index="index" item="item" open="(" separator=" " close=")">
                ( CASE WHEN LOCATE( #{item}, t.material_name ) > 0 THEN 1 ELSE 0 END )
                <if test="index != (keyList.size() - 1)">
                    +
                </if>
            </foreach>
            ) AS material_name_case,
            SUM(
            <foreach collection="keyList" index="index" item="item" open="(" separator=" " close=")">
                ( CASE WHEN LOCATE( #{item}, t.tag_value ) > 0 THEN 1 ELSE 0 END )
                <if test="index != (keyList.size() - 1)">
                    +
                </if>
            </foreach>
            ) AS tag_value_case,
        </if>
        t.url,
        t.th_url,
        t.file_type,
        t.file_size,
        t.create_time,
        t.user_view_count,
        t.tag_value
        FROM
        (
        SELECT
        om.id,
        main.remark,
        om.material_name,
        ( SELECT GROUP_CONCAT( mti.tag_value ) FROM material_tag_item mti WHERE mti.material_id = main.material_id
        and mti.is_delete =0 GROUP BY mti.material_id ) AS tag_value,
        fd.url,
        fd.th_url,
        om.file_type,
        om.file_size,
        om.create_time,
        main.user_view_count
        FROM
        material_tag main
        LEFT JOIN origin_material om ON om.id = main.material_id and om.is_delete = 0
        LEFT JOIN file_detail fd ON fd.id = om.file_id  and fd.is_delete = 0
        where
        om.tag_status = 1
        AND om.is_dir = 0
        AND om.is_public = 1
        AND om.is_show = 1
        and main.is_delete = 0
        <if test="materialIdList != null  and materialIdList.size>0">
            and main.material_id in
            <foreach collection="materialIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="matchSuffixesList != null and matchSuffixesList.size>0">
            and om.file_type in
            <foreach collection="matchSuffixesList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="matchFileTypeList != null and matchFileTypeList.size>0">
            and om.file_type in
            <foreach collection="matchFileTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="matchNotInSuffixesList != null and matchNotInSuffixesList.size>0">
            and om.file_type not in
            <foreach collection="matchNotInSuffixesList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) t group by t.id
        ) m
        <if test="keyList != null  and keyList.size>0">
            where
            !(m.remark_case=0 and m.material_name_case=0 and m.tag_value_case=0)
        </if>


        ) a
        ORDER BY
        <choose>
            <when test="query.orderBy != null and query.orderBy != '' and query.orderBy=='createTime'.toString()">
                <if test="keyList != null  and keyList.size>0">
                    tag_value_case DESC,
                    material_name_case DESC,
                    remark_case DESC,
                </if>
                create_time desc
            </when>
            <when test="query.orderBy != null and query.orderBy != '' and query.orderBy=='userViewCount'.toString()">
                <if test="keyList != null  and keyList.size>0">
                    tag_value_case DESC,
                    material_name_case DESC,
                    remark_case DESC,
                </if>
                user_view_count desc
            </when>
            <otherwise>
                <if test="keyList != null  and keyList.size>0">
                    tag_value_case DESC,
                    material_name_case DESC,
                    remark_case DESC,
                </if>
                create_time desc
            </otherwise>
        </choose>


        limit ${pageOffset}, ${pageSize}
    </select>

    <select id="getGlobalTagSearchPageInfo_COUNT"
            resultType="long">
        SELECT
        count(*)
        FROM
        (
        SELECT
        <if test="keyList != null  and keyList.size>0">
            a.remark_case,
            a.material_name_case ,
            a.tag_value_case,
        </if>
        a.id,
        a.material_name as materialName,
        a.url,
        a.th_url AS thUrl,
        a.file_type AS fileType,
        a.file_size as fileSize,
        a.create_time as createTime,
        a.user_view_count as userViewCount,
        a.tag_value as tagValue
        FROM
        (
        select * from (
        SELECT
        t.id,
        t.remark,
        t.material_name,

        <if test="keyList != null  and keyList.size>0">
            SUM(
            <foreach collection="keyList" index="index" item="item" open="(" separator=" " close=")">
                ( CASE WHEN LOCATE( #{item}, t.remark ) > 0 THEN 1 ELSE 0 END )
                <if test="index != (keyList.size() - 1)">
                    +
                </if>
            </foreach>
            ) AS remark_case,

            SUM(
            <foreach collection="keyList" index="index" item="item" open="(" separator=" " close=")">
                ( CASE WHEN LOCATE( #{item}, t.material_name ) > 0 THEN 1 ELSE 0 END )
                <if test="index != (keyList.size() - 1)">
                    +
                </if>
            </foreach>
            ) AS material_name_case,
            SUM(
            <foreach collection="keyList" index="index" item="item" open="(" separator=" " close=")">
                ( CASE WHEN LOCATE( #{item}, t.tag_value ) > 0 THEN 1 ELSE 0 END )
                <if test="index != (keyList.size() - 1)">
                    +
                </if>
            </foreach>
            ) AS tag_value_case,
        </if>
        t.url,
        t.th_url,
        t.file_type,
        t.file_size,
        t.create_time,
        t.user_view_count,
        t.tag_value
        FROM
        (
        SELECT
        om.id,
        main.remark,
        om.material_name,
        ( SELECT GROUP_CONCAT( mti.tag_value ) FROM material_tag_item mti WHERE mti.material_id = main.material_id
        and mti.is_delete =0 GROUP BY mti.material_id ) AS tag_value,
        fd.url,
        fd.th_url,
        om.file_type,
        om.file_size,
        om.create_time,
        main.user_view_count
        FROM
        material_tag main
        LEFT JOIN origin_material om ON om.id = main.material_id and om.is_delete = 0
        LEFT JOIN file_detail fd ON fd.id = om.file_id  and fd.is_delete = 0
        where
        om.tag_status = 1
        AND om.is_dir = 0
        AND om.is_public = 1
        AND om.is_show = 1
        and main.is_delete = 0
        <if test="materialIdList != null  and materialIdList.size>0">
            and main.material_id in
            <foreach collection="materialIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="matchSuffixesList != null and matchSuffixesList.size>0">
            and om.file_type in
            <foreach collection="matchSuffixesList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="matchFileTypeList != null and matchFileTypeList.size>0">
            and om.file_type in
            <foreach collection="matchFileTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="matchNotInSuffixesList != null and matchNotInSuffixesList.size>0">
            and om.file_type not in
            <foreach collection="matchNotInSuffixesList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) t group by t.id
        ) m
        <if test="keyList != null  and keyList.size>0">
            where
            !(m.remark_case=0 and m.material_name_case=0 and m.tag_value_case=0)
        </if>


        ) a
        ORDER BY
        <choose>
            <when test="query.orderBy != null and query.orderBy != '' and query.orderBy=='createTime'.toString()">
                <if test="keyList != null  and keyList.size>0">
                    tag_value_case DESC,
                    material_name_case DESC,
                    remark_case DESC,
                </if>
                create_time desc
            </when>
            <when test="query.orderBy != null and query.orderBy != '' and query.orderBy=='userViewCount'.toString()">
                <if test="keyList != null  and keyList.size>0">
                    tag_value_case DESC,
                    material_name_case DESC,
                    remark_case DESC,
                </if>
                user_view_count desc
            </when>
            <otherwise>
                <if test="keyList != null  and keyList.size>0">
                    tag_value_case DESC,
                    material_name_case DESC,
                    remark_case DESC,
                </if>
                create_time desc
            </otherwise>
        </choose>

        ) aa

    </select>
    <update id="updatePublicStatusBatchByParentIdList">
        update origin_material  set is_public=#{publicStatus}
        where  JSON_CONTAINS(`parent_id_list`, JSON_ARRAY(#{parentId}))
        and is_delete=0
    </update>
</mapper>