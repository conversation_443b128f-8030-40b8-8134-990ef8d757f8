<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.voyah.material.mapper.MaterialTagMapper">
    <update id="addViewCount">
        update material_tag  set user_view_count=user_view_count+1
        where  material_id=#{materialId}
          and is_delete=0
    </update>
    <update id="addFavoriteCount">
        update material_tag  set user_favorite_count=user_favorite_count+#{incrCount}
        where  material_id=#{materialId}
          and is_delete=0
    </update>

    <update id="addDownloadCount">
        update material_tag  set user_download_count=user_download_count+1
        where  material_id=#{materialId}
          and is_delete=0
    </update>
    <select id="existTagIdList" resultType="long">
        SELECT
            t1.id
        FROM
            tag_info t1
        LEFT JOIN (
                    SELECT DISTINCT ( mti.tag_id ) tag_id FROM material_tag_item mti
                    left join origin_material om on mti.material_id=om.id
                    where om.is_delete=0 and mti.is_delete=0 ) t2
        ON t1.id = t2.tag_id
        WHERE
            t2.tag_id IS  not null
          and t1.is_delete=0;
    </select>


</mapper>