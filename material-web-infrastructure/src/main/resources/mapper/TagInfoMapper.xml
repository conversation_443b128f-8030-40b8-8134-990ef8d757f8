<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.voyah.material.mapper.TagInfoMapper">

    <select id="selectAllParents" resultType="cn.com.voyah.material.domain.entity.TagInfoDO">
        WITH RECURSIVE parent_tag_info AS (
            SELECT * FROM tag_info WHERE id = #{id}
            UNION ALL
            SELECT c.* FROM tag_info c
                                INNER JOIN parent_tag_info pc ON c.id = pc.parent_id
        )
        SELECT * FROM parent_tag_info WHERE id != #{id}
    </select>
</mapper>