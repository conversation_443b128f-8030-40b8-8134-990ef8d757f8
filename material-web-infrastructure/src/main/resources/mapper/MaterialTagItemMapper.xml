<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.voyah.material.mapper.MaterialTagItemMapper">
    <select id="getMaterialIdListByTags" resultType="long">
        select distinct(t.material_id)
        from material_tag t
        <foreach collection="tags" item="item" index="index">
            left join
            material_tag_item t_${index} on
                (t_${index}.material_id=t.material_id
                     and t_${index}.name=#{item.tagName}
            and t_${index}.tag_value in (
            <foreach collection="item.tagValueList" item="each" separator=",">
                #{each}
            </foreach>
            ))
        </foreach>
        where
            1=1
        <foreach collection="tags" item="item" index="index">
          and  t_${index}.material_id is not null
          and  t_${index}.is_delete =0
        </foreach>
    </select>


</mapper>