package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.OperationLogGateway;
import cn.com.voyah.material.convertor.OperationLogConvert;
import cn.com.voyah.material.domain.entity.OperationLogDO;
import cn.com.voyah.material.pojo.OperationLogPO;
import cn.com.voyah.material.mapper.OperationLogMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 日志表 服务层实现。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Component
@AllArgsConstructor
public class OperationLogGatewayImpl extends GatewayImpl<OperationLogDO, OperationLogMapper, OperationLogPO, OperationLogConvert> implements OperationLogGateway {

}