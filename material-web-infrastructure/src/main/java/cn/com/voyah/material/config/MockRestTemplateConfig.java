package cn.com.voyah.material.config;

import com.iov.tencent.inc.access.config.properties.IncBaseConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;

@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(prefix = "rest", name = "mock", havingValue = "true")
public class MockRestTemplateConfig {

    @Bean(name = "IncRestTemplate")
    public MockRestTemplate restTemplate(IncBaseConfig incBaseConfig) {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(10000);
        requestFactory.setReadTimeout(20000);
        return new MockRestTemplate(requestFactory, incBaseConfig);
    }
}
