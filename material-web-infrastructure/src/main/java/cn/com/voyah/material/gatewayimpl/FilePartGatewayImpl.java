package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.FilePartGateway;
import cn.com.voyah.material.convertor.FilePartConvert;
import cn.com.voyah.material.domain.entity.FilePartDO;
import cn.com.voyah.material.pojo.FilePartPO;
import cn.com.voyah.material.mapper.FilePartMapper;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.com.voyah.domain.entity.def.FileDetailDef.FILE_DETAIL;
import static cn.com.voyah.domain.entity.def.FilePartDef.FILE_PART;

/**
 * 文件分片信息表，仅在手动分片上传时使用 服务层实现。
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Component
@AllArgsConstructor
public class FilePartGatewayImpl extends GatewayImpl<FilePartDO, FilePartMapper, FilePartPO, FilePartConvert> implements FilePartGateway {

    @Override
    public List<FilePartDO> getListByUploadId(String uploadId) {
        QueryWrapper query = QueryWrapper.create();
        query.select(FILE_PART.ALL_COLUMNS)
                .from(FILE_PART.getTableName())
                .where(FILE_PART.UPLOAD_ID.eq(uploadId));

        return mapper.selectListByQueryAs(query,FilePartDO.class);
    }
}