package cn.com.voyah.material.convertor;

import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.MaterialFavoritePO;
import cn.com.voyah.material.domain.entity.MaterialFavoriteDO;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 * 收藏表 对象转换。
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface MaterialFavoriteConvert extends IConvert<MaterialFavoriteDO, MaterialFavoritePO>{

}