package cn.com.voyah.material.convertor;

import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.NoticeMessagePO;
import cn.com.voyah.material.domain.entity.NoticeMessageDO;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 * 通知表 对象转换。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface NoticeMessageConvert extends IConvert<NoticeMessageDO, NoticeMessagePO>{

}