package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.convertor.RecommendSearchConvert;
import cn.com.voyah.material.domain.entity.RecommendSearchDO;
import cn.com.voyah.material.domain.gateway.RecommendSearchGateway;
import cn.com.voyah.material.mapper.RecommendSearchMapper;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.pojo.RecommendSearchPO;
import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.com.voyah.domain.entity.def.RecommendSearchDef.RECOMMEND_SEARCH;

/**
 * 金刚位表 服务层实现。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Component
@AllArgsConstructor
public class RecommendSearchGatewayImpl extends GatewayImpl<RecommendSearchDO, RecommendSearchMapper, RecommendSearchPO, RecommendSearchConvert> implements RecommendSearchGateway {

    @Override
    public RecommendSearchPO getNearerDOBySort(Long sort, boolean isAsc) {
        QueryWrapper query = QueryWrapper.create();
        query.select(RECOMMEND_SEARCH.ALL_COLUMNS);
        query.from(RECOMMEND_SEARCH)
                .where(RECOMMEND_SEARCH.IS_DELETE.eq(false))
                .orderBy(RECOMMEND_SEARCH.SORT,isAsc).limit(1);
        if(isAsc){
            query.and(RECOMMEND_SEARCH.SORT.gt(sort));
        }else{
            query.and(RECOMMEND_SEARCH.SORT.lt(sort));
        }
        List<RecommendSearchPO> list=mapper.selectListByQuery(query);
        if(CollectionUtil.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @Override
    public boolean updateSort(Long id, Long sort) {
        RecommendSearchPO update = new RecommendSearchPO();
        update.setId(id);
        update.setSort(sort);
        return mapper.update(update)>0;
    }
}