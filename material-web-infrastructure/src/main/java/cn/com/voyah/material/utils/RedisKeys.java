package cn.com.voyah.material.utils;
import cn.com.voyah.material.constants.CommonConstants;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


@Component
public class RedisKeys {

    @Getter
    @Value("${project:material-server}")
    private String project;

    @Getter
    @Value("${spring.profiles.active:local}")
    private String env;

    @Getter
    @Value("${redis.key.default-expire:259200}")
    private Long defaultExpire;


    /**
     * cos临时token
     */
    @Getter
    private String cosCredential;


    @Getter
    private String fileNameKey;
    @Getter
    private String authMaterialIdListKey;
    @PostConstruct
    private void init(){
        cosCredential = getKey("cosCredential:");
        fileNameKey = getKey("generate:fileName:%s:%s");
        authMaterialIdListKey = getKey("auth:authMaterialIdListKey:%s");
    }

    public String getKey(String suffix) {
        return project + CommonConstants.REDIS_SEPERATOR + env + CommonConstants.REDIS_SEPERATOR + suffix;
    }




}
