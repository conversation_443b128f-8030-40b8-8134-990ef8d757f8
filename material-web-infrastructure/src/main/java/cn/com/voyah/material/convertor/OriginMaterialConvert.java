package cn.com.voyah.material.convertor;

import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.OriginMaterialPO;
import cn.com.voyah.material.domain.entity.OriginMaterialDO;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 * 素材 对象转换。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface OriginMaterialConvert extends IConvert<OriginMaterialDO, OriginMaterialPO>{

}