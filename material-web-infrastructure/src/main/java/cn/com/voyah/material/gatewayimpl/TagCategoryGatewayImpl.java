package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.TagCategoryGateway;
import cn.com.voyah.material.convertor.TagCategoryConvert;
import cn.com.voyah.material.domain.entity.TagCategoryDO;
import cn.com.voyah.material.pojo.TagCategoryPO;
import cn.com.voyah.material.mapper.TagCategoryMapper;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

import static cn.com.voyah.domain.entity.def.TagCategoryDef.TAG_CATEGORY;

/**
 *  服务层实现。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Component
@AllArgsConstructor
public class TagCategoryGatewayImpl extends GatewayImpl<TagCategoryDO, TagCategoryMapper, TagCategoryPO, TagCategoryConvert> implements TagCategoryGateway {

    @Override
    public List<TagCategoryDO> getList(TagCategoryDO tagCategoryDO) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_CATEGORY.ALL_COLUMNS)
                .from(TAG_CATEGORY.getTableName())
                .where(TAG_CATEGORY.IS_DELETE.eq(false))
                .orderBy(TAG_CATEGORY.SORT.getName(),true);
        if(ObjectUtil.isNotEmpty(tagCategoryDO)&&!StringUtils.isEmpty(tagCategoryDO.getName())){
            query.and(TAG_CATEGORY.NAME.eq(tagCategoryDO.getName()));
        }
        if(ObjectUtil.isNotEmpty(tagCategoryDO)&&!ObjectUtil.isEmpty(tagCategoryDO.getStatus())){
            query.and(TAG_CATEGORY.STATUS.eq(tagCategoryDO.getStatus()));
        }
        return mapper.selectListByQueryAs(query, TagCategoryDO.class);
    }
}