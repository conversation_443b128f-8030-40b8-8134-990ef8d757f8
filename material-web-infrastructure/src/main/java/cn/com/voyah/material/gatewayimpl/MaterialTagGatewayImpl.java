package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.MaterialTagGateway;
import cn.com.voyah.material.convertor.MaterialTagConvert;
import cn.com.voyah.material.domain.entity.MaterialTagDO;
import cn.com.voyah.material.pojo.MaterialTagPO;
import cn.com.voyah.material.mapper.MaterialTagMapper;
import cn.com.voyah.material.pojo.OriginMaterialPO;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.com.voyah.domain.entity.def.MaterialTagDef.MATERIAL_TAG;

/**
 *  服务层实现。
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Component
@AllArgsConstructor
public class MaterialTagGatewayImpl extends GatewayImpl<MaterialTagDO, MaterialTagMapper, MaterialTagPO, MaterialTagConvert> implements MaterialTagGateway {

    @Override
    public MaterialTagDO getByMaterialId(Long materialId) {
        QueryWrapper query = QueryWrapper.create();
        query.select(MATERIAL_TAG.ALL_COLUMNS)
                .from(MATERIAL_TAG.getTableName())
                .where(MATERIAL_TAG.MATERIAL_ID.eq(materialId));
        MaterialTagPO materialTagPO= mapper.selectOneByQuery(query);
        return MaterialTagDO.buildFrom(materialTagPO);
    }


    @Override
    public Boolean updateTagRemark(Long id, String remark) {
        MaterialTagPO update = new MaterialTagPO();
        update.setId(id);
        update.setRemark(remark);
        return mapper.update(update)>0;
    }

    @Override
    public boolean addViewCount(Long materialId) {
        if (ObjectUtil.isEmpty(materialId) || materialId == 0) {
            return false;
        }
        return mapper.addViewCount(materialId)>0;
    }

    @Override
    public boolean addFavoriteCount(Long materialId,boolean isIncr) {
        if (ObjectUtil.isEmpty(materialId) || materialId == 0) {
            return false;
        }
        return mapper.addFavoriteCount(materialId,isIncr?1:-1)>0;
    }

    @Override
    public boolean addDownloadCount(Long materialId) {
        if (ObjectUtil.isEmpty(materialId) || materialId == 0) {
            return false;
        }
        return mapper.addDownloadCount(materialId)>0;
    }

    @Override
    public List<Long> existTagIdList() {

        return mapper.existTagIdList();
    }


}