package cn.com.voyah.material.listener;

import cn.com.voyah.material.domain.entity.OperationLogDO;
import cn.com.voyah.material.domain.gateway.OperationLogGateway;
import cn.com.voyah.material.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

@Component
public class RedisMessageListener implements MessageListener {



    @Autowired
    private OperationLogGateway operationLogGateway;

    @Value("${operation.log.ignore:false}")
    private  boolean operationLogIgnore;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        // 处理接收到的消息
        String channel = new String(message.getChannel());
        String body = new String(message.getBody());
        OperationLogDO operationLogDO=JsonUtil.readValue(body, OperationLogDO.class);
        if(!operationLogIgnore){
            operationLogGateway.save(operationLogDO);
        }
    }
}
