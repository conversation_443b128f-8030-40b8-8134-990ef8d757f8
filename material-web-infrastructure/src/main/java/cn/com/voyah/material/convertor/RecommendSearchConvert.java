package cn.com.voyah.material.convertor;

import cn.com.voyah.material.domain.entity.RecommendSearchDO;
import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.RecommendSearchPO;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 * 金刚位表 对象转换。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface RecommendSearchConvert extends IConvert<RecommendSearchDO, RecommendSearchPO>{

}