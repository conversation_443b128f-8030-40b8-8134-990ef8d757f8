package cn.com.voyah.material.convertor;

import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.FilePartPO;
import cn.com.voyah.material.domain.entity.FilePartDO;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 * 文件分片信息表，仅在手动分片上传时使用 对象转换。
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface FilePartConvert extends IConvert<FilePartDO, FilePartPO>{

}