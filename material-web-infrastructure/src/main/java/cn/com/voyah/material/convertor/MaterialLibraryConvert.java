package cn.com.voyah.material.convertor;

import cn.com.voyah.material.domain.entity.MaterialLibraryDO;
import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.MaterialLibraryPO;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 * 素材库（类型） 对象转换。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface MaterialLibraryConvert extends IConvert<MaterialLibraryDO, MaterialLibraryPO>{


}