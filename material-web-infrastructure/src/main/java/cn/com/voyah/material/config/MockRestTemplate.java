package cn.com.voyah.material.config;

import com.iov.tencent.inc.access.config.properties.IncBaseConfig;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MockRestTemplate extends RestTemplate {
    private final static Map<String, String> MOCK_CONFIG = new HashMap<>();
    private final static List<String> permIdList = List.of(
            "/dictionary/homeFolder:activate",
            "/dictionary/homeFolder:delete",
            "/dictionary/homeFolder:edit",
            "/dictionary/homeFolder:add",
            "/dictionary/homeFolder",
            "/rawMaterials/file:addFolder",
            "/rawMaterials/file:view",
            "/rawMaterials/file:edit",
            "/rawMaterials/file:delete",
            "/rawMaterials/file:add",
            "/rawMaterials/file",
            "/knowledge",
            "/rawMaterials/search",
            "/annotation/treat",
            "/annotation/treat:add",
            "/annotation/stop",
            "/annotation/stop:edit",
            "/annotation/stop:view",
            "/annotation/details",
            "/annotation/treatDetails",
            "/annotation/stopDetails",
            "/dictionary/labelDetails",
            "/dictionary/labelDictionary",
            "/dictionary/labelDictionary:add",
            "/dictionary/labelDictionary:delete",
            "/dictionary/labelDictionary:edit",
            "/dictionary/labelDictionary:view",
            "/dictionary/labelDictionary:activate",
            "/systemManagement/permission",
            "/systemManagement/permission:add",
            "/systemManagement/permission:edit",
            "/systemManagement/permission:view",
            "/systemManagement/permission/details",
            "/spotCheck/task",
            "/spotCheck/task:add",
            "/spotCheck/task:delete",
            "/spotCheck/task:edit",
            "/spotCheck/task:view",
            "/advertisement/banner",
            "/advertisement/banner:add",
            "/advertisement/banner:delete",
            "/advertisement/banner:edit",
            "/advertisement/banner:view",
            "/advertisement/banner:activate",
            "/advertisement/vajra",
            "/advertisement/vajra:add",
            "/advertisement/vajra:delete",
            "/advertisement/vajra:edit",
            "/advertisement/vajra:view",
            "/advertisement/vajra:activate",
            "/systemManagement/operationLog",
            "/systemManagement/message",
            "/spotCheck/taskDetails",
            "/advertisement/bannerDetails",
            "/spotCheck/vajraDetails",
            "/message/details",
            "/advertisement/searchHotwords",
            "/advertisement/searchHotwords:add",
            "/advertisement/searchHotwords:delete",
            "/advertisement/searchHotwords:edit",
            "/advertisement/searchHotwords:view",
            "/advertisement/searchHotwords:activate"

    );
    static {
        MOCK_CONFIG.put("islogin", "{\"code\":0,\"msg\":\"SUCCESS\",\"data\":true}");
        MOCK_CONFIG.put("parseTk", "{\"code\":0,\"msg\":\"SUCCESS\",\"data\":{\"token\":\"8cb5e646-e8c2-4490-bca4-35f97413b936\"}}");
        MOCK_CONFIG.put("checkApiByToken", "{\"code\":0,\"msg\":\"SUCCESS\",\"data\":true}");
        MOCK_CONFIG.put("getUser", "{\"code\":0,\"msg\":\"SUCCESS\",\"data\":{\"user\":{\"token\":\"8cb5e646-e8c2-4490-bca4-35f97413b936\",\"userName\":\"联想-杜珊珊\",\"isInitial\":\"N\",\"roles\":[\"6732c8811d0d48001a71cd16\"],\"mobile\":\"15701548482\",\"status\":\"ENABLE\",\"gender\":\"F\",\"manageOrgs\":[],\"mainTenant\":\"5f978d56a68979001ae37473\",\"tenant\":\"5f978d56a68979001ae37473\",\"org\":[\"61bd86ec8bc1c1001a87e8a6\"],\"extra\":{\"ToUserName\":\"ww813ccd027c47c6d5\",\"FromUserName\":\"sys\",\"CreateTime\":1731380957965,\"MsgType\":\"event\",\"Event\":\"change_contact\",\"ChangeType\":\"create_user\",\"UserID\":\"1029715\",\"Name\":\"联想-杜珊珊\",\"Mobile\":15701548482,\"Gender\":2,\"Email\":\"\",\"Avatar\":\"\",\"Department\":\"489\",\"Code\":\"SUP1029715\",\"Status\":1},\"loginTime\":\"2024-11-20T08:06:25.302Z\",\"isThirdPartyImport\":true,\"thirdPartyType\":\"thirdparty\",\"activatedState\":[],\"_id\":\"6732c6de1d0d48001a71a28b\",\"name\":\"1029715\",\"jobNum\":\"SUP1029715\",\"id\":\"144115205301762625\",\"bindUsers\":[],\"created_at\":\"2024-11-12T03:09:18.202Z\",\"tags\":[{\"roles\":[],\"_id\":\"622aaf5694dd72001ae1f106\",\"tagId\":\"68\",\"tagName\":\"普通员工\"}],\"tenantList\":[{\"tenantId\":\"5f978d56a68979001ae37473\",\"orgs\":[{\"status\":\"1\",\"rootId\":\"5f978d56a68979001ae37473\",\"orgType\":\"1\",\"fullPath\":[\"5f978d56a68979001ae37473\",\"60f94773e4aff102be47b625\",\"61b6e85592894f001aefb447\"],\"thirdPartyAppOrgId\":\"489\",\"_id\":\"61bd86ec8bc1c1001a87e8a6\",\"name\":\"供应商1\",\"code\":\"489\"}],\"isAdmin\":false,\"fullOrgs\":[[\"5f978d56a68979001ae37473\",\"60f94773e4aff102be47b625\",\"61b6e85592894f001aefb447\",\"61bd86ec8bc1c1001a87e8a6\"]],\"manageOrgs\":[],\"roles\":[{\"rolePerm\":[\"673c0ace1d0d48001ad7fe81\",\"673c0aa61d0d48001ad7fcad\",\"673c0a951d0d48001ad7fbd9\",\"673c0a841d0d48001ad7fa36\",\"673c0a4a1d0d48001ad7f6e8\",\"673c0a371d0d48001ad7f608\",\"673c0a231d0d48001ad7f54a\",\"673c0a111d0d48001ad7f41c\",\"673c0a031d0d48001ad7f382\",\"673c09f21d0d48001ad7f29a\",\"673c09da1d0d48001ad7f1b7\",\"673c09911d0d48001ad7ecec\",\"6732c5c21d0d48001a7183db\"],\"status\":\"1\",\"apps\":[\"6732c5c21d0d48001a7183d2\"],\"org\":\"5f978d56a68979001ae37473\",\"type\":\"3\",\"_id\":\"6732c8811d0d48001a71cd16\",\"roleName\":\"亮点知识库管理员\"}]}],\"orgCode\":[\"489\"],\"orgId\":[\"489\"],\"orgName\":[\"供应商1\"],\"orgFullPath\":[[\"5f978d56a68979001ae37473\",\"60f94773e4aff102be47b625\",\"61b6e85592894f001aefb447\",\"61bd86ec8bc1c1001a87e8a6\"]],\"roleType\":\"3\",\"roleNames\":[\"亮点知识库管理员\"],\"permApiList\":[],\"permIdList\":[\""+
                String.join( "\",\"",permIdList)+
                "\"],\"permList\":[{\"apis\":[],\"_id\":\"673c0ace1d0d48001ad7fe81\",\"permId\":\"/dictionary/homeFolder:activate\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c0aa61d0d48001ad7fcad\",\"permId\":\"/dictionary/homeFolder:delete\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c0a951d0d48001ad7fbd9\",\"permId\":\"/dictionary/homeFolder:edit\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c0a841d0d48001ad7fa36\",\"permId\":\"/dictionary/homeFolder:add\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c0a4a1d0d48001ad7f6e8\",\"permId\":\"/dictionary/homeFolder\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c0a371d0d48001ad7f608\",\"permId\":\"/rawMaterials/file:addFolder\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c0a231d0d48001ad7f54a\",\"permId\":\"/rawMaterials/file:view\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c0a111d0d48001ad7f41c\",\"permId\":\"/rawMaterials/file:edit\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c0a031d0d48001ad7f382\",\"permId\":\"/rawMaterials/file:delete\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c09f21d0d48001ad7f29a\",\"permId\":\"/rawMaterials/file:add\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c09da1d0d48001ad7f1b7\",\"permId\":\"/rawMaterials/file\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"673c09911d0d48001ad7ecec\",\"permId\":\"/knowledge\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}},{\"apis\":[],\"_id\":\"6732c5c21d0d48001a7183db\",\"appId\":{\"_id\":\"6732c5c21d0d48001a7183d2\",\"name\":\"亮点知识库管理平台\",\"appId\":\"70910786\"}}],\"iamAppId\":\"7111603767638533\"}}}");
        MOCK_CONFIG.put("logout", "{\"code\":0,\"msg\":\"SUCCESS\",\"data\":\"https://oauth.idsmp.voyah.com.cn/oauth/authorize?state=MTU5OTM2NzQ5NDg3Nw%3D%3D&nonce=OTMzMTI1MDEwNjk0&scope=verify_phone%20verify_email%20send_email%20send_sms%20set_email%20user_import%20reset_password%20get_user_info%20get_email%20set_profile%20get_profile%20set_phone%20get_phone%20set_username%20get_username%20logout%20manage_tenant%20delete_user%20%2F.*&response_type=code&appid=1&client_id=2575043885&redirect_uri=https%3A%2F%2Fcenter.idsmp.voyah.com.cn%2Fsso.html%3Fr_uri%3Dhttps%253A%252F%252Fvoyahub-test.voyah.cn&cfg_host=https%3A%2F%2Fcenter.idsmp.voyah.com.cn&base=https%3A%2F%2Fstatic-1255598736.cos.ap-guangzhou.myqcloud.com%2Finc-center-pt%2Fspring%2F9fcdef9a2f407d070bc4f2e9d4c1436c%2F\"}");
        MOCK_CONFIG.put("getSSO", "{\"code\":0,\"msg\":\"SUCCESS\"," +
                "\"data\":\"incServer/?tk=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9" +
                ".eyJ0b2tlbiI6IjhjYjVlNjQ2LWU4YzItNDQ5MC1iY2E0LTM1Zjk3NDEzYjkzNiIsImlhdCI6MTczMjA5MzAyMCwiZXhwIjozNDY0MTg2MTAwfQ._jYow4w7V0KfVgIpcvr9PNkZyHsGSp6J_nz4bWM9u7Y\"}");
    }

    private final IncBaseConfig incBaseConfig;

    public MockRestTemplate(ClientHttpRequestFactory requestFactory, IncBaseConfig incBaseConfig) {
        super(requestFactory);
        this.incBaseConfig = incBaseConfig;
    }


    @Override
    protected <T> T doExecute(URI url, HttpMethod method, RequestCallback requestCallback, ResponseExtractor<T> responseExtractor) throws RestClientException {
        int i = url.getPath().lastIndexOf("/");
        String endUrl = url.getPath().substring(i + 1);
        if (MOCK_CONFIG.containsKey(endUrl)) {
            String body = MOCK_CONFIG.get(endUrl);
            if(endUrl.equals("getSSO")){
                body = body.replace("incServer", incBaseConfig.getIncServer());
            }
            final String bodyStr = body;
            ClientHttpResponse response = new ClientHttpResponse() {
                private InputStream inputStream;

                @Override
                public HttpStatus getStatusCode() {
                    return HttpStatus.OK;
                }

                @Override
                public int getRawStatusCode() {
                    return HttpStatus.OK.value();
                }

                @Override
                public String getStatusText() {
                    return HttpStatus.OK.getReasonPhrase();
                }

                @Override
                public void close() {
                    if (inputStream != null) {
                        try {
                            inputStream.close();
                        } catch (IOException e) {
                            logger.error(e.getMessage(), e);
                        }
                    }
                }

                @Override
                public InputStream getBody() {
                    inputStream = new ByteArrayInputStream(bodyStr.getBytes(StandardCharsets.UTF_8));
                    return inputStream;
                }

                @Override
                public HttpHeaders getHeaders() {
                    HttpHeaders headers = new HttpHeaders();
                    headers.add("Content-Type", "application/json; charset=utf-8");
                    return headers;
                }
            };
            try {
                return responseExtractor.extractData(response);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return super.doExecute(url, method, requestCallback, responseExtractor);
    }



}
