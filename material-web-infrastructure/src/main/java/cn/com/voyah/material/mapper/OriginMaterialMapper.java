package cn.com.voyah.material.mapper;

import cn.com.voyah.material.dto.GlobalOriginMaterialQuery;
import cn.com.voyah.material.dto.OriginMaterialExtensionDTO;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.pojo.OriginMaterialPO;
import cn.com.voyah.material.vo.user.OriginMaterialInfoV2VO;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 素材 映射层。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
public interface OriginMaterialMapper extends BaseMapper<OriginMaterialPO> {
    Long getPageInfoCount(@Param("originMaterialDTO") OriginMaterialExtensionDTO originMaterialDTO, @Param("authMaterialIdList") List<Long> authMaterialIdList);

    List<OriginMaterialExtensionDTO> getList(@Param("offset") Long offset, @Param("size") Long size, @Param("originMaterialDTO") OriginMaterialExtensionDTO originMaterialDTO, @Param("authMaterialIdList") List<Long> authMaterialIdList);

    int updatePublicStatusBatchByParentIdList(@Param("parentId") Long parentId, @Param("publicStatus") Integer publicStatus);

    Page<OriginMaterialInfoV2VO> getGlobalTagSearchPageInfo(@Param("query") PageQueryCondition<GlobalOriginMaterialQuery> pageQuery
            , @Param("keyList") List<String> keyList
            , @Param("materialIdList") List<Long> materialIdList
            , @Param("matchSuffixesList") List<String> matchSuffixesList
            , @Param("matchNotInSuffixesList") List<String> matchNotInSuffixesList
            , @Param("matchFileTypeList") List<String> matchFileTypeList

    );
}
