package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.constants.TaskStatus;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.TaskInfoGateway;
import cn.com.voyah.material.convertor.TaskInfoConvert;
import cn.com.voyah.material.domain.entity.TaskInfoDO;
import cn.com.voyah.material.pojo.TaskInfoPO;
import cn.com.voyah.material.mapper.TaskInfoMapper;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

import static cn.com.voyah.domain.entity.def.TaskInfoDef.TASK_INFO;

/**
 * 任务基本信息表 服务层实现。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Component
@AllArgsConstructor
public class TaskInfoGatewayImpl extends GatewayImpl<TaskInfoDO, TaskInfoMapper, TaskInfoPO, TaskInfoConvert> implements TaskInfoGateway {

    @Override
    public List<TaskInfoPO> getList(String shortId, Long foldId, LocalDateTime endTime) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(TASK_INFO.getTableName())
                .where(TASK_INFO.EXECUTE_SHORT_ID.eq(shortId))
                .and(TASK_INFO.MATERIAL_FOLD_ID.eq(foldId))
                .and(TASK_INFO.IS_DELETE.eq(false))
                .and(TASK_INFO.TASK_END_TIME.gt(endTime));
        return mapper.selectListByQuery(queryWrapper);
    }
    @Override
    public List<TaskInfoPO> getProcessList(String shortId, Long foldId, LocalDateTime endTime) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(TASK_INFO.getTableName())
                .where(TASK_INFO.EXECUTE_SHORT_ID.eq(shortId))
                .and(TASK_INFO.MATERIAL_FOLD_ID.eq(foldId))
                .and(TASK_INFO.IS_DELETE.eq(false))
                .and(TASK_INFO.TASK_STATUS.eq(TaskStatus.PROGRESS.getValue()))
                .and(TASK_INFO.TASK_END_TIME.gt(endTime));
        return mapper.selectListByQuery(queryWrapper);
    }



    @Override
    public boolean updateFileCount(Long id, int actualFileCount) {
        TaskInfoPO update = new TaskInfoPO();
        update.setId(id);
        update.setActualFileCount(actualFileCount);

        return mapper.update(update)>0;
    }

    @Override
    public boolean updateTaskStatus(Long id, TaskStatus taskStatus,LocalDateTime updateTime) {
        TaskInfoPO update = new TaskInfoPO();
        update.setId(id);
        update.setTaskStatus(taskStatus.getValue());
        if(TaskStatus.COMPLETE.equals(taskStatus)||TaskStatus.CANCEL.equals(taskStatus)){
            update.setTaskFinishTime(updateTime);
        }
        return mapper.update(update)>0;
    }

    @Override
    public boolean updateStatusBatch(List<Long> ids, TaskStatus status) {
        TaskInfoPO taskInfoPO=new TaskInfoPO();
        taskInfoPO.setTaskStatus(status.getValue());
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(TASK_INFO.getTableName())
                .where(TASK_INFO.ID.in(ids))
                .and(TASK_INFO.IS_DELETE.eq(false));
        return mapper.updateByQuery(taskInfoPO,queryWrapper)>0;
    }
}