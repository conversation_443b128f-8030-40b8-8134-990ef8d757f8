package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.domain.entity.MaterialTagDO;
import cn.com.voyah.material.domain.entity.TagInfoDO;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.MaterialTagItemGateway;
import cn.com.voyah.material.convertor.MaterialTagItemConvert;
import cn.com.voyah.material.domain.entity.MaterialTagItemDO;
import cn.com.voyah.material.pojo.MaterialTagItemPO;
import cn.com.voyah.material.mapper.MaterialTagItemMapper;
import cn.com.voyah.material.pojo.MaterialTagPO;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static cn.com.voyah.domain.entity.def.BannerInfoDef.BANNER_INFO;
import static cn.com.voyah.domain.entity.def.FileDetailDef.FILE_DETAIL;
import static cn.com.voyah.domain.entity.def.MaterialTagDef.MATERIAL_TAG;
import static cn.com.voyah.domain.entity.def.MaterialTagItemDef.MATERIAL_TAG_ITEM;
import static cn.com.voyah.domain.entity.def.TagInfoDef.TAG_INFO;

/**
 *  服务层实现。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Component
@AllArgsConstructor
public class MaterialTagItemGatewayImpl extends GatewayImpl<MaterialTagItemDO, MaterialTagItemMapper, MaterialTagItemPO, MaterialTagItemConvert> implements MaterialTagItemGateway {

    @Override
    public List<MaterialTagItemDO> getBaseList(Long materialId, Long categoryId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(MATERIAL_TAG_ITEM.getTableName())
                .where(MATERIAL_TAG_ITEM.MATERIAL_ID.eq(materialId))
                .and(MATERIAL_TAG_ITEM.IS_DELETE.eq(false));
        if(ObjectUtil.isNotEmpty(categoryId)){
            queryWrapper.and(MATERIAL_TAG_ITEM.CATEGORY_ID.eq(categoryId));
        }
        List<MaterialTagItemPO> list=mapper.selectListByQuery(queryWrapper);
        return MaterialTagItemDO.buildFrom(list);
    }

    @Override
    public List<MaterialTagItemDO> getList(Long materialId, Long categoryId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select(MATERIAL_TAG_ITEM.ID,
                MATERIAL_TAG_ITEM.TAG_ID,
                MATERIAL_TAG_ITEM.MATERIAL_ID,
                MATERIAL_TAG_ITEM.MATERIAL_TAG_ID,
                MATERIAL_TAG_ITEM.CATEGORY_ID,
                MATERIAL_TAG_ITEM.LEVEL,
                MATERIAL_TAG_ITEM.PARENT_ID,
                TAG_INFO.CODE,
                TAG_INFO.NAME,
                TAG_INFO.TAG_VALUE);
        queryWrapper.from(MATERIAL_TAG_ITEM.getTableName())
                .leftJoin(TAG_INFO.getTableName())
                .on(MATERIAL_TAG_ITEM.TAG_ID.eq(TAG_INFO.ID))
                .where(MATERIAL_TAG_ITEM.MATERIAL_ID.eq(materialId))
                .and(MATERIAL_TAG_ITEM.IS_DELETE.eq(false))
                .and(TAG_INFO.IS_DELETE.eq(false));
        if(ObjectUtil.isNotEmpty(categoryId)){
            queryWrapper.and(MATERIAL_TAG_ITEM.CATEGORY_ID.eq(categoryId));
        }
        List<MaterialTagItemPO> list=mapper.selectListByQuery(queryWrapper);
        return MaterialTagItemDO.buildFrom(list);

    }

    @Override
    public boolean delete(Long materialId, Long categoryId) {
        MaterialTagItemPO materialTagItemPO=new MaterialTagItemPO();
        materialTagItemPO.setIsDelete(true);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(MATERIAL_TAG_ITEM.getTableName())
                .where(MATERIAL_TAG_ITEM.CATEGORY_ID.eq(categoryId))
                .and(MATERIAL_TAG_ITEM.MATERIAL_ID.eq(materialId))
                .and(MATERIAL_TAG_ITEM.IS_DELETE.eq(false));
        return mapper.updateByQuery(materialTagItemPO,queryWrapper)>0;
    }

    @Override
    public boolean deleteByTagIds(Long categoryId, Long materialId, List<Long> tagIds) {
        MaterialTagItemPO materialTagItemPO=new MaterialTagItemPO();
        materialTagItemPO.setIsDelete(true);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(MATERIAL_TAG_ITEM.getTableName())
                .where(MATERIAL_TAG_ITEM.MATERIAL_ID.eq(materialId))
                .and(MATERIAL_TAG_ITEM.TAG_ID.in(tagIds))
                .and(MATERIAL_TAG_ITEM.IS_DELETE.eq(false));
        if(ObjectUtil.isNotEmpty(categoryId)){
            queryWrapper.and(MATERIAL_TAG_ITEM.CATEGORY_ID.eq(categoryId));
        }
        return mapper.updateByQuery(materialTagItemPO,queryWrapper)>0;
    }

    @Override
    public List<Long> getCategoryIdList(Long materialId) {
        QueryWrapper query = QueryWrapper.create();
        query.select(MATERIAL_TAG_ITEM.CATEGORY_ID)
                .from(MATERIAL_TAG_ITEM.getTableName())
                .where(MATERIAL_TAG_ITEM.MATERIAL_ID.eq(materialId))
                .and(MATERIAL_TAG_ITEM.IS_DELETE.eq(false))
                .groupBy(MATERIAL_TAG_ITEM.CATEGORY_ID);
        List<MaterialTagItemPO> list= mapper.selectListByQuery(query);
        if(CollectionUtil.isNotEmpty(list)){
            return list.stream().map(each->each.getCategoryId()).collect(Collectors.toList());
        }
        return List.of();
    }


    @Override
    public List<TagInfoMergeVO> getTagMergeVoList(Long materialId, Long categoryId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select(MATERIAL_TAG_ITEM.ID,
                MATERIAL_TAG_ITEM.TAG_ID,
                MATERIAL_TAG_ITEM.MATERIAL_ID,
                MATERIAL_TAG_ITEM.MATERIAL_TAG_ID,
                MATERIAL_TAG_ITEM.CATEGORY_ID,
                MATERIAL_TAG_ITEM.LEVEL,
                MATERIAL_TAG_ITEM.PARENT_ID,
                TAG_INFO.CODE,
                TAG_INFO.NAME,
                TAG_INFO.TAG_VALUE);
        queryWrapper.from(MATERIAL_TAG_ITEM.getTableName())
                .leftJoin(TAG_INFO.getTableName())
                .on(MATERIAL_TAG_ITEM.TAG_ID.eq(TAG_INFO.ID))
                .where(MATERIAL_TAG_ITEM.MATERIAL_ID.eq(materialId))
                .and(MATERIAL_TAG_ITEM.IS_DELETE.eq(false))
                .and(TAG_INFO.IS_DELETE.eq(false));
        if(ObjectUtil.isNotEmpty(categoryId)){
            queryWrapper.and(MATERIAL_TAG_ITEM.CATEGORY_ID.eq(categoryId));
        }
        List<MaterialTagItemPO> list=mapper.selectListByQuery(queryWrapper);
        Map<String, Set<String>> nameValueSetmap=new HashMap<>();
        List<String> nameList=new ArrayList<String>();
        for (MaterialTagItemPO each:list) {
            if(nameList.contains(each.getName())){
                nameValueSetmap.get(each.getName()).add(each.getTagValue());
            }else{
                nameList.add(each.getName());
                HashSet set=new HashSet();
                set.add(each.getTagValue());
                nameValueSetmap.put(each.getName(),set);
            }
        }
        List<TagInfoMergeVO> mergeList=new ArrayList<>();
        for (String name:nameList) {
            TagInfoMergeVO tagInfoMergeVO=new TagInfoMergeVO();
            tagInfoMergeVO.setTagName(name);
            tagInfoMergeVO.setTagValueList(nameValueSetmap.get(name));
            mergeList.add(tagInfoMergeVO);
        }
        return mergeList;
    }

    @Override
    public List<Long> getMaterialIdListByTags(List<TagInfoMergeVO> tags) {

        return mapper.getMaterialIdListByTags(tags);
    }
}