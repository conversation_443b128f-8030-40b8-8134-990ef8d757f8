package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.BannerInfoGateway;
import cn.com.voyah.material.convertor.BannerInfoConvert;
import cn.com.voyah.material.domain.entity.BannerInfoDO;
import cn.com.voyah.material.pojo.BannerInfoPO;
import cn.com.voyah.material.mapper.BannerInfoMapper;
import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.List;
import static cn.com.voyah.domain.entity.def.BannerInfoDef.BANNER_INFO;

/**
 * banner表 服务层实现。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Component
@AllArgsConstructor
public class BannerInfoGatewayImpl extends GatewayImpl<BannerInfoDO, BannerInfoMapper, BannerInfoPO, BannerInfoConvert> implements BannerInfoGateway {

    @Override
    public BannerInfoPO getNearerDOBySort(Long sort, boolean isAsc) {
        QueryWrapper query = QueryWrapper.create();
        query.select(BANNER_INFO.ALL_COLUMNS);
        query.from(BANNER_INFO)
                .where(BANNER_INFO.IS_DELETE.eq(false))
                .orderBy(BANNER_INFO.SORT,isAsc).limit(1);
        if(isAsc){
            query.and(BANNER_INFO.SORT.gt(sort));
        }else{
            query.and(BANNER_INFO.SORT.lt(sort));
        }
        List<BannerInfoPO> list=mapper.selectListByQuery(query);
        if(CollectionUtil.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @Override
    public boolean updateSort(Long id, Long sort) {
        BannerInfoPO update = new BannerInfoPO();
        update.setId(id);
        update.setSort(sort);
        return mapper.update(update)>0;
    }
}