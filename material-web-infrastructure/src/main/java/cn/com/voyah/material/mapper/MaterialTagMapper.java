package cn.com.voyah.material.mapper;

import com.mybatisflex.core.BaseMapper;
import cn.com.voyah.material.pojo.MaterialTagPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  映射层。
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface MaterialTagMapper extends BaseMapper<MaterialTagPO> {

    int addViewCount(@Param("materialId") Long materialId);
    int addFavoriteCount(@Param("materialId") Long materialId,@Param("incrCount")int incrCount);

    int addDownloadCount(@Param("materialId") Long materialId);

    List<Long> existTagIdList();

}
