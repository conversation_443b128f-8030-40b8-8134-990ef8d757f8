package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.convertor.OriginMaterialConvert;
import cn.com.voyah.material.domain.entity.OriginMaterialDO;
import cn.com.voyah.material.domain.gateway.OriginMaterialGateway;
import cn.com.voyah.material.dto.OriginMaterialExtensionDTO;
import cn.com.voyah.material.mapper.OriginMaterialMapper;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.mybatisflex.query.QueryExt;
import cn.com.voyah.material.pojo.OriginMaterialPO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.com.voyah.domain.entity.def.FileDetailDef.FILE_DETAIL;
import static cn.com.voyah.domain.entity.def.OriginMaterialDef.ORIGIN_MATERIAL;
import static com.mybatisflex.core.query.QueryMethods.case_;
import static com.mybatisflex.core.query.QueryMethods.sum;

/**
 * 素材 服务层实现。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Component
@AllArgsConstructor
public class OriginMaterialGatewayImpl extends GatewayImpl<OriginMaterialDO, OriginMaterialMapper, OriginMaterialPO,
        OriginMaterialConvert> implements OriginMaterialGateway {


    @Override
    public Page<OriginMaterialExtensionDTO> getPageInfo(Page<OriginMaterialExtensionDTO> pageQuery,
                                                        OriginMaterialExtensionDTO originMaterialExtensionDTO,List<Long> authMaterialIdList) {
        // 只返回显示的文件
        originMaterialExtensionDTO.setIsShow(CommonConstants.ENABLE);
        Long total = getMapper().getPageInfoCount(originMaterialExtensionDTO,authMaterialIdList);
        List<OriginMaterialExtensionDTO> list = getMapper().getList(pageQuery.offset(), pageQuery.getPageSize(),
                originMaterialExtensionDTO,authMaterialIdList);
        return new Page<>(list, pageQuery.getPageNumber(), pageQuery.getPageSize(), total);
    }

    @Override
    public OriginMaterialExtensionDTO getDetailById(Long id) {
        QueryWrapper query = QueryWrapper.create();
        query.select(ORIGIN_MATERIAL.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL, FILE_DETAIL.UPLOAD_ID, FILE_DETAIL.UPLOAD_STATUS
                        , FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL,FILE_DETAIL.TH_CONTENT_TYPE)
                .from(ORIGIN_MATERIAL.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(ORIGIN_MATERIAL.FILE_ID))
                .where(ORIGIN_MATERIAL.ID.eq(id));

        OriginMaterialExtensionDTO dto = mapper.selectOneByQueryAs(query, OriginMaterialExtensionDTO.class);
        if (dto != null) {
            // 匹配对应父级名称
            List<Long> parentIdList = dto.getParentIdList();
            if (!parentIdList.isEmpty() && parentIdList.size() > 1) {
                // 查询父级名称
                QueryWrapper where = QueryWrapper.create()
                        .from(ORIGIN_MATERIAL)
                        .select(ORIGIN_MATERIAL.ID, ORIGIN_MATERIAL.MATERIAL_NAME)
                        .where(ORIGIN_MATERIAL.ID.in(dto.getParentIdList().stream().filter(o -> o != 0L).toList()));
                List<OriginMaterialPO> list = mapper.selectListByQueryAs(where, OriginMaterialPO.class);
                Map<Long, String> map = list.stream()
                        .collect(Collectors.toMap(OriginMaterialPO::getId, OriginMaterialPO::getMaterialName));
                // 封装结果
                dto.setParentNameList(parentIdList.stream().map(o -> map.getOrDefault(o, "")).toList());
            } else {
                dto.setParentNameList(Collections.emptyList());
            }
        }
        return dto;
    }

    @Override
    public int updateEntity(OriginMaterialDO originMaterialDO) {
        OriginMaterialPO updateEntity = new OriginMaterialPO();
        updateEntity.setMaterialName(originMaterialDO.getMaterialName());
        updateEntity.setIsShow(originMaterialDO.getIsShow());
        updateEntity.setIsPublic(originMaterialDO.getIsPublic());
        QueryWrapper query = QueryWrapper.create()
                .from(ORIGIN_MATERIAL.getTableName())
                .where(ORIGIN_MATERIAL.ID.eq(originMaterialDO.getId()));
        return mapper.updateByQuery(updateEntity, query);
    }

    @Override
    public List<OriginMaterialExtensionDTO> getList(OriginMaterialExtensionDTO originMaterialExtensionDTO,List<Long> authMaterialIdList) {
        QueryWrapper query = QueryWrapper.create();
        query.select(ORIGIN_MATERIAL.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL, FILE_DETAIL.UPLOAD_ID, FILE_DETAIL.UPLOAD_STATUS
                        , FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL,FILE_DETAIL.TH_CONTENT_TYPE)
                .from(ORIGIN_MATERIAL.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(ORIGIN_MATERIAL.FILE_ID))
                .where(ORIGIN_MATERIAL.PARENT_ID.eq(originMaterialExtensionDTO.getParentId())
                        .and(ORIGIN_MATERIAL.MATERIAL_NAME.eq(originMaterialExtensionDTO.getQueryParam1()).or(ORIGIN_MATERIAL.CREATE_BY.eq(originMaterialExtensionDTO.getQueryParam1())).or(ORIGIN_MATERIAL.CREATE_SHORT_ID.eq(originMaterialExtensionDTO.getQueryParam1())))
                        .and(ORIGIN_MATERIAL.LIBRARY_ID.eq(originMaterialExtensionDTO.getLibraryId()))
                        .and(FILE_DETAIL.UPLOAD_ID.eq(originMaterialExtensionDTO.getUploadId()))
                        .and(FILE_DETAIL.HASH_INFO.eq(originMaterialExtensionDTO.getHashInfo()))
                        .and(ORIGIN_MATERIAL.LEVEL.eq(originMaterialExtensionDTO.getLevel())));
        if(ObjectUtil.isNotEmpty(originMaterialExtensionDTO.getIsShow())){
            query.and(ORIGIN_MATERIAL.IS_SHOW.eq(originMaterialExtensionDTO.getIsShow()));
        }
        if(CommonConstants.HOME_FOLD_PARENT_ID.equals(originMaterialExtensionDTO.getParentId())){
            if(CollectionUtil.isNotEmpty(authMaterialIdList)){
                query.and(ORIGIN_MATERIAL.ID.in(authMaterialIdList));
            }
        }else{
            if(CollectionUtil.isNotEmpty(authMaterialIdList)){
                query.and(ORIGIN_MATERIAL.ID.in(authMaterialIdList).or(QueryExt.conditionJsonContains(authMaterialIdList)));
            }
        }

        return mapper.selectListByQueryAs(query, OriginMaterialExtensionDTO.class);
    }

    @Override
    public void updateChildFileCountById(Long parentId) {
        // 目录及文件统计数据信息
        QueryWrapper query = QueryWrapper.create();
        query.from(ORIGIN_MATERIAL)
                .where(ORIGIN_MATERIAL.PARENT_ID.eq(parentId));
        query.select(sum(case_(ORIGIN_MATERIAL.IS_DIR)
                .when(Boolean.TRUE)
                .then(ORIGIN_MATERIAL.FILE_COUNT)
                // 如果不是是目录，就是文件 数量1
                .else_(1)
                .end()));
        Long totalCount = mapper.selectOneByQueryAs(query, Long.class);
        if (totalCount == null) {
            totalCount = 0L;
        }
        // 修改数量
        OriginMaterialPO updateEntity = new OriginMaterialPO();
        updateEntity.setId(parentId);
        updateEntity.setFileCount(totalCount);
        mapper.update(updateEntity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeIdAndChildAll(Long id) {
        // 不能删除根目录
        if (id == null || id == 0) {
            return;
        }
        OriginMaterialPO materialPO = mapper.selectOneById(id);
        if (materialPO == null) {
            return;
        }
        Integer level = materialPO.getLevel();

        if (materialPO.getIsDir()) {
            // 删除父目录下的所有文件及目录
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.from(ORIGIN_MATERIAL.getTableName())
                    .where(QueryExt.conditionJsonContains(id))
                    .and(ORIGIN_MATERIAL.LEVEL.ge(level));
            OriginMaterialPO update = new OriginMaterialPO();
            update.setIsDelete(Boolean.TRUE);
            mapper.updateByQuery(update, queryWrapper);
        }

        // 删除自身
        mapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIsShow(Long id, Integer isShow) {
        if (id == null || id == 0) {
            return;
        }
        ;
        OriginMaterialPO materialPO = mapper.selectOneById(id);
        if (materialPO == null) {
            return;
        }
        // 删除父目录下的所有文件及目录
        if (materialPO.getIsDir()) {
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.from(ORIGIN_MATERIAL.getTableName())
                    .where(QueryExt.conditionJsonContains(id))
                    .and(ORIGIN_MATERIAL.LEVEL.ge(materialPO.getLevel()));
            OriginMaterialPO update = new OriginMaterialPO();
            update.setIsShow(isShow);
            mapper.updateByQuery(update, queryWrapper);
        }

        // 更新冗余原素才主文件夹
        OriginMaterialPO update = new OriginMaterialPO();
        update.setId(id);
        update.setIsShow(isShow);
        mapper.update(update);
    }

    @Override
    public Boolean updateTagStatus(Long id, Integer tagStatus) {
        if (ObjectUtil.isEmpty(id) || id == 0) {
            return false;
        }
        if (ObjectUtil.isEmpty(tagStatus)) {
            return false;
        }
        OriginMaterialPO update = new OriginMaterialPO();
        update.setId(id);
        update.setTagStatus(tagStatus);
        return mapper.update(update)>0;
    }


    @Override
    public int updatePublicStatusBatchByParentIdList(Long parentId, Integer publicStatus){
        if (ObjectUtil.isEmpty(parentId) || parentId == 0) {
            return 0;
        }
        if (ObjectUtil.isEmpty(publicStatus)) {
            return 0;
        }

        return mapper.updatePublicStatusBatchByParentIdList(parentId,publicStatus);

    }

    @Override
    public void updatePublicStatusByChildStatus(Long parentId) {
        QueryWrapper query = QueryWrapper.create();
        query.select(ORIGIN_MATERIAL.IS_PUBLIC);
        query.from(ORIGIN_MATERIAL)
                .where(ORIGIN_MATERIAL.PARENT_ID.eq(parentId))
                .and(ORIGIN_MATERIAL.IS_DELETE.eq(false))
                .groupBy(ORIGIN_MATERIAL.IS_PUBLIC);
        List<OriginMaterialPO> list=mapper.selectListByQuery(query);
        if(CollectionUtil.isNotEmpty(list)){
            List<Integer> publicStatusList=new ArrayList<>();
            for (OriginMaterialPO each:list) {
                if(ObjectUtil.isNotEmpty(each)&&ObjectUtil.isNotEmpty(each.getIsPublic())){
                    publicStatusList.add(each.getIsPublic());
                }
            }

            if(CollectionUtil.isEmpty(publicStatusList)){
                return ;
            }
            if(publicStatusList.contains(CommonConstants.PUBLIC_STATUS_PART_PUBLIC)){
                updateSelfPublicStatusById(parentId,CommonConstants.PUBLIC_STATUS_PART_PUBLIC);
                return ;
            }
            if(publicStatusList.size()==1){
                updateSelfPublicStatusById(parentId,publicStatusList.get(0));
                return ;
            }
            updateSelfPublicStatusById(parentId,CommonConstants.PUBLIC_STATUS_PART_PUBLIC);
        }

    }


    public void updateSelfPublicStatusById(Long id,Integer publicStatus){
        if (ObjectUtil.isEmpty(id) || id == 0) {
            return ;
        }
        if (ObjectUtil.isEmpty(publicStatus)) {
            return ;
        }
        OriginMaterialPO update = new OriginMaterialPO();
        update.setId(id);
        update.setIsPublic(publicStatus);
        mapper.update(update);
    }

    @Override
    public List<OriginMaterialExtensionDTO> getLabList(OriginMaterialExtensionDTO originMaterialExtensionDTO) {
        QueryWrapper query = QueryWrapper.create();
        query.select(ORIGIN_MATERIAL.ALL_COLUMNS)
                .from(ORIGIN_MATERIAL.getTableName())
                .where(ORIGIN_MATERIAL.PARENT_ID.eq(0)
                        .and(ORIGIN_MATERIAL.IS_DELETE.eq(false)));
        if(ObjectUtil.isNotEmpty(originMaterialExtensionDTO.getIsShow())){
            query.and(ORIGIN_MATERIAL.IS_SHOW.eq(originMaterialExtensionDTO.getIsShow()));
        }
        if(ObjectUtil.isNotEmpty(originMaterialExtensionDTO.getIsPublic())){
            query.and(ORIGIN_MATERIAL.IS_PUBLIC.eq(originMaterialExtensionDTO.getIsPublic()));
        }
        return mapper.selectListByQueryAs(query, OriginMaterialExtensionDTO.class);
    }
    @Override
    public List<OriginMaterialExtensionDTO> getChildrenList(Long id,Boolean onlyDir) {
        QueryWrapper query = QueryWrapper.create();
        query.select(ORIGIN_MATERIAL.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL, FILE_DETAIL.UPLOAD_ID, FILE_DETAIL.UPLOAD_STATUS
                        , FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL,FILE_DETAIL.TH_CONTENT_TYPE)
                .from(ORIGIN_MATERIAL.getTableName())
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(ORIGIN_MATERIAL.FILE_ID))
                .where(ORIGIN_MATERIAL.IS_DELETE.eq(false));
        if(onlyDir){
            query.and(ORIGIN_MATERIAL.IS_DIR.eq(true));
        }
        query.and(QueryExt.conditionJsonContains(id));
        return mapper.selectListByQueryAs(query, OriginMaterialExtensionDTO.class);
    }


}