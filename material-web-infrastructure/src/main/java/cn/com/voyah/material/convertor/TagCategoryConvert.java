package cn.com.voyah.material.convertor;

import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.TagCategoryPO;
import cn.com.voyah.material.domain.entity.TagCategoryDO;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 *  对象转换。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface TagCategoryConvert extends IConvert<TagCategoryDO, TagCategoryPO>{

}