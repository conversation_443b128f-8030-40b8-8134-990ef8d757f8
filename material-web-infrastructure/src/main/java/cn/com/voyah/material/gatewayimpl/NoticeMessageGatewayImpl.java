package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.NoticeMessageGateway;
import cn.com.voyah.material.convertor.NoticeMessageConvert;
import cn.com.voyah.material.domain.entity.NoticeMessageDO;
import cn.com.voyah.material.pojo.NoticeMessagePO;
import cn.com.voyah.material.mapper.NoticeMessageMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 通知表 服务层实现。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Component
@AllArgsConstructor
public class NoticeMessageGatewayImpl extends GatewayImpl<NoticeMessageDO, NoticeMessageMapper, NoticeMessagePO, NoticeMessageConvert> implements NoticeMessageGateway {

}