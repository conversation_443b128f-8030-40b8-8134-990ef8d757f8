package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.dto.ClientInfoDTO;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.ClientInfoGateway;
import cn.com.voyah.material.convertor.ClientInfoConvert;
import cn.com.voyah.material.domain.entity.ClientInfoDO;
import cn.com.voyah.material.pojo.ClientInfoPO;
import cn.com.voyah.material.mapper.ClientInfoMapper;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.com.voyah.domain.entity.def.ClientInfoDef.CLIENT_INFO;
import static cn.com.voyah.domain.entity.def.FileDetailDef.FILE_DETAIL;

/**
 *  服务层实现。
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Component
@AllArgsConstructor
public class ClientInfoGatewayImpl extends GatewayImpl<ClientInfoDO, ClientInfoMapper, ClientInfoPO, ClientInfoConvert> implements ClientInfoGateway {

    @Override
    public List<ClientInfoDTO> getList() {
        QueryWrapper query = QueryWrapper.create();
        query.select(CLIENT_INFO.ALL_COLUMNS, FILE_DETAIL.FILENAME, FILE_DETAIL.URL, FILE_DETAIL.TH_FILENAME, FILE_DETAIL.TH_URL,
                        FILE_DETAIL.CONTENT_TYPE,FILE_DETAIL.SIZE)
                .from(CLIENT_INFO)
                .leftJoin(FILE_DETAIL.getTableName()).on(FILE_DETAIL.ID.eq(CLIENT_INFO.FILE_ID));

        query.where(CLIENT_INFO.ACTIVE.eq(CommonConstants.ENABLE));
        query.and(CLIENT_INFO.IS_DELETE.eq(false));
        query.orderBy(CLIENT_INFO.CREATE_TIME.getName(),false);
        return mapper.selectListByQueryAs(query,ClientInfoDTO.class);
    }
}