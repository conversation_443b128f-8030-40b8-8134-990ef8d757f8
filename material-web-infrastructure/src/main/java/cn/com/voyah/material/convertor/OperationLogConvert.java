package cn.com.voyah.material.convertor;

import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.OperationLogPO;
import cn.com.voyah.material.domain.entity.OperationLogDO;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 * 日志表 对象转换。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface OperationLogConvert extends IConvert<OperationLogDO, OperationLogPO>{

}