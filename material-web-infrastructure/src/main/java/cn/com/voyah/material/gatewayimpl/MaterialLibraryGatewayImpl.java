package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.convertor.MaterialLibraryConvert;
import cn.com.voyah.material.domain.entity.MaterialLibraryDO;
import cn.com.voyah.material.domain.entity.OriginMaterialDO;
import cn.com.voyah.material.domain.gateway.MaterialLibraryGateway;
import cn.com.voyah.material.domain.gateway.OriginMaterialGateway;
import cn.com.voyah.material.exception.Assert;
import cn.com.voyah.material.mapper.MaterialLibraryMapper;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.pojo.MaterialLibraryPO;
import cn.com.voyah.material.util.BoolUtil;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.com.voyah.domain.entity.def.MaterialLibraryDef.MATERIAL_LIBRARY;

/**
 * 素材库（类型） 服务层实现。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Component
@AllArgsConstructor
public class MaterialLibraryGatewayImpl extends GatewayImpl<MaterialLibraryDO, MaterialLibraryMapper, MaterialLibraryPO,
        MaterialLibraryConvert> implements MaterialLibraryGateway {

    private OriginMaterialGateway originMaterialGateway;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialLibraryDO save(MaterialLibraryDO materialLibraryDO) {
        materialLibraryDO.setIsPublic(CommonConstants.PUBLIC_STATUS_PUBLIC);
        // 查找最大编号
        QueryWrapper select = query()
                .select(QueryMethods.max(MATERIAL_LIBRARY.LIBRARY_NO));
        Integer maxNo = mapper.selectOneByQueryAs(select, Integer.class);
        maxNo = maxNo == null ? 1 : maxNo + 1;
        materialLibraryDO.setLibraryNo(String.format("%02d", maxNo));

        // 排序，默认使用序号作为顺序
        materialLibraryDO.setSort(maxNo);


        // 冗余原素才主文件夹
        OriginMaterialDO originMaterialDO = new OriginMaterialDO()
                .setMaterialName(materialLibraryDO.getLibraryName())
                .setIsShow(materialLibraryDO.getStatus())
                .setIsPublic(materialLibraryDO.getIsPublic())
                .setSort(materialLibraryDO.getSort())
                .setIsDir(true)
                .setParentId(0L)
                .setParentIdList(List.of(0L));
        originMaterialDO = originMaterialGateway.save(originMaterialDO);

        // 保存素材库信息
        materialLibraryDO.setOriginMaterialId(originMaterialDO.getId());
        return super.save(materialLibraryDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(MaterialLibraryDO materialLibraryDO) {
        MaterialLibraryPO materialLibrary = mapper.selectOneById(materialLibraryDO.getId());
        Assert.isFalse(materialLibrary == null, "信息不存在");

        // 更新冗余原素才主文件夹
        OriginMaterialDO originMaterialDO = new OriginMaterialDO()
                .setId(materialLibrary.getOriginMaterialId())
                .setMaterialName(materialLibraryDO.getLibraryName())
                .setIsShow(materialLibraryDO.getStatus());
        originMaterialGateway.updateById(originMaterialDO);

        // 更新素材库信息
        return super.updateById(materialLibraryDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Serializable id) {
        MaterialLibraryPO materialLibrary = mapper.selectOneById(id);
        if (materialLibrary == null) {
            return true;
        }
        originMaterialGateway.removeIdAndChildAll(materialLibrary.getOriginMaterialId());
        return super.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableOnDisable(Long id, boolean status) {
        MaterialLibraryPO materialLibrary = mapper.selectOneById(id);
        Assert.isFalse(materialLibrary == null, "信息不存在");
        if (status == BoolUtil.toBoolean(materialLibrary.getStatus())) {
            return;
        }

        MaterialLibraryPO update = new MaterialLibraryPO();
        update.setId(id);
        update.setStatus(BoolUtil.toInt(status));
        int flag = mapper.update(update);
        Assert.isFalse(flag != 1, "修改失败！");

        // 更新冗余原素才主文件夹
        originMaterialGateway.updateIsShow(materialLibrary.getOriginMaterialId(), update.getStatus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(List<MaterialLibraryDO> list) {
        List<Long> libraryIds = list.stream().map(MaterialLibraryDO::getId).toList();
        if (libraryIds.isEmpty()) {
            return;
        }

        List<MaterialLibraryDO> libs = this.listByIds(libraryIds);
        Map<Long, Long> librayMap = libs.stream()
                .collect(Collectors.toMap(MaterialLibraryDO::getId, MaterialLibraryDO::getOriginMaterialId));
        List<OriginMaterialDO> writeOriginMaterials = new ArrayList<>();
        for (MaterialLibraryDO library : list) {
            Long id = library.getId();
            if (librayMap.containsKey(id)) {
                Long originMaterialId = librayMap.get(library.getId());
                writeOriginMaterials.add(new OriginMaterialDO()
                        .setId(originMaterialId)
                        .setSort(library.getSort()));
            }
        }
        boolean isUp = this.updateBatch(list);
        Assert.isTrue(isUp, "修改失败！");

        // 更新冗余原素才主文件夹
        if (!writeOriginMaterials.isEmpty()) {
            isUp = originMaterialGateway.updateBatch(writeOriginMaterials);
            Assert.isTrue(isUp, "修改失败！");
        }
    }

    @Override
    public boolean updatePublicByMaterialId(Long materialId,Integer publicStatus){
        OriginMaterialDO originMaterialDO=new OriginMaterialDO();
        originMaterialDO.setId(materialId);
        originMaterialDO.setIsPublic(publicStatus);
        originMaterialGateway.updateEntity(originMaterialDO);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(MATERIAL_LIBRARY.getTableName())
                .where(MATERIAL_LIBRARY.ORIGIN_MATERIAL_ID.eq(materialId));
        MaterialLibraryPO update = new MaterialLibraryPO();
        update.setIsPublic(publicStatus);
        mapper.updateByQuery(update,queryWrapper);
        return false;
    }
}