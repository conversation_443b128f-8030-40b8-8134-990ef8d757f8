package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.convertor.FileDetailConvert;
import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.gateway.FileDetailGateway;
import cn.com.voyah.material.mapper.FileDetailMapper;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.pojo.FileDetailPO;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import static cn.com.voyah.domain.entity.def.FileDetailDef.FILE_DETAIL;

/**
 * 文件记录表 服务层实现。
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Component
@AllArgsConstructor
public class FileDetailGatewayImpl extends GatewayImpl<FileDetailDO, FileDetailMapper, FileDetailPO, FileDetailConvert> implements FileDetailGateway {

    @Override
    public FileDetailDO getOneByEntity(FileDetailDO fileDetailDO) {
        QueryWrapper query = QueryWrapper.create();
        query.select(FILE_DETAIL.ALL_COLUMNS)
                .from(FILE_DETAIL.getTableName())
                .where(FILE_DETAIL.ID.eq(fileDetailDO.getId()))
                .and(FILE_DETAIL.FILENAME.eq(fileDetailDO.getFilename()))
                .and(FILE_DETAIL.ORIGINAL_FILENAME.eq(fileDetailDO.getOriginalFilename()))
                .and(FILE_DETAIL.UPLOAD_ID.eq(fileDetailDO.getUploadId()));
        return mapper.selectOneByQueryAs(query, FileDetailDO.class);
    }

    @Override
    public void updateThById(FileDetailDO fileDetailDO) {
        FileDetailPO updateEntity=new FileDetailPO();
        updateEntity.setThUrl(fileDetailDO.getThUrl());
        updateEntity.setThFilename(fileDetailDO.getThFilename());
        updateEntity.setThSize(fileDetailDO.getThSize());
        updateEntity.setThContentType(fileDetailDO.getThContentType());
        QueryWrapper query = QueryWrapper.create()
                .from(FILE_DETAIL.getTableName())
                .where(FILE_DETAIL.ID.eq(fileDetailDO.getId()));
        mapper.updateByQuery(updateEntity,query);
    }
}