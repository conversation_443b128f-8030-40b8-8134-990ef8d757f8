package cn.com.voyah.material.convertor;

import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.ClientInfoPO;
import cn.com.voyah.material.domain.entity.ClientInfoDO;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 *  对象转换。
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface ClientInfoConvert extends IConvert<ClientInfoDO, ClientInfoPO>{

}