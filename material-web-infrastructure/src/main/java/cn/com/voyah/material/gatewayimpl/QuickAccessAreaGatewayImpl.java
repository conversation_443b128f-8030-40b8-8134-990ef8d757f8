package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.QuickAccessAreaGateway;
import cn.com.voyah.material.convertor.QuickAccessAreaConvert;
import cn.com.voyah.material.domain.entity.QuickAccessAreaDO;
import cn.com.voyah.material.pojo.QuickAccessAreaPO;
import cn.com.voyah.material.mapper.QuickAccessAreaMapper;
import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.List;
import static cn.com.voyah.domain.entity.def.QuickAccessAreaDef.QUICK_ACCESS_AREA;

/**
 * 金刚位表 服务层实现。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Component
@AllArgsConstructor
public class QuickAccessAreaGatewayImpl extends GatewayImpl<QuickAccessAreaDO, QuickAccessAreaMapper, QuickAccessAreaPO, QuickAccessAreaConvert> implements QuickAccessAreaGateway {

    @Override
    public QuickAccessAreaPO getNearerDOBySort(Long sort, boolean isAsc) {
        QueryWrapper query = QueryWrapper.create();
        query.select(QUICK_ACCESS_AREA.ALL_COLUMNS);
        query.from(QUICK_ACCESS_AREA)
                .where(QUICK_ACCESS_AREA.IS_DELETE.eq(false))
                .orderBy(QUICK_ACCESS_AREA.SORT,isAsc).limit(1);
        if(isAsc){
            query.and(QUICK_ACCESS_AREA.SORT.gt(sort));
        }else{
            query.and(QUICK_ACCESS_AREA.SORT.lt(sort));
        }
        List<QuickAccessAreaPO> list=mapper.selectListByQuery(query);
        if(CollectionUtil.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @Override
    public boolean updateSort(Long id, Long sort) {
        QuickAccessAreaPO update = new QuickAccessAreaPO();
        update.setId(id);
        update.setSort(sort);
        return mapper.update(update)>0;
    }
}