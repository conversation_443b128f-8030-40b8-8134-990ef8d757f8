package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.MaterialFavoriteGateway;
import cn.com.voyah.material.convertor.MaterialFavoriteConvert;
import cn.com.voyah.material.domain.entity.MaterialFavoriteDO;
import cn.com.voyah.material.pojo.MaterialFavoritePO;
import cn.com.voyah.material.mapper.MaterialFavoriteMapper;
import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static cn.com.voyah.domain.entity.def.MaterialFavoriteDef.MATERIAL_FAVORITE;

/**
 * 收藏表 服务层实现。
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Component
@AllArgsConstructor
public class MaterialFavoriteGatewayImpl extends GatewayImpl<MaterialFavoriteDO, MaterialFavoriteMapper, MaterialFavoritePO, MaterialFavoriteConvert> implements MaterialFavoriteGateway {

    @Override
    public boolean deleteByMaterialIdAndShortId(Long materialId, String shortId) {
        MaterialFavoritePO materialFavoritePO=new MaterialFavoritePO();
        materialFavoritePO.setIsDelete(true);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(MATERIAL_FAVORITE.getTableName())
                .where(MATERIAL_FAVORITE.MATERIAL_ID.eq(materialId))
                .and(MATERIAL_FAVORITE.SHORT_ID.eq(shortId))
                .and(MATERIAL_FAVORITE.IS_DELETE.eq(false));
        return mapper.updateByQuery(materialFavoritePO,queryWrapper)>0;
    }

    @Override
    public List<Long> getHaveFavoriteIdList(List<Long> materialIdList, String shortId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(MATERIAL_FAVORITE.getTableName())
                .where(MATERIAL_FAVORITE.MATERIAL_ID.in(materialIdList))
                .and(MATERIAL_FAVORITE.SHORT_ID.eq(shortId))
                .and(MATERIAL_FAVORITE.IS_DELETE.eq(false));
        List<MaterialFavoritePO> list= mapper.selectListByQuery(queryWrapper);
        if(CollectionUtil.isNotEmpty(list)){
            return list.stream().map(each->each.getMaterialId()).collect(Collectors.toList());
        }
        return List.of();
    }
}