package cn.com.voyah.material.config;

import cn.com.voyah.material.dto.FileSourceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@Component
@ConfigurationProperties(prefix = "material.user-platform")
public class MaterialUserPlatformConfig {

    private List<FileSourceType> fileSourceTypes;


    public Set<String> getAllSuffixes() {
        Set<String> res = new HashSet<>();
        fileSourceTypes.stream().forEach(t -> {
            res.addAll(t.getSuffixes());

        });
        return res;
    }

}
