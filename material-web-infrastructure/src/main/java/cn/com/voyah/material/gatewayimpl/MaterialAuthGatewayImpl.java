package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.client.MaterialAuthService;
import cn.com.voyah.material.convertor.MaterialAuthConvert;
import cn.com.voyah.material.domain.entity.MaterialAuthDO;
import cn.com.voyah.material.domain.gateway.MaterialAuthGateway;
import cn.com.voyah.material.mapper.MaterialAuthMapper;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.pojo.MaterialAuthPO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.com.voyah.domain.entity.def.MaterialAuthDef.MATERIAL_AUTH;

/**
 *  服务层实现。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Component
@AllArgsConstructor
public class MaterialAuthGatewayImpl extends GatewayImpl<MaterialAuthDO, MaterialAuthMapper, MaterialAuthPO, MaterialAuthConvert> implements MaterialAuthGateway {

    private MaterialAuthService materialAuthService;

    @Override
    public List<MaterialAuthPO> getList(String shortId) {
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.from(MATERIAL_AUTH.getTableName()).where(MATERIAL_AUTH.SHORT_ID.eq(shortId)).and(MATERIAL_AUTH.IS_DELETE.eq(false));
       return getMapper().selectListByQuery(queryWrapper);
    }

    @Override
    public MaterialAuthPO getAuth(String shortId, Long materialFoldId) {
        List<MaterialAuthPO> authList= getList(shortId);
        if(CollectionUtil.isNotEmpty(authList)){
            MaterialAuthPO materialAuthPO=authList.get(0);
            List<Long> idList=materialAuthPO.getMaterialIdList();
            if(CollectionUtil.isNotEmpty(idList)&&idList.contains(materialFoldId)){
                return materialAuthPO;
            }
        }
        return null;
    }

    @Override
    public void updateAllAuth(List<String>materialIdList,Long materialId) {
        List<MaterialAuthPO> allAuth = mapper.getAllAuth(materialIdList);
        allAuth.stream().forEach(t->{
            t.getMaterialIdList().add(materialId);
            MaterialAuthDO materialAuthDO =new MaterialAuthDO();
            BeanUtil.copyProperties(t,materialAuthDO);
            this.updateById(materialAuthDO);
            materialAuthService.removeAuthKeyCache(t.getShortId());
        });
    }

}