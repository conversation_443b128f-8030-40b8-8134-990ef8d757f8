package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.domain.gateway.FileDetailTmpGateway;
import cn.com.voyah.material.convertor.FileDetailTmpConvert;
import cn.com.voyah.material.domain.entity.FileDetailTmpDO;
import cn.com.voyah.material.pojo.FileDetailTmpPO;
import cn.com.voyah.material.mapper.FileDetailTmpMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 文件记录中间表 服务层实现。
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
@Component
@AllArgsConstructor
public class FileDetailTmpGatewayImpl extends GatewayImpl<FileDetailTmpDO, FileDetailTmpMapper, FileDetailTmpPO, FileDetailTmpConvert> implements FileDetailTmpGateway {

}