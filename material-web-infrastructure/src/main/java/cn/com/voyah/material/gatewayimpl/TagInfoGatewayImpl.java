package cn.com.voyah.material.gatewayimpl;

import cn.com.voyah.material.convertor.TagInfoConvert;
import cn.com.voyah.material.domain.entity.TagInfoDO;
import cn.com.voyah.material.domain.gateway.TagInfoGateway;
import cn.com.voyah.material.dto.query.TagInfoQuery;
import cn.com.voyah.material.mapper.TagInfoMapper;
import cn.com.voyah.material.mybatisflex.gateway.GatewayImpl;
import cn.com.voyah.material.pojo.TagInfoPO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.com.voyah.domain.entity.def.TagInfoDef.TAG_INFO;

/**
 *  服务层实现。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Component
@AllArgsConstructor
public class TagInfoGatewayImpl extends GatewayImpl<TagInfoDO, TagInfoMapper, TagInfoPO, TagInfoConvert> implements TagInfoGateway {

    /**
     * 可见
     */
    public static final Integer HIDDEN_ALL_TRUE = 0;
    /**
     * 部分可见
     */
    public static final Integer HIDDEN_HAVE_TRUE = 1;

    /**
     * 隐藏
     */
    public static final Integer HIDDEN_ALL_FALSE = 2;
    @Override
    public List<TagInfoDO> list(TagInfoDO tagInfoDO) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_INFO.ALL_COLUMNS)
                .from(TAG_INFO.getTableName())
                .where(TAG_INFO.CATEGORY_ID.eq(tagInfoDO.getCategoryId())
                        .and(TAG_INFO.IS_DELETE.eq(false))
                        .and(TAG_INFO.LEVEL.eq(tagInfoDO.getLevel()))
                        .and(TAG_INFO.TAG_VALUE.like(tagInfoDO.getTagValue())));
        return mapper.selectListByQueryAs(query, TagInfoDO.class);
    }

    @Override
    public List<TagInfoDO> getList(Long categoryId) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_INFO.ALL_COLUMNS)
                .from(TAG_INFO.getTableName())
                .where(TAG_INFO.IS_DELETE.eq(false));
        if(ObjectUtil.isNotEmpty(categoryId)){
            query.and(TAG_INFO.CATEGORY_ID.eq(categoryId));
        }
        query.orderBy(TAG_INFO.LEVEL.getName());
        return mapper.selectListByQueryAs(query, TagInfoDO.class);
    }

    @Override
    public List<TagInfoDO> getList(TagInfoDO tagInfoDO, List<Long> parentIdList) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_INFO.ALL_COLUMNS)
                .from(TAG_INFO.getTableName())
                .where(TAG_INFO.CATEGORY_ID.eq(tagInfoDO.getCategoryId())
                        .and(TAG_INFO.IS_DELETE.eq(false))
                        .and(TAG_INFO.LEVEL.eq(tagInfoDO.getLevel())));
        if(StringUtils.isNotEmpty(tagInfoDO.getName())){
            query.and(TAG_INFO.NAME.eq(tagInfoDO.getName()));
        }
        if(StringUtils.isNotEmpty(tagInfoDO.getTagValue())){
            query.and(TAG_INFO.TAG_VALUE.eq(tagInfoDO.getTagValue()));
        }
        if(ObjectUtil.isNotEmpty(tagInfoDO.getParentId())){
            query.and(TAG_INFO.PARENT_ID.eq(tagInfoDO.getParentId()));
        }
        if(CollectionUtil.isNotEmpty(parentIdList)){
            query.and(TAG_INFO.PARENT_ID.in(parentIdList));
        }
        return mapper.selectListByQueryAs(query, TagInfoDO.class);
    }

    @Override
    public List<TagInfoDO> getTagNameAndLevelList(Long categoryId) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_INFO.NAME,TAG_INFO.LEVEL,TAG_INFO.CATEGORY_ID,QueryMethods.min(TAG_INFO.CREATE_TIME).as("create_time"))
                .from(TAG_INFO.getTableName())
                .where(TAG_INFO.CATEGORY_ID.eq(categoryId)
                        .and(TAG_INFO.IS_DELETE.eq(false))).groupBy(TAG_INFO.NAME,TAG_INFO.LEVEL);
        return mapper.selectListByQueryAs(query, TagInfoDO.class);
    }

    @Override
    public List<TagInfoDO> getListByIdList(List<Long> idList) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_INFO.ALL_COLUMNS)
                .from(TAG_INFO.getTableName())
                .where(TAG_INFO.ID.in(idList)
                        .and(TAG_INFO.IS_DELETE.eq(false)));
        return mapper.selectListByQueryAs(query, TagInfoDO.class);
    }

    @Override
    public List<TagInfoDO> getByParentIdList(TagInfoQuery tagInfoQuery) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_INFO.ALL_COLUMNS)
                .from(TAG_INFO.getTableName())
                .where(TAG_INFO.PARENT_ID.in(tagInfoQuery.getParentTagIdList())
                        .and(TAG_INFO.IS_DELETE.eq(false))
                        .and(TAG_INFO.CATEGORY_ID.eq(tagInfoQuery.getCategoryId()))
                        .and(TAG_INFO.NAME.eq(tagInfoQuery.getTagName()))
                        .and(TAG_INFO.LEVEL.eq(tagInfoQuery.getQueryLevel())));
        return mapper.selectListByQueryAs(query, TagInfoDO.class);
    }

    @Override
    public List<TagInfoDO> getListByParentId(Long parentId) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_INFO.ALL_COLUMNS)
                .from(TAG_INFO.getTableName())
                .where(TAG_INFO.PARENT_ID.eq(parentId)
                        .and(TAG_INFO.IS_DELETE.eq(false)));
        List<TagInfoDO> tagInfoDOS = mapper.selectListByQueryAs(query, TagInfoDO.class);
        this.setHiddenStatus(tagInfoDOS);
        return tagInfoDOS;
    }

    @Override
    public List<TagInfoDO> getList(Long categoryId, Integer level, String tagName,List<String> values, List<Long> parentIdList) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_INFO.ALL_COLUMNS)
                .from(TAG_INFO.getTableName())
                .where(TAG_INFO.CATEGORY_ID.eq(categoryId)
                        .and(TAG_INFO.IS_DELETE.eq(false))
                        .and(TAG_INFO.PARENT_ID.in(parentIdList))
                        .and(TAG_INFO.NAME.eq(tagName))
                        .and(TAG_INFO.TAG_VALUE.in(values))
                        .and(TAG_INFO.LEVEL.eq(level)));
        return mapper.selectListByQueryAs(query, TagInfoDO.class);
    }

    @Override
    public List<TagInfoDO> getTagNameAndValueList(Long categoryId,boolean filterHiden) {
        QueryWrapper query = QueryWrapper.create();
        query.select(TAG_INFO.NAME,TAG_INFO.LEVEL,TAG_INFO.NAME,TAG_INFO.TAG_VALUE,TAG_INFO.CATEGORY_ID,QueryMethods.min(TAG_INFO.CREATE_TIME).as("create_time"))
                .from(TAG_INFO.getTableName())
                .where(TAG_INFO.IS_DELETE.eq(false));
        if(ObjectUtil.isNotEmpty(categoryId)){
            query.and(TAG_INFO.CATEGORY_ID.eq(categoryId));
        }
        if(filterHiden){
            query.and(TAG_INFO.HIDDEN.eq(false));
        }
        query.groupBy(TAG_INFO.NAME,TAG_INFO.LEVEL,TAG_INFO.TAG_VALUE,TAG_INFO.CATEGORY_ID);
        return mapper.selectListByQueryAs(query, TagInfoDO.class);
    }

    @Override
    public void updateHiddenStatusByExistTagIdList(List<Long> existTagIdList) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.from(TAG_INFO.getTableName())
                .where(TAG_INFO.IS_DELETE.eq(false))
                .and(TAG_INFO.ID.notIn(existTagIdList));
        TagInfoPO update = new TagInfoPO();
        update.setHidden(true);
        mapper.updateByQuery(update, queryWrapper);

        QueryWrapper queryWrapper2 = new QueryWrapper();
        queryWrapper2.from(TAG_INFO.getTableName())
                .where(TAG_INFO.IS_DELETE.eq(false))
                .and(TAG_INFO.ID.in(existTagIdList));
        TagInfoPO update2 = new TagInfoPO();
        update.setHidden(false);
        mapper.updateByQuery(update2, queryWrapper2);
    }
    @Override
    public List<TagInfoDO> getAllTreeList(Long id) {
//        QueryWrapper wrapper = QueryWrapper.create().eq(TagInfoDO::getId,id).eq(TagInfoDO::getIsDelete,false);
        List<TagInfoDO> entityList = new ArrayList<>();
        List<TagInfoDO> allList = new ArrayList<>();
        TagInfoDO tagInfoDO = this.getById(id);
        allList.add(tagInfoDO);
        entityList.add(tagInfoDO);
        getSubList(entityList, allList);
        return allList;
    }
    @Override
    public void setHiddenStatus(List<TagInfoDO> tagInfoDOS){
        tagInfoDOS.stream().forEach(t -> {
            List<TagInfoDO> allSubTreeList = this.getAllSubTreeList(t.getId());
            if (CollectionUtil.isEmpty(allSubTreeList)) {
                t.setHiddenStatus(t.isHidden() ? HIDDEN_ALL_FALSE : HIDDEN_ALL_TRUE);
            }else {
                List<Boolean> collect = allSubTreeList.stream().map(TagInfoDO::isHidden).collect(Collectors.toList());
                if (collect.contains(true) && collect.contains(false)) {
                    t.setHiddenStatus(HIDDEN_HAVE_TRUE);
                }else {
                    t.setHiddenStatus(t.isHidden() ? HIDDEN_ALL_FALSE : HIDDEN_ALL_TRUE);
                }
            }

        });

    }
    @Override
    public List<TagInfoDO> getAllSubTreeList(Long id) {
//        QueryWrapper wrapper = QueryWrapper.create().eq(TagInfoDO::getId,id).eq(TagInfoDO::getIsDelete,false);
        List<TagInfoDO> entityList = new ArrayList<>();
        List<TagInfoDO> allList = new ArrayList<>();
        TagInfoDO tagInfoDO = this.getById(id);
        entityList.add(tagInfoDO);
        getSubList(entityList, allList);
        return allList;
    }

    @Override
    public List<TagInfoDO> getAllParentList(Long id) {
        return mapper.selectAllParents(id);
    }




    private List<TagInfoDO> getSubList(List<TagInfoDO> entityList, List<TagInfoDO> allList) {
        for (int i = 0; i < entityList.size(); i++) {
            QueryWrapper wrapper = QueryWrapper.create().eq(TagInfoDO::getParentId, entityList.get(i).getId()).eq(TagInfoDO::getIsDelete, false);
            List<TagInfoDO> entityList2 = this.list(wrapper);
            allList.addAll(entityList2);
            getSubList(entityList2, allList);
        }
        return allList;
    }
}