package cn.com.voyah.material.utils;

import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.exception.JsonException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;


@Slf4j
@Component
public class RedisUtil {

    public RedisTemplate<String, String> redisTemplate;

    private final ObjectMapper objectMapper;

    public RedisUtil(RedisTemplate<String, String> redisTemplate,
                     @Qualifier("redisObjectMapper") ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
    }

    private <V> V convert(String value, Class<V> clazz) {
        try {
            return objectMapper.readValue(value, clazz);
        } catch (JsonProcessingException e) {
            throw new JsonException(e);
        }
    }

    private String convertStr(Object value) {
        try {
            if (value instanceof String) {
                return (String) value;
            }
            return objectMapper.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new JsonException(e);
        }
    }
    private String[] convertArrayStr(Object... values) {
        return Arrays.stream(values).map(this::convertStr).toArray(String[]::new);
    }

    @SuppressWarnings("unchecked")
    private <V> List<V> convert(List<String> list, Class<V> clazz) {
        if (clazz == String.class) {
            return (List<V>) list;
        }
        return list.stream().map(v -> convert(v, clazz)).toList();
    }

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public String get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }

    /**
     * 批量获取keys中的值
     */
    public <V> List<V> get(Collection<String> keys, Class<V> clazz) {
        List<String> list = redisTemplate.opsForValue().multiGet(keys);
        return this.convert(list, clazz);
    }

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     */
    public void expire(String key, long time) {
        if (time <= 0) {
            return;
        }
        redisTemplate.expire(key, time, TimeUnit.SECONDS);
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回 0 代表为永久有效
     */
    public long getExpire(String key) {
        Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        return expire == null ? 0L : expire;
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        Boolean flag = redisTemplate.hasKey(key);
        return Boolean.TRUE.equals(flag);
    }


    /**
     * 删除缓存
     *
     * @param key 首先判断改值是否存在，然后进行删除
     */
    public void del(String key) {
        if (hasKey(key)) {
            redisTemplate.delete(key);
        }
    }

    public void delKeyWithPrefix(String keyPrefix) {
        Set<String> set = redisTemplate.keys(keyPrefix + "*");
        if (set == null || set.isEmpty()) {
            return;
        }
        for (String key : set) {
            if (key != null && !key.isBlank()) {
                redisTemplate.delete(key);
            }
        }

    }


    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     */
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, convertStr(value));
    }

    public void set(String key, Object value, long sec) {
        if (sec > 0) {
            redisTemplate.opsForValue().set(key, convertStr(value), sec, TimeUnit.SECONDS);
        } else {
            set(key, value);
        }
    }

    public void zSetAdd(String key, Object value, long score) {
        if (score > 0) {
            redisTemplate.opsForZSet()
                    .add(key, convertStr(value), score);
        } else {
            redisTemplate.opsForSet()
                    .add(key, convertStr(value));
        }
    }


    public void removeRangeByScore(String key, double min, double max) {
        redisTemplate.opsForZSet().removeRangeByScore(key, min, max);
    }


    public Set<ZSetOperations.TypedTuple<String>> zSetGet(String key, Integer start, Integer end) {
        Set<ZSetOperations.TypedTuple<String>> result = null;
        if (key != null && !key.isBlank()) {
            result = redisTemplate.opsForZSet().rangeWithScores(key, start, end);
        }
        return result;
    }

    public Set<String> rangeByScore(String key, long start, long end) {
        return redisTemplate.opsForZSet().rangeByScore(key, start, end);
    }

    public Double zSetScore(String key, Object value) {
        return redisTemplate.opsForZSet().score(key, convertStr(value));
    }


    /**
     * 获取hashKey对应的所有键值,并返回为对应的值
     *
     * @param hKey 键
     * @return 对应的多个键值
     */
    public <V> Map<String, V> hmGet(String hKey) {
        return redisTemplate.<String, V>opsForHash().entries(hKey);
    }


    /**
     * HashGet
     *
     * @param key     键 不能为null
     * @param hashKey 项 不能为null
     * @return 值
     */
    public <V> V hmGet(String key, String hashKey) {
        return redisTemplate.<String, V>opsForHash().get(key, hashKey);
    }

    /**
     * 获取key 对应的List集合信息
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public <V> List<V> hmMultiGetGet(String key, List<String> hKeys) {
        return redisTemplate.<String, V>opsForHash().multiGet(key, hKeys);
    }

    /**
     * HashSet
     * 一次性存入多个键值对
     *
     * @param key 键
     * @param map 对应多个键值
     */
    public void hmSet(String key, Map<String, ?> map) {
        redisTemplate.opsForHash().putAll(key, map);
    }

    /**
     * HashSet
     * 一次性存入多个键值对
     *
     * @param key 键
     */
    public void hmSet(String key, String hashKey, Object value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }


    /**
     * HashSet 并设置时间 hao
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     */
    public void hmSetExpire(String key, Map<String, ?> map, long time) {
        redisTemplate.opsForHash().putAll(key, map);
        if (time > 0) {
            expire(key, time);
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key     键
     * @param hashKey 项
     * @param value   值
     */
    public void hSet(String key, String hashKey, Object value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key     键
     * @param hashKey 项
     * @param value   值
     * @param time    时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     */
    public void hSet(String key, String hashKey, Object value, long time) {
        redisTemplate.opsForHash().put(key, hashKey, value);
        if (time > 0) {
            expire(key, time);
        }
    }

    /**
     * 删除hash表中的值
     *
     * @param key     键 不能为null
     * @param hashKey 项 可以使多个 不能为null
     */
    public void hDel(String key, Object... hashKey) {
        redisTemplate.opsForHash().delete(key, hashKey);
    }

    /**
     * 删除hash表中的值
     *
     * @param key      键 不能为null
     * @param hashKeys 项 可以使多个 不能为null
     */
    public void hDel(String key, Collection<String> hashKeys) {
        redisTemplate.opsForHash().delete(key, hashKeys.toArray());
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key     键 不能为null
     * @param hashKey 项 不能为null
     * @return true 存在 false不存在
     */
    public boolean hHasKey(String key, String hashKey) {
        return redisTemplate.opsForHash().hasKey(key, hashKey);
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key     键
     * @param hashKey 项
     * @param delta   要增加几(大于0)
     */
    public double hIncr(String key, String hashKey, double delta) {
        if (delta < 0) {
            throw new BizException("递增因子必须大于0");
        }
        return redisTemplate.opsForHash().increment(key, hashKey, delta);
    }

    /**
     * hash递减
     *
     * @param key     键
     * @param hashKey 项
     * @param delta   要减少记(小于0)
     */
    public double hDecr(String key, String hashKey, double delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return redisTemplate.opsForHash().increment(key, hashKey, -delta);
    }


    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束 0 到 -1代表所有值
     */
    public List<String> lGet(String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    /**
     * 将value放入list的右边
     *
     * @param key   键
     * @param value 值
     */
    public void lRightSet(String key, String value) {
        redisTemplate.opsForList().rightPush(key, value);
    }


    public void lLeftSet(String key, String value) {
        redisTemplate.opsForList().leftPush(key, value);
    }

    public void lRightPushAll(String key, List<String> values) {
        redisTemplate.opsForList().rightPushAll(key, values);
    }


    /**
     * 获取list缓存的长度
     *
     * @param key 键
     */
    public long lGetSize(String key) {
        Long size = redisTemplate.opsForList().size(key);
        return size == null ? 0 : size;
    }


    public void setAdd(String key, String... values) {
        redisTemplate.opsForSet().add(key, values);
    }

    public Set<String> setMembers(String key) {
        return redisTemplate.opsForSet().members(key);
    }

    public boolean setIsMember(String key, String value) {
        return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, value));
    }

    public void setRemove(String key, String... values) {
        redisTemplate.opsForSet().remove(key, (Object[]) values);
    }

    public boolean hashHasKey(String key, String hashKey) {
        return redisTemplate.opsForHash().hasKey(key, hashKey);
    }

    public void zSetRemove(String key, String value) {
        redisTemplate.opsForZSet().remove(key, value);
    }

    public Long incr(String key, Long milliSeconds) {
        String s = redisTemplate.boundValueOps(key).get();
        if (s == null) {
            redisTemplate.opsForValue().set(key, 0 + "", milliSeconds, TimeUnit.MILLISECONDS);
        } else {
            redisTemplate.expire(key, milliSeconds, TimeUnit.MILLISECONDS);
        }
        return redisTemplate.opsForValue().increment(key, 1);
    }

    public long increment(String key) {
        Long increment = redisTemplate.opsForValue().increment(key);
        return increment == null ? 0 : increment;
    }

    public long decrement(String key) {
        Long decrement = redisTemplate.opsForValue().decrement(key);
        return decrement == null ? 0 : decrement;
    }

    public long incr(String key, long delta, Long milliSeconds) {
        String s = redisTemplate.boundValueOps(key).get();
        if (s == null) {
            redisTemplate.opsForValue().set(key, "0", milliSeconds, TimeUnit.MILLISECONDS);
        } else {
            redisTemplate.expire(key, milliSeconds, TimeUnit.MILLISECONDS);
        }
        Long increment = redisTemplate.opsForValue().increment(key, delta);
        return increment == null ? delta : increment;
    }

    public void publishMessage(String channel, String message) {
        redisTemplate.convertAndSend(channel, message);
    }
}
