package cn.com.voyah.material.convertor;

import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.FileDetailTmpPO;
import cn.com.voyah.material.domain.entity.FileDetailTmpDO;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 * 文件记录中间表 对象转换。
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface FileDetailTmpConvert extends IConvert<FileDetailTmpDO, FileDetailTmpPO>{

}