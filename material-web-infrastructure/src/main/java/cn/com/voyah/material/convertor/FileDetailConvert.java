package cn.com.voyah.material.convertor;


import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.mybatisflex.convert.IConvert;
import cn.com.voyah.material.pojo.FileDetailPO;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;


/**
 * 文件记录表 对象转换。
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public interface FileDetailConvert extends IConvert<FileDetailDO, FileDetailPO> {

}