package cn.com.voyah.material.catchlog;


import cn.com.voyah.material.dto.Response;
import cn.com.voyah.material.exception.BaseException;
import lombok.extern.slf4j.Slf4j;

/**
 * ResponseHandler
 *
 * <AUTHOR>
 * @date 2020-11-10 3:18 PM
 */
@Slf4j
public class DefaultResponseHandler implements ResponseHandlerI {

    @Override
    public Object handle(Class<?> returnType, int errCode, String errMsg) {
        if (isColaResponse(returnType)) {
            return handleColaResponse(returnType, errCode, errMsg);
        }
        return null;
    }

    public Object handle(Class<?> returnType, BaseException e) {
        return handle(returnType, e.getErrCode(), e.getMessage());
    }


    private static Object handleColaResponse(Class<?> returnType, int errCode, String errMsg) {
        try {
            Response response = (Response) returnType.getDeclaredConstructor().newInstance();
            return response.failure(errCode, errMsg);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return null;
        }
    }

    private static boolean isColaResponse(Class<?> returnType) {
        return returnType == Response.class || returnType.getGenericSuperclass() == Response.class;
    }
}
