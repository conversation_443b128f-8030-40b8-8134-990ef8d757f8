<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.voyah.material</groupId>
        <artifactId>material-components</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>material-component-catchlog</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.voyah.material</groupId>
            <artifactId>material-component-common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.voyah.material</groupId>
            <artifactId>material-web-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.iov.tencent</groupId>-->
<!--            <artifactId>inc-access-sdk</artifactId>-->
<!--            <version>23.0-SNAPSHOT</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
    </dependencies>

</project>