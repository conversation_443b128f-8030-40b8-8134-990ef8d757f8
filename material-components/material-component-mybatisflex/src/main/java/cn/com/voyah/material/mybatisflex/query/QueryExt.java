package cn.com.voyah.material.mybatisflex.query;

import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.RawQueryCondition;

import java.util.Collection;

public class QueryExt {


    public static QueryCondition conditionJsonContains(Long value) {
        return new RawQueryCondition("JSON_CONTAINS(`parent_id_list`, JSON_ARRAY(?))", value);
    }

    public static QueryCondition conditionJsonContains(Collection<Long> collection) {
        StringBuilder paramsStr=new StringBuilder();
        int i=0;
        for (Long each:collection) {
            if(i!=0){
                paramsStr.append(" or ");
            }
            paramsStr.append("JSON_CONTAINS(parent_id_list, JSON_ARRAY(");
            paramsStr.append(each);
            paramsStr.append("))");
            i++;
        }
        return new RawQueryCondition(paramsStr.toString());
    }


    public static QueryCondition conditionAndJsonContains(Collection<Long> collection) {
        StringBuilder paramsStr=new StringBuilder();
        int i=0;
        paramsStr.append("JSON_CONTAINS(parent_id_list, JSON_ARRAY(");
        for (Long each:collection) {
            if(i!=0){
                paramsStr.append(",");
            }
            paramsStr.append(each);
            i++;
        }
        paramsStr.append("))");
        return new RawQueryCondition(paramsStr.toString());
    }

    public static QueryCondition conditionAndJsonContainsAndLength(Collection<Long> collection) {
        StringBuilder paramsStr=new StringBuilder();
        int i=0;
        paramsStr.append("JSON_CONTAINS(parent_id_list, JSON_ARRAY(");
        for (Long each:collection) {
            if(i!=0){
                paramsStr.append(",");
            }
            paramsStr.append(each);
            i++;
        }
        paramsStr.append("))");
        paramsStr.append("and JSON_LENGTH(parent_id_list) = ");
        paramsStr.append(collection.size());
        return new RawQueryCondition(paramsStr.toString());
    }

}
