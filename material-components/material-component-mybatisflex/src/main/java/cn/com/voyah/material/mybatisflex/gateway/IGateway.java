package cn.com.voyah.material.mybatisflex.gateway;

import com.mybatisflex.core.exception.FlexExceptions;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.CPI;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface IGateway<T> {
    T save(T entity);

    boolean saveBatch(Collection<T> entities);

    boolean saveBatch(Collection<T> entities, int batchSize);

    boolean saveOrUpdate(T entity);

    boolean saveOrUpdateBatch(Collection<T> entities);

    boolean saveOrUpdateBatch(Collection<T> entities, int batchSize);

    boolean remove(QueryWrapper query);

    default boolean remove(QueryCondition condition) {
        return this.remove(this.query().where(condition));
    }

    boolean removeById(T entity);

    boolean removeById(Serializable id);

    boolean removeByIds(Collection<? extends Serializable> ids);

    default boolean removeByMap(Map<String, Object> query) {
        if (query != null && !query.isEmpty()) {
            return this.remove(this.query().where(query));
        } else {
            throw FlexExceptions.wrap("deleteByMap is not allow empty map.", new Object[0]);
        }
    }

    default boolean updateById(T entity) {
        return this.updateById(entity, true);
    }

    boolean updateById(T entity, boolean ignoreNulls);

    default boolean update(T entity, Map<String, Object> query) {
        return this.update(entity, this.query().where(query));
    }

    boolean update(T entity, QueryWrapper query);

    default boolean update(T entity, QueryCondition condition) {
        return this.update(entity, this.query().where(condition));
    }

    boolean updateBatch(Collection<T> entities);

    boolean updateBatch(Collection<T> entities, boolean ignoreNulls);

    boolean updateBatch(Collection<T> entities, int batchSize);

    boolean updateBatch(Collection<T> entities, int batchSize, boolean ignoreNulls);

    T getById(Serializable id);

    T getOneByEntityId(T entity);

    default Optional<T> getByEntityIdOpt(T entity) {
        return Optional.ofNullable(this.getOneByEntityId(entity));
    }

    default Optional<T> getByIdOpt(Serializable id) {
        return Optional.ofNullable(this.getById(id));
    }

    T getOne(QueryWrapper query);

    default Optional<T> getOneOpt(QueryWrapper query) {
        return Optional.ofNullable(this.getOne(query));
    }

    <R> R getOneAs(QueryWrapper query, Class<R> asType);

    default <R> Optional<R> getOneAsOpt(QueryWrapper query, Class<R> asType) {
        return Optional.ofNullable(this.getOneAs(query, asType));
    }

    default T getOne(QueryCondition condition) {
        return this.getOne(this.query().where(condition).limit(1));
    }

    default Optional<T> getOneOpt(QueryCondition condition) {
        return Optional.ofNullable(this.getOne(condition));
    }

    Object getObj(QueryWrapper query);

    default Optional<Object> getObjOpt(QueryWrapper query) {
        return Optional.ofNullable(this.getObj(query));
    }

    <R> R getObjAs(QueryWrapper query, Class<R> asType);

    default <R> Optional<R> getObjAsOpt(QueryWrapper query, Class<R> asType) {
        return Optional.ofNullable(this.getObjAs(query, asType));
    }

    List<Object> objList(QueryWrapper query);

    <R> List<R> objListAs(QueryWrapper query, Class<R> asType);

    default List<T> list() {
        return this.list(this.query());
    }

    List<T> list(QueryWrapper query);

    default List<T> list(QueryCondition condition) {
        return this.list(this.query().where(condition));
    }

    <R> List<R> listAs(QueryWrapper query, Class<R> asType);

    List<T> listByIds(Collection<? extends Serializable> ids);

    default List<T> listByMap(Map<String, Object> query) {
        return this.list(this.query().where(query));
    }

    default boolean exists(QueryWrapper query) {
        return this.exists(CPI.getWhereQueryCondition(query));
    }

    boolean exists(QueryCondition condition);

    default long count() {
        return this.count(this.query());
    }

    long count(QueryWrapper query);

    default long count(QueryCondition condition) {
        return this.count(this.query().where(condition));
    }

    default Page<T> page(Page<T> page) {
        return this.page(page, this.query());
    }

    Page<T> page(Page<T> page, QueryWrapper query);

    default Page<T> page(Page<T> page, QueryCondition condition) {
        return this.page(page, this.query().where(condition));
    }

    <R> Page<R> pageAs(Page<R> page, QueryWrapper query, Class<R> asType);

    QueryWrapper query();
}
