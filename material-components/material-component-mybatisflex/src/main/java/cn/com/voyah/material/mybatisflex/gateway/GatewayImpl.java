package cn.com.voyah.material.mybatisflex.gateway;

import cn.com.voyah.material.mybatisflex.convert.IConvert;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import com.mybatisflex.core.util.ClassUtil;
import com.mybatisflex.core.util.CollectionUtil;
import com.mybatisflex.core.util.SqlUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.executor.ExecutorException;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

@Setter
@Getter
public class GatewayImpl<T, M extends BaseMapper<P>, P, C extends IConvert<T, P>> implements IGateway<T> {

    @Autowired
    protected M mapper;

    @Autowired
    protected C convert;

    @SuppressWarnings("unchecked")
    private Class<M> getMapperClass() {
        Class<M> aClass = (Class<M>) this.getMapper().getClass();
        return ClassUtil.getUsefulClass(aClass);
    }

    @Override
    public T save(T entity) {
        P o = convert.toConvert(entity);
        int insert = this.getMapper().insert(o, true);
        if (insert > 0) {
            return convert.toConvertT(o);
        } else {
            throw new ExecutorException("save data fail.");
        }
    }

    @Override
    public boolean saveBatch(Collection<T> entities) {
        return this.saveBatch(entities, BaseMapper.DEFAULT_BATCH_SIZE);
    }

    @Override
    public boolean saveBatch(Collection<T> entities, int batchSize) {
        Class<M> usefulClass = getMapperClass();
        List<P> list = convert.toConvert(entities);
        return SqlUtil.toBool(Db.executeBatch(list, batchSize, usefulClass, M::insertSelective));
    }

    @Override
    public boolean saveOrUpdate(T entity) {
        P o = convert.toConvert(entity);
        return SqlUtil.toBool(this.getMapper().insertOrUpdate(o, true));
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<T> entities) {
        return this.saveOrUpdateBatch(entities, BaseMapper.DEFAULT_BATCH_SIZE);
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<T> entities, int batchSize) {
        Class<M> usefulClass = getMapperClass();
        List<P> list = convert.toConvert(entities);
        return SqlUtil.toBool(Db.executeBatch(list, batchSize, usefulClass, M::insertOrUpdateSelective));
    }

    @Override
    public boolean remove(QueryWrapper query) {
        return SqlUtil.toBool(this.getMapper().deleteByQuery(query));
    }

    @Override
    public boolean removeById(T entity) {
        P o = convert.toConvert(entity);
        return SqlUtil.toBool(this.getMapper().delete(o));
    }

    @Override
    public boolean removeById(Serializable id) {
        return SqlUtil.toBool(this.getMapper().deleteById(id));
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> ids) {
        return !CollectionUtil.isEmpty(ids) && SqlUtil.toBool(this.getMapper().deleteBatchByIds(ids));
    }

    @Override
    public boolean updateById(T entity, boolean ignoreNulls) {
        P o = convert.toConvert(entity);
        return SqlUtil.toBool(this.getMapper().update(o, ignoreNulls));
    }

    @Override
    public boolean update(T entity, QueryWrapper query) {
        P o = convert.toConvert(entity);
        return SqlUtil.toBool(this.getMapper().updateByQuery(o, query));
    }

    @Override
    public boolean updateBatch(Collection<T> entities) {
        return this.updateBatch(entities, BaseMapper.DEFAULT_BATCH_SIZE);
    }

    @Override
    public boolean updateBatch(Collection<T> entities, boolean ignoreNulls) {
        return this.updateBatch(entities, BaseMapper.DEFAULT_BATCH_SIZE, ignoreNulls);
    }

    @Override
    public boolean updateBatch(Collection<T> entities, int batchSize) {
        Class<M> usefulClass = getMapperClass();
        List<P> list = convert.toConvert(entities);
        return SqlUtil.toBool(Db.executeBatch(list, batchSize, usefulClass, M::update));
    }

    @Override
    public boolean updateBatch(Collection<T> entities, int batchSize, boolean ignoreNulls) {
        Class<M> usefulClass = getMapperClass();
        List<P> list = convert.toConvert(entities);
        return SqlUtil.toBool(Db.executeBatch(list, batchSize, usefulClass, (mapper, entity) -> mapper.update(entity, ignoreNulls)));

    }

    @Override
    public T getById(Serializable id) {
        P p = this.getMapper().selectOneById(id);
        return convert.toConvertT(p);
    }

    @Override
    public T getOneByEntityId(T entity) {
        P p = convert.toConvert(entity);
        p = this.getMapper().selectOneByEntityId(p);
        return convert.toConvertT(p);
    }

    @Override
    public T getOne(QueryWrapper query) {
        P p = this.getMapper().selectOneByQuery(query);
        return convert.toConvertT(p);
    }

    @Override
    public <R> R getOneAs(QueryWrapper query, Class<R> asType) {
        return this.getMapper().selectOneByQueryAs(query, asType);
    }

    @Override
    public Object getObj(QueryWrapper query) {
        return this.getMapper().selectObjectByQuery(query);
    }

    @Override
    public <R> R getObjAs(QueryWrapper query, Class<R> asType) {
        return this.getMapper().selectObjectByQueryAs(query, asType);
    }

    @Override
    public List<Object> objList(QueryWrapper query) {
        return this.getMapper().selectObjectListByQuery(query);
    }

    @Override
    public <R> List<R> objListAs(QueryWrapper query, Class<R> asType) {
        return this.getMapper().selectObjectListByQueryAs(query, asType);
    }

    @Override
    public List<T> list(QueryWrapper query) {
        List<P> list = this.getMapper().selectListByQuery(query);
        return convert.toConvertT(list);
    }

    @Override
    public <R> List<R> listAs(QueryWrapper query, Class<R> asType) {
        return this.getMapper().selectListByQueryAs(query, asType);
    }

    @Override
    public List<T> listByIds(Collection<? extends Serializable> ids) {
        List<P> list = this.getMapper().selectListByIds(ids);
        return convert.toConvertT(list);
    }

    @Override
    public boolean exists(QueryCondition condition) {
        QueryWrapper queryWrapper = QueryMethods.selectOne().where(condition).limit(1);
        List<Object> objects = this.getMapper().selectObjectListByQuery(queryWrapper);
        return CollectionUtil.isNotEmpty(objects);
    }

    @Override
    public long count(QueryWrapper query) {
        return this.getMapper().selectCountByQuery(query);
    }

    @Override
    public Page<T> page(Page<T> page, QueryWrapper query) {
        Page<P> tPage = this.pageAs(page.map(o -> null), query, null);
        return tPage.map(p -> convert.toConvertT(p));
    }

    @Override
    public <R> Page<R> pageAs(Page<R> page, QueryWrapper query, Class<R> asType) {
        return this.getMapper().paginateAs(page, query, asType);
    }

    @Override
    public QueryWrapper query() {
        return QueryWrapper.create();
    }
}
