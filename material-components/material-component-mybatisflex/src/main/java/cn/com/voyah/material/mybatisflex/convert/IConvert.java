package cn.com.voyah.material.mybatisflex.convert;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public interface IConvert<T, P> {

    T toConvertT(P source);

    default List<T> toConvertT(Collection<P> list) {
        return list.stream().map(this::toConvertT).toList();
    }

    default List<P> toConvert(Collection<T> list) {
        return list.stream().map(this::toConvert).toList();
    }

    P toConvert(T source);

}
