package cn.com.voyah.material.mybatisflex.handle;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.handler.BaseJsonTypeHandler;
import java.util.List;

public class LongListTypeHandler extends BaseJsonTypeHandler<Object> {

    protected List<Long> parseJson(String json) {
        return ObjectUtil.isEmpty(json)?List.of():JSONUtil.toList(json,Long.class);
    }

    protected String toJson(Object object) {
        return JSONUtil.toJsonStr(object);
    }

}
