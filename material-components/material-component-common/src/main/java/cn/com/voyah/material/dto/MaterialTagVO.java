package cn.com.voyah.material.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "标签对象")
public class MaterialTagVO {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long categoryId;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialTagId;
    private List<TagInfoDTO> tagList;
}
