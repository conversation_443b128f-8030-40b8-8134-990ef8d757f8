package cn.com.voyah.material.exception;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * Base Exception is the parent of all exceptions
 *
 * <AUTHOR> 2017年10月22日 上午12:00:39
 */
@Setter
@Getter
public abstract class BaseException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 1L;

    private int errCode;

    public BaseException(String errMessage) {
        super(errMessage);
    }

    public BaseException(int errCode, String errMessage) {
        super(errMessage);
        this.errCode = errCode;
    }

    public BaseException(String errMessage, Throwable e) {
        super(errMessage, e);
    }

    public BaseException(int errCode, String errMessage, Throwable e) {
        super(errMessage, e);
        this.errCode = errCode;
    }

}
