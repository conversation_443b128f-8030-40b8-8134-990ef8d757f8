package cn.com.voyah.material.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Collection;

/**
 * Response with batch record to return,
 * usually use in conditional query
 * <p/>
 * Created by <PERSON><PERSON> on 2017/11/1.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MultiResponse<T> extends SingleResponse<Collection<T>> {

    @Serial
    private static final long serialVersionUID = 1L;

    public static MultiResponse<?> buildSuccess() {
        MultiResponse<?> response = new MultiResponse<>();
        response.success();
        return response;
    }

    public static MultiResponse<?> buildFailure(int errCode, String errMessage) {
        MultiResponse<?> response = new MultiResponse<>();
        response.setCode(errCode);
        response.setMsg(errMessage);
        return response;
    }

    public static <T> MultiResponse<T> of(Collection<T> data) {
        MultiResponse<T> response = new MultiResponse<>();
        response.success();
        response.setData(data);
        return response;
    }

}
