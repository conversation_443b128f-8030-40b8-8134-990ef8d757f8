package cn.com.voyah.material.dto;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.handler.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "素材")
public class OriginMaterialDTO  extends BaseDTO{

    private static final long serialVersionUID = 1L;

    /**
     * 素材ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "素材ID")
    private Long id;

    /**
     * 素材名称
     */
    @Schema(description = "素材展示名称")
    private String materialName;

    /**
     * 素材库id 1：产品素材 2：传播素材
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "素材类型 1：产品素材 2：传播素材")
    private Long libraryId;

    /**
     * 是否公开 0：否  1：是
     */
    @Schema(description = "是否公开 0：否  1：是 2部分公开")
    private Integer isPublic;



    /**
     * 是否是目录 false：否  true：是
     */
    @Schema(description = "是否是目录 false：否  true：是")
    private Boolean isDir;

    /**
     * 素材层级 0级为素材库
     */
    @Schema(description = "素材层级 0级为素材库")
    private Integer level;

    /**
     * 是否显示
     */
    @Schema(description = "是否显示")
    private Integer isShow;

    /**
     * 层级父级ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "层级父级ID")
    private Long parentId;

    /**
     * 目录及文件大小 Byte
     */
    @Schema(description = "目录及文件大小 Byte")
    private Long fileSize;

    /**
     * 文件类型 如；dir、pdf、img
     */
    @Schema(description = "文件类型 如；dir、pdf、img")
    private String fileType;

    /**
     * 文件本身的id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "文件本身的id")
    private Long fileId;

    /**
     * 最后上传人
     */
    @Schema(description = "最后上传人")
    private String lastUploadUser;

    /**
     * 最后上传时间
     */
    @Schema(description = "最后上传时间")
    private LocalDateTime lastUploadTime;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "层级父级ID")
    @Column(value="parent_id_list",typeHandler = JacksonTypeHandler.class)
    private List<Long> parentIdList;

    private Long fileCount;

    /**
     * 来源 1或Null：手动新增 2：文件夹上传
     */
    @Schema(description = "来源 1或Null：手动新增 2：文件夹上传")
    private Integer source;

    private Integer tagStatus;
}
