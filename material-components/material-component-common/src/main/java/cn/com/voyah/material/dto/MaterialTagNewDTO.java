package cn.com.voyah.material.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "素材标注")
public class MaterialTagNewDTO{
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long categoryId;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long materialId;

    @Schema(description = "")
    private List<Long> tagIdList;
}
