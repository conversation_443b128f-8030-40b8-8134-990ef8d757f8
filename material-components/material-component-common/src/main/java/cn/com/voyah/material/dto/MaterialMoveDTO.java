package cn.com.voyah.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "移动素材")
public class MaterialMoveDTO {
    @Schema(description = "被移动的素材ID(数组)")
    private Long sourceId;
    @Schema(description = "被移动的素材夫节点")
    private Long sourceParentId;
    @Schema(description = "是否文件夹 false：否  true：是")
    private Boolean sourceIsDir;
    @Schema(description = "目标素材ID")
    private Long toMaterialId;
}
