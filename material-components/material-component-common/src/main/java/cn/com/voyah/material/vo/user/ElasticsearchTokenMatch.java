//package cn.com.voyah.material.vo.user;
//
//import org.apache.http.HttpHost;
//import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
//import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
//import org.elasticsearch.action.index.IndexRequest;
//import org.elasticsearch.action.index.IndexResponse;
//import org.elasticsearch.action.search.SearchRequest;
//import org.elasticsearch.action.search.SearchResponse;
//import org.elasticsearch.client.RequestOptions;
//import org.elasticsearch.client.RestClient;
//import org.elasticsearch.client.RestHighLevelClient;
//import org.elasticsearch.common.settings.Settings;
//import org.elasticsearch.common.xcontent.XContentBuilder;
//import org.elasticsearch.common.xcontent.XContentFactory;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.elasticsearch.search.SearchHit;
//import org.elasticsearch.search.builder.SearchSourceBuilder;
//import org.elasticsearch.search.sort.SortOrder;
//import java.io.IOException;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 功能说明：
// * 1.创建/连接ES索引：
// *
// * 1.1使用RestHighLevelClient连接到Elasticsearch
// * 1.2检查索引是否存在，不存在则创建新索引
// * 1.3配置索引使用IK分词器进行中文分词
// *
// * 2.导入分词：
// *
// * 2.1将"岚图，机器岚，性格，活泼，可爱"作为分词导入到ES中
// * 2.2每条记录包含分词内容和创建时间
// *
// * 3.插入示例数据：
// *
// * 3.1将"你是岚图机器岚，性格活泼可爱，乐于助"作为内容插入到ES中
// *
// * 4.搜索与排序功能：
// *
// * 4.1按分词关键词进行搜索（多字段匹配content和tokens）
// * 4.2按创建时间（createTime）降序排序结果
// * 4.3支持分页查询
// */
//
//public class ElasticsearchTokenMatch {
//
//    private static final String INDEX_NAME = "lantu_index";
//    private static final String TYPE_NAME = "_doc";
//
//    public static void main(String[] args) throws IOException {
//        // 创建ES客户端连接
//        RestHighLevelClient client = new RestHighLevelClient(
//                RestClient.builder(new HttpHost("localhost", 9200, "http")));
//
//        try {
//            // 检查索引是否存在，如果不存在则创建
//            if (!indexExists(client, INDEX_NAME)) {
//                createIndex(client, INDEX_NAME);
//            }
//
//            // 导入分词数据
//            insertTokens(client, "岚图，机器岚，性格，活泼，可爱");
//
//            // 插入一条示例数据
//            insertSampleData(client, "你是岚图机器岚，性格活泼可爱，乐于助");
//
//            // 按照分词搜索，并按时间排序
//            searchByTokensWithTimeSort(client, "岚图");
//            searchByTokensWithTimeSort(client, "机器岚");
//            searchByTokensWithTimeSort(client, "性格");
//            searchByTokensWithTimeSort(client, "活泼");
//            searchByTokensWithTimeSort(client, "可爱");
//
//        } finally {
//            // 关闭客户端连接
//            client.close();
//        }
//    }
//
//    // 检查索引是否存在
//    private static boolean indexExists(RestHighLevelClient client, String indexName) {
//        try {
//            return client.indices().exists(new org.elasticsearch.client.indices.GetIndexRequest(indexName), RequestOptions.DEFAULT);
//        } catch (IOException e) {
//            e.printStackTrace();
//            return false;
//        }
//    }
//
//    // 创建索引
//    private static void createIndex(RestHighLevelClient client, String indexName) throws IOException {
//        CreateIndexRequest request = new CreateIndexRequest(indexName);
//
//        // 设置索引配置
//        request.settings(Settings.builder()
//                .put("index.number_of_shards", 1)
//                .put("index.number_of_replicas", 1)
//                .put("analysis.analyzer.ik_smart_analyzer.type", "custom")
//                .put("analysis.analyzer.ik_smart_analyzer.tokenizer", "ik_smart")
//        );
//
//        // 设置映射
//        XContentBuilder builder = XContentFactory.jsonBuilder();
//        builder.startObject();
//        {
//            builder.startObject("properties");
//            {
//                // 内容字段，使用IK分词器
//                builder.startObject("content");
//                {
//                    builder.field("type", "text");
//                    builder.field("analyzer", "ik_smart");
//                    builder.field("search_analyzer", "ik_smart");
//                }
//                builder.endObject();
//
//                // 分词字段，使用IK分词器
//                builder.startObject("tokens");
//                {
//                    builder.field("type", "text");
//                    builder.field("analyzer", "ik_smart");
//                    builder.field("search_analyzer", "ik_smart");
//                }
//                builder.endObject();
//
//                // 创建时间字段
//                builder.startObject("createTime");
//                {
//                    builder.field("type", "date");
//                    builder.field("format", "strict_date_optional_time||epoch_millis");
//                }
//                builder.endObject();
//            }
//            builder.endObject();
//        }
//        builder.endObject();
//        request.mapping(builder);
//
//        // 发送创建索引请求
//        CreateIndexResponse createIndexResponse = client.indices().create(request, RequestOptions.DEFAULT);
//        System.out.println("索引创建状态: " + createIndexResponse.isAcknowledged());
//    }
//
//    // 插入分词
//    private static void insertTokens(RestHighLevelClient client, String tokensStr) throws IOException {
//        // 处理分词字符串，去掉逗号
//        String[] tokens = tokensStr.split("，");
//
//        // 构建索引请求
//        IndexRequest indexRequest = new IndexRequest(INDEX_NAME);
//        Map<String, Object> jsonMap = new HashMap<>();
//        jsonMap.put("tokens", tokensStr);
//        jsonMap.put("createTime", new Date());
//
//        indexRequest.source(jsonMap);
//
//        // 执行索引请求
//        IndexResponse indexResponse = client.index(indexRequest, RequestOptions.DEFAULT);
//        System.out.println("分词导入结果: " + indexResponse.getResult());
//    }
//
//    // 插入示例数据
//    private static void insertSampleData(RestHighLevelClient client, String content) throws IOException {
//        // 构建索引请求
//        IndexRequest indexRequest = new IndexRequest(INDEX_NAME);
//        Map<String, Object> jsonMap = new HashMap<>();
//        jsonMap.put("content", content);
//        jsonMap.put("createTime", new Date());
//
//        indexRequest.source(jsonMap);
//
//        // 执行索引请求
//        IndexResponse indexResponse = client.index(indexRequest, RequestOptions.DEFAULT);
//        System.out.println("示例数据导入结果: " + indexResponse.getResult());
//    }
//
//    // 按分词搜索并按时间排序
//    private static void searchByTokensWithTimeSort(RestHighLevelClient client, String keyword) throws IOException {
//        // 创建搜索请求
//        SearchRequest searchRequest = new SearchRequest(INDEX_NAME);
//        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
//
//        // 设置查询条件: 搜索内容字段或分词字段
//        sourceBuilder.query(
//                QueryBuilders.multiMatchQuery(keyword, "content", "tokens")
//        );
//
//        // 设置按创建时间排序
//        sourceBuilder.sort("createTime", SortOrder.DESC);
//
//        // 设置分页
//        sourceBuilder.from(0);
//        sourceBuilder.size(10);
//
//        searchRequest.source(sourceBuilder);
//
//        // 执行搜索
//        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
//
//        // 处理搜索结果
//        System.out.println("===== 搜索关键词: " + keyword + " =====");
//        System.out.println("总匹配数: " + searchResponse.getHits().getTotalHits().value);
//
//        for (SearchHit hit : searchResponse.getHits().getHits()) {
//            System.out.println("ID: " + hit.getId() + ", 分数: " + hit.getScore());
//            System.out.println("内容: " + hit.getSourceAsMap());
//            System.out.println("----------------------------------------");
//        }
//    }
//}