package cn.com.voyah.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.lang.NonNull;

import java.util.List;

@Data
@Schema(description = "标签对象 更新显隐")
public class UpdateTagInfoHiddenDTO {
    @Schema(description = "主标签id")
    private Long categoryId;
    @Schema(description = "标签id")
    private Long tagInfoId;
    @Schema(description = "控制显隐  false  显示  true 隐藏")
    private boolean hidden;
}
