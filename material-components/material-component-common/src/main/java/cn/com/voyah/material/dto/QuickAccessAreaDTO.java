package cn.com.voyah.material.dto;

import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.handler.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class QuickAccessAreaDTO  extends BaseDTO{
    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long id;
    @Column(value="tags",typeHandler = JacksonTypeHandler.class)
    @Schema(description = "标签列表")
    private List<TagInfoMergeVO> tags;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long fileId;

    @Schema(description = "")
    private String name;

    @Schema(description = "")
    private String remark;

    @Schema(description = "")
    private Long sort;

    @Schema(description = "")
    private LocalDateTime startTime;

    @Schema(description = "")
    private LocalDateTime endTime;

    /**
     * 位置
     */
    @Schema(description = "位置")
    private Integer position;


    @Schema(description = "上架状态 0下架 1 上架")
    private Integer status;
    /**
     * 文件路径
     */
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String filename;
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String thUrl;

    @Schema(description = "文件在对应的存储平台对应缩略图的Key")
    private String thFilename;

    @Schema(description = "文件在对应的存储平台对应的Key")
    private String url;

    @Schema(description = "MIME类型")
    private String contentType;
    private Long size;

    @Schema(description = "文件类型")
    private String fileType;


    @Schema(description = "背景图ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long backgroundFileId;

    @Schema(description = "背景图Url")
    @JsonSerialize(using = ToStringSerializer.class)
    private String backgroundUrl;
    @Schema(description = "背景图文件名称")
    private String backgroundFilename;

}



