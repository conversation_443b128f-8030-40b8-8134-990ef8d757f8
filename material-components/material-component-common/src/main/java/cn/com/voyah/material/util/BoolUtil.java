package cn.com.voyah.material.util;

/**
 * Boolean工具类
 *
 * <AUTHOR>
 */
public class BoolUtil {
    private BoolUtil() {
    }

    /**
     * Boolean 转Int
     * @param bool 布尔值
     * @return 1：真 0：假
     */
    public static int toInt(Boolean bool) {
        return (bool == null || !bool) ? 0 : 1;
    }

    /**
     * Int转Boolean
     * @param b 1：真 0：假
     * @return 布尔值
     */
    public static boolean toBoolean(Integer b){
        return b != null && b != 0;
    }
}
