package cn.com.voyah.material.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TagInfoBaseDTO {

    /**
     * 字典主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "字典主键")
    private Long id;

    /**
     * 字典名称
     */
    @Schema(description = "字典名称")
    private String name;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;

    /**
     * 字典描述
     */
    @Schema(description = "字典描述")
    private String tagValue;

    /**
     * 类目id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "类目id")
    private Long categoryId;

    /**
     * 父级id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "父级id")
    private Long parentId;

    /**
     * 层级
     */
    @Schema(description = "层级")
    private Integer level;


    @Schema(description = "0 显示 1 隐藏")
    private boolean hidden=false;


    @Schema(description = "0 可见 1 部分可见  2 隐藏")
    private Integer hiddenStatus;

}
