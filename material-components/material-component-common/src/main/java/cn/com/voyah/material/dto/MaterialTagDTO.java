package cn.com.voyah.material.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "素材标注")
public class MaterialTagDTO extends BaseDTO{
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long id;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long materialId;

    @Schema(description = "")
    private String remark;

    /**
     * 用户浏览量
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userViewCount;
    /**
     * 用户收藏量
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userFavoriteCount;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userDownloadCount;
}
