//package cn.com.voyah.material.util;
//import lombok.extern.slf4j.Slf4j;
//import org.bytedeco.javacv.FFmpegFrameGrabber;
//import org.bytedeco.javacv.Frame;
//import org.bytedeco.javacv.Java2DFrameConverter;
//import javax.imageio.ImageIO;
//import java.awt.image.BufferedImage;
//import java.awt.image.RenderedImage;
//import java.io.File;
//@Slf4j
//public class VideoUtils {
//    public static void convertFirstFrame(String videoPath,String outputPath) throws Exception {
////        String videoPath = "path/to/your/video.mp4"; // 视频文件路径
////        String outputPath = "path/to/output/first_frame.jpg"; // 输出图片路径
//
////        org.bytedeco.javacpp.avutil
//        FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoPath);
//        grabber.start();
//        // 获取第一帧
//        Frame firstFrame = grabber.grabFrame();
//        if (firstFrame != null) {
//            // 将帧转换为BufferedImage
//            Java2DFrameConverter converter = new Java2DFrameConverter();
//            BufferedImage image = converter.convert(firstFrame);
//
//            // 保存图片到文件
//            File outputFile = new File(outputPath);
//            ImageIO.write(image, "jpg", outputFile);
//        }
//        grabber.stop();
//    }
//
//    public static void convertFirstFrame2(String videoPath,String outputPath) throws Exception {
//        File targetFile = new File(outputPath);
//        if (!targetFile.getParentFile().exists()) {
//            targetFile.getParentFile().mkdirs();
//        }
//        File file2 = new File(videoPath);
//        if (file2.exists()) {
//            log.info("文件存在，路径正确！");
//            FFmpegFrameGrabber ff = new FFmpegFrameGrabber(file2);
//            ff.start();
//            Long duration = ff.getLengthInTime() / (1000 * 1000); //获取视频时长
//            System.out.println(duration);
//            int ftp = ff.getLengthInFrames();
//            int flag=0;
//            Frame frame = null;
//            while (flag <= ftp) {
//                //获取帧
//                frame = ff.grabImage();
//                //过滤前3帧，避免出现全黑图片
//                if ((flag>3)&&(frame != null)) {
//                    break;
//                }
//                flag++;
//            }
//            ImageIO.write(frameToBufferedImage(frame), "jpg", targetFile);
//            ff.close();
//            ff.stop();
//        }
//    }
//    private static RenderedImage frameToBufferedImage(Frame frame) {
//        //创建BufferedImage对象
//        Java2DFrameConverter converter = new Java2DFrameConverter();
//        BufferedImage bufferedImage = converter.getBufferedImage(frame);
//        return bufferedImage;
//    }
//
//}