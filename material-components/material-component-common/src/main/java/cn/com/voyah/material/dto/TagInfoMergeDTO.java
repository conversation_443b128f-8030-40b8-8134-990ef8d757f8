package cn.com.voyah.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class TagInfoMergeDTO {
//    @JsonSerialize(using = ToStringSerializer.class)
//    @Schema(description = "字典名称")
//    private Long categoryId;
    /**
     * 字典名称
     */
    @Schema(description = "字典名称")
    private String tagName;
    private Integer level;
    /**
     * 字典名称
     */
    @Schema(description = "字典名称")
    private List<TagInfoBaseDTO> tagList;
}
