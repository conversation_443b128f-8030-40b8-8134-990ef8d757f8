package cn.com.voyah.material.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Set;

@Data
@Schema(description = "标签对象")
public class MaterialTagHeading3VO implements Comparable<MaterialTagHeading3VO>{
    @JsonSerialize(using = ToStringSerializer.class)
    private Long categoryId;
    @JsonSerialize(using = ToStringSerializer.class)
    private String name;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sort;

    private Set<MaterialTagHeadingVO> list;
    @Override
    public int compareTo(MaterialTagHeading3VO a) {
        if(this.sort!=a.getSort()){
            return this.sort.compareTo(a.getSort());
        }
        return this.getCategoryId().compareTo(a.getCategoryId());
    }
}
