package cn.com.voyah.material.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ClientInfoDTO{

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "桌面客户端名称")
    private String name;

    @Schema(description = "当前版本")
    private String currentVersion;

    @Schema(description = "类型 1.为window 2为mac")
    private Integer type;

    @Schema(description = " 1 为激活")
    private Integer active;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "文件id")
    private Long fileId;

    /**
     * 目录及文件大小 Byte
     */
    @Schema(description = "目录及文件大小 Byte")
    private Long fileSize;

    /**
     * 文件类型 如；dir、pdf、img
     */
    @Schema(description = "文件类型 如；dir、pdf、img")
    private String fileType;
    /**
     * 文件路径
     */
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String filename;

    @Schema(description = "文件在对应的存储平台对应缩略图的Key")
    private String thFilename;
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String thUrl;
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String url;
}
