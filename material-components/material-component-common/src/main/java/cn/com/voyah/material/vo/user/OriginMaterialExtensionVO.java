package cn.com.voyah.material.vo.user;

import cn.com.voyah.material.dto.OriginMaterialExtensionDTO;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
public class OriginMaterialExtensionVO extends OriginMaterialExtensionDTO {
    private List<TagInfoMergeVO> tags;
    private String tagRemark;

    /**
     * 用户浏览量
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userViewCount;
    /**
     * 用户收藏量
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userFavoriteCount;
    @Schema(description = "素材类型名称")
    private String labName;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userDownloadCount;
    @Schema(description = "当前用户是否已收藏")
    private boolean haveFavorite=false;
}
