package cn.com.voyah.material.vo.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OriginMaterialInfoV2VO implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "素材ID")
    private Long id;

    /**
     * 素材名称
     */
    @Schema(description = "素材展示名称")
    private String materialName;


    /**
     * 用户浏览量
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "用户浏览量")
    private Long userViewCount;

    @Schema(description = "当前用户是否已收藏")
    private boolean haveFavorite = false;

    @Schema(description = "文件类型 如；dir、pdf、img")
    private String fileType;

    @Schema(description = "文件在对应的存储平台对应缩略图的Key")
    private String thFilename;
    @Schema(description = "文件在对应的存储平台对应的Key(略缩图)")
    private String thUrl;

    @Schema(description = "文件在对应的存储平台对应的Key")
    private String url;

    @Schema(description = "标签绑定")
    private String tagValue;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "目录及文件大小 Byte")
//    @JsonSerialize(using = ToStringSerializer.class)
    private Long fileSize;

}
