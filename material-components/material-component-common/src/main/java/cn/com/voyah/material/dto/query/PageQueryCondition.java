package cn.com.voyah.material.dto.query;

import cn.com.voyah.material.dto.PageQuery;
import cn.com.voyah.material.dto.TimeQueryInterval;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
public class PageQueryCondition<T> extends PageQuery {
    private T condition;
    @Schema(description = "添加开始时间")
    private LocalDateTime createStartTime;
    //后来改为创建开始时间
    @Schema(description = "添加结束时间")
    private LocalDateTime createEndTime;

    private String userNameOrShortId;


}
