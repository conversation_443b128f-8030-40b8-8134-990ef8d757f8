package cn.com.voyah.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BaseDTO {

    @Schema(description = "")
    private String createBy;
    @Schema(description = "")
    private LocalDateTime createTime;

    @Schema(description = "")
    private String updateBy;
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Schema(description = "")
    private Long version;

    /**
     * 创建人短ID
     */
    private String createShortId;


    /**
     * 修改人短ID
     */
    private String updateShortId;


    @Schema(description = "")
    private Boolean isDelete;
}
