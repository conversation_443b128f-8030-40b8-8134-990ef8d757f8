package cn.com.voyah.material.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "标注类目")
public class TagCategoryDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long id;

    @Schema(description = "")
    private String name;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Long sort;

    @Schema(description = "")
    private String desc;

    /**
     * 状态 0：停用  1：启用
     */
    @Schema(description = "状态 0：停用  1：启用")
    private Integer status;

    @Schema(description = "状态 0：非必填  1：必填")
    private Integer required;

    private List<TagInfoDTO> tagList;
}
