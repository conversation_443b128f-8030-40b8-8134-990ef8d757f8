package cn.com.voyah.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperationLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "")
    private Long id;

    @Schema(description = "")
    private String shortId;

    @Schema(description = "")
    private String userName;

    @Schema(description = "")
    private Integer state;

    @Schema(description = "")
    private String interfaceRemark;

    @Schema(description = "")
    private LocalDateTime operationTime;

    @Schema(description = "")
    private String ip;

    @Schema(description = "")
    private String path;

    @Schema(description = "")
    private String method;

    @Schema(description = "")
    private String requestParams;

    @Schema(description = "")
    private String response;

    @Schema(description = "")
    private String errorMsg;

    @Schema(description = "")
    private Long timeInterval;

    @Schema(description = "")
    private LocalDateTime requestTime;

    @Schema(description = "")
    private LocalDateTime responseTime;
}
