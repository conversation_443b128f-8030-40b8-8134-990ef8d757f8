package cn.com.voyah.material.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "金刚区 统计、背景图")
public class QuickAccessAreaMetaDataVO {
    @Schema(description = "名称")
    private String title;

    @Schema(description = "统计")
    private String count;


    @Schema(description = "背景图ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long backgroundFileId;

    @Schema(description = "背景图Url")
    @JsonSerialize(using = ToStringSerializer.class)
    private String backgroundUrl;
}
