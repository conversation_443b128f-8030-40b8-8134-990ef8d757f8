package cn.com.voyah.material.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Schema(description = "标签对象")
public class MaterialTagHeadingVO2 implements Comparable<MaterialTagHeadingVO2>{
    @JsonSerialize(using = ToStringSerializer.class)
    private Long categoryId;
    @JsonSerialize(using = ToStringSerializer.class)
    private String tagName;
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer level;
    @JsonSerialize(using = ToStringSerializer.class)
    private Map<String,List<Long>> tagValueMap;

    @Override
    public int compareTo(MaterialTagHeadingVO2 a) {
        if(this.level!=a.getLevel()){
            return this.level-a.getLevel();
        }
        return this.getTagName().compareTo(a.getTagName());
    }
}
