package cn.com.voyah.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "素材")
@EqualsAndHashCode(callSuper = true)
public class OriginMaterialExtensionDTO extends OriginMaterialDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 存储平台
     */
    @Schema(description = "存储平台 1.腾讯云 2.阿里云")
    private String platform;

    /**
     * 基础存储路径
     */
    @Schema(description = "文件在对应的存储平台存储路径")
    private String basePath;

    /**
     * 文件路径
     */
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String filename;

    @Schema(description = "文件在对应的存储平台对应缩略图的Key")
    private String thFilename;
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String thUrl;
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String url;

    //文件名/创建人或者工号queryParam1
    private String queryParam1;

    /**
     * 分片上传ID，仅在手动分片上传时使用
     */
    private String uploadId;
    /**
     * 上传状态，仅在手动分片上传时使用，1：初始化完成，2：上传完成
     */
    private Integer uploadStatus;

    @Schema(description = "预签名url")
    private String signedUrl;

    private String hashInfo;

    private String originalFilename;


    @Schema(description = "层级父级名称")
    private List<String> parentNameList;
    @Schema(description = "")
    private LocalDateTime createTime;

    @Schema(description = "")
    private String updateBy;
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Schema(description = "")
    private Long version;

    /**
     *标注创建人
     */
    @Schema(description = "标注创建人")
    private String tagCreateBy;
    /**
     *
     */
    @Schema(description = "标注人短ID")
    private String tagCreateShortId;
    /**
     * 标注时间
     */
    @Schema(description = "标注时间")
    private LocalDateTime tagCreateTime;


    private String thContentType;



}
