package cn.com.voyah.material.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
@Data
public class OperationLogExportQuery {

    @Schema(description = "添加开始时间")
    private LocalDateTime createStartTime;
    //后来改为创建开始时间
    @Schema(description = "添加结束时间")
    private LocalDateTime createEndTime;

    private String userNameOrShortId;

    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long id;

    @Schema(description = "")
    private String shortId;

    @Schema(description = "")
    private String userName;

    @Schema(description = " 0 成功 1失败")
    private Integer state;

    @Schema(description = "")
    private String interfaceRemark;

    @Schema(description = "")
    private LocalDateTime operationTime;

    @Schema(description = "")
    private String ip;

    @Schema(description = "")
    private String path;

    @Schema(description = "")
    private String method;

    @Schema(description = "")
    private String requestParams;

    @Schema(description = "")
    private String response;

    @Schema(description = "")
    private String errorMsg;

    @Schema(description = "")
    private Long timeInterval;

    @Schema(description = "")
    private LocalDateTime requestTime;

    @Schema(description = "")
    private LocalDateTime responseTime;
    private Integer limit;
}
