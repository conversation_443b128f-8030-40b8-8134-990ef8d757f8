package cn.com.voyah.material.dto;

import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.core.handler.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
@Schema(description = "全局搜索条件对象")
public class GlobalOriginMaterialQuery {

    @Schema(description = "主文件夹ID")
    private Long homeFolderId;

    @Schema(description = "文件资源类型 传值如picture、video、ppt、excel、word、pdf")
    private List<String> fileSourceTypes;

    /**
     * 文件名/创建人或者工号queryParam1
     */
    @Schema(description = "文件名/创建人或者工号")
    private String queryParam1;

    //后来改为创建开始时间
    @Schema(description = "修改开始时间")
    private LocalDate updateStartTime;
    //后来改为创建开始时间
    @Schema(description = "修改结束时间")
    private LocalDate updateEndTime;

    @Schema(description = "是否已标注 1 已标注 0 未标注")
    private Integer tagStatus;
    private Boolean isDir;
    private Integer isPublic;
    protected List<Long> authMaterialList;

    private String createShortId;
    @Column(value="tags",typeHandler = JacksonTypeHandler.class)
    @Schema(description = "标签列表")
    private List<TagInfoMergeVO> tags;

    @Schema(description = "文件类型")
    protected List<String> fileTypes;

    @Schema(description = "文件类型 数组")
    protected List<TagInfoMergeVO> fileTypeList;

    private String tagCreateQueryParam;

    private DateQueryInterval tagCreateTimeInterval;


}
