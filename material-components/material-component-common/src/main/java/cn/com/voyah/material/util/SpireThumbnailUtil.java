package cn.com.voyah.material.util;
import com.spire.doc.Document;
import com.spire.doc.documents.ImageType;
import com.spire.pdf.PdfDocument;
import com.spire.pdf.graphics.PdfImageType;
import com.spire.presentation.FileFormat;
import com.spire.presentation.Presentation;
//import com.spire.xls.Workbook;
import lombok.SneakyThrows;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.Assert;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class SpireThumbnailUtil {

    // 定义日期时间格式化字符串，用于生成时间戳部分的文件名
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");



    @SneakyThrows
    public static boolean wordToImage(File wordFile,String imgUrl) {
        Document word = new Document(wordFile.getAbsolutePath());
        System.out.println("word.getPageCount() = " + word.getBuiltinDocumentProperties().getPageCount());

        BufferedImage image = word.saveToImages(0, ImageType.Bitmap);
        File file = new File(imgUrl);
        ImageIO.write(image, "PNG", file);
        System.out.println("word imgUrl：" + imgUrl);
        return true;
    }

    @SneakyThrows
    public static boolean pdfToImage(File pdfFile,String imgUrl) {
        Locale newLocale = Locale.ROOT;
        Locale.setDefault(newLocale);
        PdfDocument pdf = new PdfDocument(pdfFile.getAbsolutePath());
        BufferedImage image = pdf.saveAsImage(0, PdfImageType.Bitmap);
        File file = new File(imgUrl);
        ImageIO.write(image, "PNG", file);
        return true;
    }

//    @SneakyThrows
//    public static boolean excelToImage(File excelFile,String imgUrl) {
//        Workbook workbook = new Workbook();
//        workbook.loadFromFile(excelFile.getAbsolutePath());
//        System.out.println("workbook.getWorksheets().getCount() = " + workbook.getWorksheets().getCount());
//
//        BufferedImage image = workbook.saveAsImage(0, 100, 100);
//        int maxWidth = image.getWidth();
//        System.out.println("maxWidth = " + maxWidth);
//        int maxHeight = image.getHeight();
//        System.out.println("maxHeight = " + maxHeight);
//        BufferedImage subimage = image.getSubimage(0, 0, Math.min(maxWidth, 1280), Math.min(maxHeight, 1500));
//        ImageIO.write(subimage, "PNG", new File(imgUrl));
//        workbook.dispose();
//        System.out.println("excel imgUrl：" + imgUrl);
//        return true;
//    }

    @SneakyThrows
    public static boolean pptToImage(File pptFile,String imgUrl) {
        Presentation ppt = new Presentation(pptFile.getAbsolutePath(), FileFormat.AUTO);
        System.out.println("ppt.getSlides().size() = " + ppt.getSlides().size());
        BufferedImage image = ppt.getSlides().get(0).saveAsImage();
        ImageIO.write(image, "PNG", new File(imgUrl));
        ppt.dispose();
        System.out.println("ppt imgUrl：" + imgUrl);
        return true;
    }


    public static boolean generateThumbnail(String filePath,String destFilePath) {
        File file = new File(filePath);
        Assert.isTrue(file.exists(), "文件不存在");
        if (filePath.endsWith("doc") || filePath.endsWith("docx") || filePath.endsWith("txt")) {
            return wordToImage(file,destFilePath);
        } else if (filePath.endsWith("pdf")) {
            return pdfToImage(file,destFilePath);
        } else if (filePath.endsWith("xlsx") || filePath.endsWith("xls")) {
            return ExcelToStyledImageWithBordersAndFormula.excelToImage(file,destFilePath);
        } else if (filePath.endsWith("ppt") || filePath.endsWith("pptx")) {
            return pptToImage(file,destFilePath);
        }
        return false;
    }
    @SneakyThrows
    public static boolean excelToImage(File excelFile,String imgUrl) {
        return true;
    }
    public static void main(String[] args) throws Exception {
//        generateThumbnail("D:\\test\\tmp\\tmp\\1.docx");
//        generateThumbnail("D:\\test\\tmp\\tmp\\1.pdf");
        String destFilePath="D:\\test\\tmp\\tmp\\text.jpg";
//        generateThumbnail("D:\\test\\tmp\\tmp\\1.xlsx",destFilePath);
        generateThumbnail("D:\\test\\tmp\\tmp\\1.txt",destFilePath);
    }

}
