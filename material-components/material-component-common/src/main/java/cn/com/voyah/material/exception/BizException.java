package cn.com.voyah.material.exception;

import java.io.Serial;

/**
 * BizException is known Exception, no need retry
 *
 * <AUTHOR>
 */
public class BizException extends BaseException {

    @Serial
    private static final long serialVersionUID = 1L;

    private static final int DEFAULT_ERR_CODE = 400;

    public BizException(String errMessage) {
        super(DEFAULT_ERR_CODE, errMessage);
    }

    public BizException(int errCode, String errMessage) {
        super(errCode, errMessage);
    }

    public BizException(String errMessage, Throwable e) {
        super(DEFAULT_ERR_CODE, errMessage, e);
    }

    public BizException(int errorCode, String errMessage, Throwable e) {
        super(errorCode, errMessage, e);
    }

}