package cn.com.voyah.material.dto;

import cn.com.voyah.material.dto.query.PageQueryCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
public class TimeQueryInterval {

    @Schema(description = "添加开始时间")
    private LocalDateTime startTime;
    //后来改为创建开始时间
    @Schema(description = "添加结束时间")
    private LocalDateTime endTime;

    public static TimeQueryInterval buildFromPage(PageQueryCondition pageQueryCondition){
        TimeQueryInterval timeQueryInterval=null;
        if (Objects.nonNull(pageQueryCondition)&&Objects.nonNull(pageQueryCondition.getCreateStartTime()) && Objects.nonNull(pageQueryCondition.getCreateEndTime())) {
            timeQueryInterval=new TimeQueryInterval();
            timeQueryInterval.setStartTime(LocalDateTime.of(pageQueryCondition.getCreateStartTime().toLocalDate(),LocalDateTime.MIN.toLocalTime()));
            timeQueryInterval.setEndTime(LocalDateTime.of(pageQueryCondition.getCreateEndTime().toLocalDate(),LocalDateTime.MAX.toLocalTime()));
        }
        return timeQueryInterval;
    }
    public static TimeQueryInterval buildFromMaterialQuery(GlobalOriginMaterialQuery globalOriginMaterialQuery){
        TimeQueryInterval timeQueryInterval=null;
        if (Objects.nonNull(globalOriginMaterialQuery)&&Objects.nonNull(globalOriginMaterialQuery.getUpdateStartTime()) && Objects.nonNull(globalOriginMaterialQuery.getUpdateEndTime())) {
            timeQueryInterval=new TimeQueryInterval();
            timeQueryInterval.setStartTime(LocalDateTime.of(globalOriginMaterialQuery.getUpdateStartTime(),LocalDateTime.MIN.toLocalTime()));
            timeQueryInterval.setEndTime(LocalDateTime.of(globalOriginMaterialQuery.getUpdateEndTime(),LocalDateTime.MAX.toLocalTime()));
        }
        return timeQueryInterval;
    }

    public static TimeQueryInterval buildFromDateQueryInterval(DateQueryInterval dateQueryInterval){
        TimeQueryInterval timeQueryInterval=null;
        if (Objects.nonNull(dateQueryInterval)&&Objects.nonNull(dateQueryInterval.getStartTime()) && Objects.nonNull(dateQueryInterval.getEndTime())) {
            timeQueryInterval=new TimeQueryInterval();
            timeQueryInterval.setStartTime(LocalDateTime.of(dateQueryInterval.getStartTime(),LocalDateTime.MIN.toLocalTime()));
            timeQueryInterval.setEndTime(LocalDateTime.of(dateQueryInterval.getEndTime(),LocalDateTime.MAX.toLocalTime()));
        }
        return timeQueryInterval;
    }



}
