package cn.com.voyah.material.util;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;

import java.util.ArrayList;
import java.util.List;

public class HanLPUtils {
    public static void main(String[] args) {
        // 输入句子
        String sentence = "我是一个梦想家，20-21  ,8*22喜欢编程。";

        // 使用 HanLP 进行分词
        List<Term> termList = HanLP.segment(sentence);

        // 输出分词结果
        for (Term term : termList) {
            if (!term.nature.toString().equals("w")&&!isSingleChineseCharacter(term.word)) {  // 过滤掉标点符号
                System.out.println(term.word);
            }
        }
    }

    public static List<String>getKeyWord(String query){
        List<String> res = new ArrayList<>();
        // 使用 HanLP 进行分词
        List<Term> termList = HanLP.segment(query);

        // 输出分词结果
        for (Term term : termList) {
            if (!term.nature.toString().equals("w")&&!isSingleChineseCharacter(term.word)) {  // 过滤掉标点符号
                res.add(term.word);
            }
//            if (!term.nature.toString().equals("w")) {  // 过滤掉标点符号
//                res.add(term.word);
//            }
        }
        return res;
    }


    /**
     * 过滤掉单个中文字符
     *
     * @param input 输入的字符串
     * @return 过滤后的字符串
     */
    public static boolean isSingleChineseCharacter(String input) {
        // 判断字符串是否只有一个字符，并且是中文字符
        if (input.length() == 1 && isChineseCharacter(input.charAt(0))) {
            return true; // 如果是单个中文字符，返回空字符串
        }
        return false; // 否则返回原字符串
    }

    /**
     * 判断一个字符是否是中文字符
     *
     * @param c 需要判断的字符
     * @return 如果是中文字符返回 true，否则返回 false
     */
    public static boolean isChineseCharacter(char c) {
        // 中文字符的 Unicode 范围是 \u4e00 到 \u9fa5
        return c >= '\u4e00' && c <= '\u9fa5';
    }
}