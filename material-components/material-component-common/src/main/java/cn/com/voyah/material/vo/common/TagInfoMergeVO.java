package cn.com.voyah.material.vo.common;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Set;

@Data
public class TagInfoMergeVO {
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "字典名称")
    private Long categoryId;
    /**
     * 字典名称
     */
    @Schema(description = "字典名称")
    private String tagName;

    /**
     * 字典名称
     */
    @Schema(description = "字典名称")
    private Set<String> tagValueList;
}
