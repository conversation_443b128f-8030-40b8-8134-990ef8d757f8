package cn.com.voyah.material.dto;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Response to caller
 *
 * <AUTHOR> 2017年10月21日 下午8:53:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Response extends DTO  {

    private int code;

    private String msg;

    @JsonIgnore
    @Schema(hidden = true)
    public boolean isSuccess() {
        return this.getCode() == getSuccessCode();
    }

    /**
     * 成功Code
     */
    public static int getSuccessCode() {
        return 0;
    }


    public void success() {
        this.setCode(getSuccessCode());
    }
    public Response failure(int errCode, String errMessage) {
        this.setCode(errCode);
        this.setMsg(errMessage);
        return this;
    }
    public static Response buildSuccess() {
        Response response = new Response();
        response.setCode(getSuccessCode());
        return response;
    }


    public static Response buildFailure(int errCode, String errMessage) {
        Response response = new Response();
        response.setCode(errCode);
        response.setMsg(errMessage);
        return response;
    }
}
