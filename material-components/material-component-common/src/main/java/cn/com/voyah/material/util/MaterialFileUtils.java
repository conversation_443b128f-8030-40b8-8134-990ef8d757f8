package cn.com.voyah.material.util;

import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.exception.BizException;
import org.springframework.util.StringUtils;

public class MaterialFileUtils {

    public static String generateThFileName(String fileName,Long timeStamp) {
        if (StringUtils.isEmpty(fileName)) {
            throw new BizException("文件名不能为空");
        }
        int index = fileName.lastIndexOf(CommonConstants.FILE_SEPRATE_SUFFIX);
        String prefix="";
        if (index > 0) {
            prefix = prefix+fileName.substring(0, index);
        } else {
            prefix = prefix+fileName;
        }

        StringBuilder stringBuilder = new StringBuilder(prefix);
        stringBuilder.append(CommonConstants.FILE_DUPLICATION_SEPRATE);
        stringBuilder.append(timeStamp);
        stringBuilder.append(CommonConstants.DEFAULT_TH_SUFFIX);
        return stringBuilder.toString();
    }
}
