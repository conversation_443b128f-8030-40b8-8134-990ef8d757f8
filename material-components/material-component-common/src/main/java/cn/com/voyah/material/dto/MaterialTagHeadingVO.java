package cn.com.voyah.material.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "标签对象")
public class MaterialTagHeadingVO implements Comparable<MaterialTagHeadingVO>{
    @JsonSerialize(using = ToStringSerializer.class)
    private Long categoryId;
    @JsonSerialize(using = ToStringSerializer.class)
    private String tagName;
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer level;
    @JsonSerialize(using = ToStringSerializer.class)
    private List<Long> tagIdlist;
    @JsonSerialize(using = ToStringSerializer.class)
    private List<Long> tagIdStrlist;
//    @JsonSerialize(using = ToStringSerializer.class)
//    private List<List<Long>> groupIds;
    private LocalDateTime createTime;
    @Override
    public int compareTo(MaterialTagHeadingVO a) {
        if(this.level!=a.getLevel()){
            return this.level-a.getLevel();
        }
        return this.getCreateTime().compareTo(a.getCreateTime());
    }
}
