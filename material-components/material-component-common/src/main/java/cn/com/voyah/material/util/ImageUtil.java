//package cn.com.voyah.material.util;
//
//import cn.hutool.core.codec.Base64;
//import lombok.extern.slf4j.Slf4j;
//import net.coobird.thumbnailator.Thumbnails;
//
//import javax.imageio.ImageIO;
//import java.awt.*;
//import java.awt.image.BufferedImage;
//import java.io.*;
//
///**
// * <AUTHOR>
// * @date ：Created in 2021/7/26
// * @description：图片管理工具类
// * @version: 1.0
// */
//@Slf4j
//public class ImageUtil {
//
//    /** 水印字体 */
//    private static final String MARK_FONT_NAME = "DejaVu Sans Mono";
//
//    /** 水印颜色 */
//    private static final Color MARK_FONT_COLOR = Color.RED;
//
//    /** 水印倾斜度 */
//    private static final int MARK_DEGREE = -45;
//
//    /** 水印透明度 */
//    private static final float MARK_ALPHA = 0.5f;
//
//    /** JPEG格式图片 */
//    private static final String PICTURE_TYPE_JPEG = "jpeg";
//
//    /** PNG格式图片 */
//    private static final String PICTURE_TYPE_PNG = "png";
//
//    /** 长宽比 */
//    private static final float ASPECT_RATIO = 0.75f;
//
//    /**
//     * 放大图片
//     */
//    public static byte[] enlarge(byte[] bytes, long minSize) {
//        try(ByteArrayOutputStream out = new ByteArrayOutputStream()) {
//            // 文件大小，单位：字节
//            long fileSize = bytes.length;
//            if(fileSize>=minSize){
//                return bytes;
//            }
//
//            int count = 0;
//            while (fileSize < minSize){
//                count++;
//                log.info("count => {}, fileSize => {}",count,fileSize);
//                BufferedImage image = ImageIO.read(new ByteArrayInputStream(bytes));
//                Thumbnails.of(image).scale(1.2).outputFormat(PICTURE_TYPE_JPEG).toOutputStream(out);
//                out.flush();
//                bytes = out.toByteArray();
//                fileSize = bytes.length;
//            }
//
//            return bytes;
//        } catch (IOException e) {
//            log.warn("enlarge file error", e);
//            return bytes;
//        }
//    }
//
//    /**
//     * 更改图片长宽
//     * @param fileBytes 图片文件字节流
//     * @param widthRatio 宽度比例
//     * @param heightRatio 高度比例
//     * @return 更改后的文件
//     */
//    public static byte[] modifyAspect(byte[] fileBytes, int widthRatio, int heightRatio){
//        try(ByteArrayOutputStream out = new ByteArrayOutputStream()) {
//            /**
//             * size(width,height) 若图片横比200小，高比300小，不变
//             * 若图片横比200小，高比300大，高缩小到300，图片比例不变 若图片横比200大，高比300小，横缩小到200，图片比例不变
//             * 若图片横比200大，高比300大，图片按比例缩小，横为200或高为300<br/>
//             *
//             *  keepAspectRatio(false) 默认是按照比例缩放的
//             */
//            BufferedImage image = ImageIO.read(new ByteArrayInputStream(fileBytes));
//            int width = image.getWidth();
//            int height = image.getHeight();
//            if (width * heightRatio == height * widthRatio) {
//                // 已符合指定长宽比
//                return fileBytes;
//            }
//
//            if (width * heightRatio / widthRatio <= height) {
//                height = width * heightRatio / widthRatio;
//            } else {
//                width = height * widthRatio / heightRatio;
//            }
//            // 长宽必须  长宽比的倍数
//            width = width / widthRatio * widthRatio;
//            height = height / heightRatio * heightRatio;
//
//            Thumbnails.of(image).size(width,height).keepAspectRatio(false).outputFormat("JPEG").toOutputStream(out);
//            out.flush();
//            return out.toByteArray();
//        } catch (IOException e) {
//            log.warn("modify file aspect error", e);
//            return fileBytes;
//        }
//    }
//
//    /**
//     * @Title:compressPhotoByQuality
//     * @Description: 递归压缩到maxSize后返回新的bytes值
//     * @date 2020年6月30日 下午2:24:36
//     * <AUTHOR>
//     * @param bytes 源文件字节
//     * @param maxSize 要求压缩到的最大b
//     * @return
//     * @throws IOException
//     */
//    public static byte[] compress(byte[] bytes, long maxSize) throws IOException {
//        if (bytes == null) {
//            return null;
//        }
//        log.info("start to compress image, size({}b)。", bytes.length);
//        // 满足目标kb值，则返回
//        long fileSize = bytes.length;
//        if (fileSize <= maxSize) {
//            log.info("compress end。", fileSize, maxSize);
//            return bytes;
//        }
//        ByteArrayOutputStream out = new ByteArrayOutputStream();
//        Thumbnails.of(new ByteArrayInputStream(bytes)).scale(0.8).toOutputStream(out);
//
//        //递归
//        return compress(out.toByteArray(), maxSize);
//    }
//
//    /**
//     * 图片加水印
//     * @param fileBase64 图片base64编码
//     * @param mark 水印内容
//     */
//    public static String watermark(String fileBase64, String mark) throws IOException {
//        byte[] bytes = Base64.decode(fileBase64);
//        byte[] watermarkBytes = ImageUtil.watermark(bytes,mark);
//        fileBase64 = Base64.encode(watermarkBytes);
//        return fileBase64;
//    }
//
//    /**
//     * 图片加水印
//     * @param bytes 图片字节
//     * @param mark 水印内容
//     */
//    public static byte[] watermark(byte[] bytes, String mark) throws IOException {
//        // 创建BufferedImage
//        BufferedImage buffImg = ImageIO.read(new ByteArrayInputStream(bytes));
//
//        // 图片尺寸
//        int width = buffImg.getWidth();
//        int height = buffImg.getHeight();
//
//        // 得到画笔对象
//        Graphics2D graphics2D = buffImg.createGraphics ();
//
//        // 设置对线段的锯齿状边缘处理
//        graphics2D.setRenderingHint (RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
//        graphics2D.drawImage(buffImg,null,0,0);
//
//        // 设置水印旋转
//        graphics2D.rotate(Math.toRadians(MARK_DEGREE),(double) width/2, (double)height/2);
//        // 设置水印文字透明度
//        graphics2D.setComposite (AlphaComposite.getInstance (AlphaComposite.SRC_ATOP, MARK_ALPHA));
//        // 设置水印文字颜色
//        graphics2D.setColor(MARK_FONT_COLOR);
//        // 设置水印文字字体
//        int fontSize = height/20;
//        Font font = new Font(MARK_FONT_NAME, Font.PLAIN, fontSize);
//        graphics2D.setFont (font);
//
//        // 字体长度
//        int markWidth = fontSize * getTextLength (mark);
//        // 字体高度
//        int markHeight = fontSize;
//
//        int x = -width / 2;
//        int y = -height / 2;
//        int xMove = width/10 + 10;
//        int yMove = height/10;
//
//        // 循环添加水印
//        while (x < width * 1.5) {
//            y = -height / 2;
//            while (y < height * 1.5) {
//                graphics2D.drawString (mark, x, y);
//                y += markHeight + yMove;
//            }
//            x += markWidth + xMove;
//        }
//
//        // 释放资源
//        graphics2D.dispose ();
//
//        // 生成图片
//        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//        ImageIO.write(buffImg,PICTURE_TYPE_PNG,outputStream);
//        outputStream.close();
//        return outputStream.toByteArray();
//    }
//
//    /**
//     * 获取文本长度。汉字为1:1，英文和数字为2:1
//     */
//    private static int getTextLength (String text) {
//        int length = text.length ();
//        for (int i = 0; i < text.length (); i++) {
//            String s = String.valueOf (text.charAt (i));
//            if (s.getBytes ().length > 1) {
//                length++;
//            }
//        }
//        length = length % 2 == 0 ? length / 2 : length / 2 + 1;
//        return length;
//    }
//
//    /**
//     * 生成单色图片
//     * @param width 宽度
//     * @param height 高度
//     * @param color 颜色
//     * @return 图片字节
//     */
//    private static BufferedImage createBackgroundImg(Integer width, Integer height, Color color) {
//        BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
//
//        // 得到画笔对象
//        Graphics2D graphics2D = bufferedImage.createGraphics ();
//        graphics2D.setColor(color);
//
//        // 逐行画线
//        for(int i=0; i<height; i++){
//            graphics2D.drawLine(0, i, width, i);
//        }
//
//        // 释放资源
//        graphics2D.dispose ();
//
//        return bufferedImage;
//    }
//
//    /**
//     * 图片拼接，待合并的两张图必须满足这样的前提，如果水平方向合并，则高度必须相等；如果是垂直方向合并，宽度必须相等。
//     * @param originalImg 原始图片
//     * @param edgeImg 左右或上下拼接图
//     * @param isHorizontal 拼接方式（true：水平拼接 false:垂直拼接）
//     * @return 拼接后的图片
//     */
//    private static BufferedImage mergeImage(BufferedImage originalImg, BufferedImage edgeImg, boolean isHorizontal){
//        // 原始图片尺寸
//        int originalImgWidth = originalImg.getWidth();
//        int originalImgHeight = originalImg.getHeight();
//
//        // 拼接图片尺寸
//        int edgeImgWidth = edgeImg.getWidth();
//        int edgeImgHeight = edgeImg.getHeight();
//
//        // 新图片尺寸
//        int destImageWidth;
//        int destImageHeight;
//        if(isHorizontal){
//            destImageWidth = originalImgWidth + edgeImgWidth + edgeImgWidth;
//            destImageHeight = originalImgHeight;
//        }else {
//            destImageHeight = originalImgHeight + edgeImgHeight + edgeImgHeight;
//            destImageWidth = originalImgWidth;
//        }
//
//        // 逐行扫描原始图像中各个像素的RGB到数组中
//        int[] originalImageArray = new int[originalImgWidth * originalImgHeight];
//        originalImageArray = originalImg.getRGB(0, 0, originalImgWidth, originalImgHeight, originalImageArray, 0, originalImgWidth);
//
//        // 逐行扫描拼接图像中各个像素的RGB到数组中
//        int[] edgeImageArray = new int[edgeImgWidth * edgeImgHeight];
//        edgeImageArray = edgeImg.getRGB(0, 0, edgeImgWidth, edgeImgHeight, edgeImageArray, 0, edgeImgWidth);
//
//        // 生成新图片
//        BufferedImage destImage = new BufferedImage(destImageWidth, destImageHeight, BufferedImage.TYPE_INT_RGB);
//        if (isHorizontal) {
//            // 合并左边的拼接图片
//            destImage.setRGB(0, 0, edgeImgWidth, edgeImgHeight, edgeImageArray, 0, edgeImgWidth);
//            // 合并中间的原始图片
//            destImage.setRGB(edgeImgWidth, 0, originalImgWidth, originalImgHeight, originalImageArray, 0, originalImgWidth);
//            // 合并右边的拼接图片
//            destImage.setRGB(edgeImgWidth + originalImgWidth, 0, edgeImgWidth, edgeImgHeight, edgeImageArray, 0, edgeImgWidth);
//
//        } else {
//            // 合并上方的拼接图片
//            destImage.setRGB(0, 0, edgeImgWidth, edgeImgHeight, edgeImageArray, 0, edgeImgWidth);
//            // 合并中间的原始图片
//            destImage.setRGB(0, edgeImgHeight, originalImgWidth, originalImgHeight, originalImageArray, 0, originalImgWidth);
//            // 合并下方的拼接图片
//            destImage.setRGB(0, edgeImgHeight + originalImgHeight, edgeImgWidth, edgeImgHeight, edgeImageArray, 0, edgeImgWidth);
//        }
//
//        return destImage;
//    }
//
//    /**
//     * 图片补边
//     * @param bytes 原始图片字节
//     * @param color 补边颜色
//     * @return 补边后的图片字节
//     */
//    public static byte[] fillEdge(byte[] bytes, Color color) throws IOException {
//        // 原始图片
//        BufferedImage originalImg = ImageIO.read(new ByteArrayInputStream(bytes));
//
//        if (originalImg == null) {
//            return bytes;
//        }
//
//        // 图片尺寸
//        int width = originalImg.getWidth();
//        int height = originalImg.getHeight();
//
//        // 白边尺寸
//        int whiteEdgeWidth;
//        int whiteEdgeHeight;
//
//        // 是否水平合并
//        boolean isHorizontal;
//
//        // 计算单个白边长宽及合并方式
//        int maxHeight = (int) (width*ASPECT_RATIO);
//        if(height<maxHeight){
//            whiteEdgeHeight = (maxHeight-height)/2;
//            whiteEdgeWidth = width;
//            isHorizontal = false;
//        }else {
//            int maxWidth = (int) (height/ASPECT_RATIO);
//            whiteEdgeWidth = (maxWidth-width)/2;
//            whiteEdgeHeight = height;
//            isHorizontal = true;
//        }
//
//        // 已经符合要求的图片不进行填充
//        if (whiteEdgeWidth <= 0 || whiteEdgeHeight <= 0) {
//            return bytes;
//        }
//
//        // 创建白边图片
//        BufferedImage whiteEdgeImage = createBackgroundImg(whiteEdgeWidth,whiteEdgeHeight,color);
//
//        // 合并原始图片和白边
//        BufferedImage destImage = mergeImage(originalImg,whiteEdgeImage,isHorizontal);
//
//        // 生成图片
//        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//        ImageIO.write(destImage,PICTURE_TYPE_PNG,outputStream);
//        outputStream.close();
//
//        return outputStream.toByteArray();
//    }
//
//}
