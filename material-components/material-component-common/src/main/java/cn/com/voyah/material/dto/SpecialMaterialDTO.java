package cn.com.voyah.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "素材")
public class SpecialMaterialDTO {

    private String originalFilename;
    @Schema(description = "文件在对应的存储平台存储路径")
    private String basePath;
    /**
     * 文件路径
     */
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String filename;

    @Schema(description = "文件在对应的存储平台对应缩略图的Key")
    private String thFilename;
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String thUrl;
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String url;
    @Schema(description = "1 banner 2 金刚区")
    private Integer specialType;
}
