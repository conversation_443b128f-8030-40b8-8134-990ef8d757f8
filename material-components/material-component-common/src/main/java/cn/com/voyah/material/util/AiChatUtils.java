package cn.com.voyah.material.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Component
public class AiChatUtils {

    //    public static final String HOST = "https://voyahgpt-gateway-test.voyah.cn/api/gateway";
    public static String HOST;

    public static String API_KEY;

    public static String APP_SECRET;

    public static String PATH;


    private static String getSign(String appKey, String appSecret, String queryTime) {

        String signBody = "appKey=" + appKey + "&queryTime=" + queryTime;

        // 字符串转Base64编码
        String base64 = Base64.encodeBase64String(signBody.getBytes(StandardCharsets.UTF_8));
// 字符串拼接生成加密字符串, key = encryKey(encryKey通过线下方式对接获取）
        String strToBeEncode = String.join(",", base64, appSecret);
// 对上面加密字符串md5加密得到签名
        String sign = DigestUtils.md5DigestAsHex(strToBeEncode.getBytes());
        return sign;
    }


    public static JSONObject POST(String query) {
        JSONObject queryObject = new JSONObject();
        queryObject.set("content", query);
        queryObject.set("role", "user");
        List<JSONObject> queryObjectList = new ArrayList<>();
        queryObjectList.add(queryObject);
        String appKey = API_KEY;
        String appSecret = "1456vsa098b04c6687659605sdgfe4v6";
        String queryTime = String.valueOf(DateUtil.date().getTime());
        JSONObject jsonObject = new JSONObject();

        jsonObject.set("queryTime", queryTime);
        jsonObject.set("model", "32b8k");
        jsonObject.set("appKey", appKey);
        jsonObject.set("stream", false);
        jsonObject.set("messages", queryObjectList);
        HttpRequest post = HttpUtil.createPost(HOST + PATH);
        post.header("sign", getSign(appKey, appSecret, queryTime));
        post.body(JSONUtil.toJsonStr(jsonObject));
        HttpResponse execute = post.execute();
//        System.out.println(execute.body());


        JSONObject res = JSONUtil.parseObj(execute.body());
        Object choices = res.get("choices");
        JSONArray array = (JSONArray) choices;
        Object body = array.get(0);
        JSONObject resBody = (JSONObject) body;


        return (JSONObject) resBody.get("message");
    }

    public static void main(String[] args) {
//        System.out.println(POST("岚图梦想家2022起个图片名"));
        System.out.println(POST(getName("岚图,梦想家,电动豪华,MPV", "png")));
    }

    public static JSONObject getTagName(String tagValue, String fileType) {
        return POST(getName(tagValue, fileType));
    }

    public static JSONObject getTagName(String tagValue) {
        return POST(getName(tagValue,null));
    }

    public static JSONObject getTagDesc(String tagValue) {
        return POST(getDesc(tagValue));
    }

    /**
     * 获取标签描述
     *
     * @param content
     * @return
     */
    private static String getDesc(String content) {
        StringBuilder sb = new StringBuilder();
        sb.append("我现在需要生成一段关于岚图汽车的素材描述，样例如下“岚图梦想家是一款高端电动豪华MPV，" +
                "凭借其卓越的性能和智能化配置在市场中脱颖而出。它基于全球首个双动力高端原生电动架构打造”，" +
                "不要生成多余的引号,不要照抄样例里的词语，要保证标签的词语都包含在句子中。保证前后逻辑不变，" +
                "重点：内容不要浮夸陈述标签即可，" +
                "不要说独树一帜这类夸张的词语，标签中的数字为车的款型，并不是该年推出的，要包含标签列中全部，完整的内容，串成一句话。");
        if(ObjectUtil.isNotEmpty(content)){
            sb.append("标签如下:");
            sb.append(content);
        }

        return sb.toString();
    }

    /**
     * 获取标签名称
     *
     * @param content
     * @return
     */
    private static String getName(String content, String fileType) {
        StringBuilder sb = new StringBuilder();
        sb.append("需要生成一个标题，样例如下“岚图知音-非棚拍内饰图片”");
        if(ObjectUtil.isNotEmpty(content)){
            sb.append("标签如下:");
            sb.append(content);
        }
        sb.append("；第一个标签为车型，在车型前加上“岚图”，车型与后面的内容加上“-”，要精简概括，不需要写出细节，并在后面加上.");
        if(StrUtil.isNotEmpty(fileType)){
            sb.append("，并在后面加上.");
            sb.append(fileType);
            sb.append("，");
        }

        sb.append("不要生成多余的引号,不要超过25个字符");
        return sb.toString();
    }

    @Value("${ai.host}")
    public void setHOST(String HOST) {
        AiChatUtils.HOST = HOST;
    }

    @Value("${ai.key}")
    public void setApiKey(String apiKey) {
        API_KEY = apiKey;
    }

    @Value("${ai.secret}")
    public void setAppSecret(String appSecret) {
        APP_SECRET = appSecret;
    }

    @Value("${ai.path}")
    public void setPATH(String PATH) {
        AiChatUtils.PATH = PATH;
    }

}
