package cn.com.voyah.material.util;

import cn.com.voyah.material.exception.JsonException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

/**
 * JSON工具类
 */
public class JsonUtil {

    private static ObjectMapper OBJECT_MAPPER = null;

    private JsonUtil() {
    }

    /**
     * 获取JsonMapper对象
     *
     * @return ObjectMapper
     */
    public static ObjectMapper getObjectMapper() {
        if (OBJECT_MAPPER == null) {
            throw new JsonException("not object_mapper.");
        }
        return OBJECT_MAPPER;
    }

    public static void register(ObjectMapper mapper) {
        // 更新配置R
        //序列化的时候序列对象的那些属性
        //JsonInclude.Include.NON_DEFAULT 属性为默认值不序列化
        //JsonInclude.Include.ALWAYS      所有属性
        //JsonInclude.Include.NON_EMPTY   属性为 空（“”） 或者为 NULL 都不序列化
        //JsonInclude.Include.NON_NULL    属性为NULL 不序列化
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        //反序列化时,遇到未知属性会不会报错
        //true - 遇到没有的属性就报错 false - 没有的属性不会管，不会报错
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        OBJECT_MAPPER = mapper;
    }

    /**
     * 对象转JSON
     *
     * @param o 实体对象
     * @return JSON字符串
     */
    public static String writeValueAsString(Object o) {
        try {
            return getObjectMapper().writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new JsonException(e);
        }
    }


    /**
     * 字符串JSON转对象
     *
     * @param jsonStr JSON字符串
     * @param tClass  类型
     * @param <T>     类型
     * @return 对象
     */
    public static <T> T readValue(String jsonStr, Class<T> tClass) {
        try {
            return getObjectMapper().readValue(jsonStr, tClass);
        } catch (IOException e) {
            throw new JsonException(e);
        }
    }

    /**
     * 字符串JSON转对象
     *
     * @param bytes  JSON字节
     * @param tClass 类型
     * @param <T>    类型
     * @return 对象
     */
    public static <T> T readValue(byte[] bytes, Class<T> tClass) {
        try {
            return getObjectMapper().readValue(bytes, tClass);
        } catch (IOException e) {
            throw new JsonException(e);
        }
    }

    /**
     * 字符串JSON转对象
     *
     * @param jsonStr       JSON字符串
     * @param typeReference 类型
     * @param <T>           类型
     * @return 对象
     */
    public static <T> T readValue(String jsonStr, TypeReference<T> typeReference) {
        try {
            return getObjectMapper().readValue(jsonStr, typeReference);
        } catch (IOException e) {
            throw new JsonException(e);
        }
    }

}
