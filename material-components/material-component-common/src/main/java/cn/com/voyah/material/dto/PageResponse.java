package cn.com.voyah.material.dto;


import cn.hutool.json.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * Response with batch page record to return,
 * usually use in page query
 * <p/>
 * Created by xiaochu.lbj on 2020/06/30.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageResponse<T> extends SingleResponse<PageResponse.PageRow<T>> {

    @Serial
    private static final long serialVersionUID = 1L;

    public static PageResponse<?> buildFailure(int errCode, String errMessage) {
        PageResponse<?> response = new PageResponse<>();
        response.failure(errCode, errMessage);
        return response;
    }

    public static <T> PageResponse<T> of(Collection<T> data, int totalCount, int pageSize, int pageIndex) {
        PageResponse<T> response = new PageResponse<>();
        response.success();
        PageRow<T> pageRow = new PageRow<>();
        pageRow.setTotalCount(totalCount);
        pageRow.setPageSize(pageSize);
        pageRow.setPageIndex(pageIndex);
        pageRow.setList(data);
        response.setData(pageRow);
        return response;
    }

    public static <T> PageResponse<T> of(Collection<T> data, int totalCount, int pageSize, int pageIndex,JSONObject jsonObject) {
        PageResponse<T> response = new PageResponse<>();
        response.success();
        PageRow<T> pageRow = new PageRow<>();
        pageRow.setTotalCount(totalCount);
        pageRow.setPageSize(pageSize);
        pageRow.setPageIndex(pageIndex);
        pageRow.setList(data);
        pageRow.setExtend(jsonObject);
        response.setData(pageRow);

        return response;
    }

    @Data
    @Accessors(chain = true)
    public static class PageRow<T> {

        private int totalCount = 0;

        private int pageSize = 1;

        private int pageIndex = 1;
        private JSONObject extend;
        private Collection<T> list;


        public int getTotalPages() {
            return this.totalCount % this.pageSize == 0 ? this.totalCount
                    / this.pageSize : (this.totalCount / this.pageSize) + 1;
        }


        public int getPageSize() {
            return Math.max(pageSize, 1);
        }

        public PageRow<T> setPageSize(int pageSize) {
            this.pageSize = Math.max(pageSize, 1);
            return this;
        }

        public int getPageIndex() {
            return Math.max(pageIndex, 1);
        }

        public void setPageIndex(int pageIndex) {
            this.pageIndex = Math.max(pageIndex, 1);
        }

        public List<T> getList() {
            if (null == list) {
                return Collections.emptyList();
            }
            if (list instanceof List) {
                return (List<T>) list;
            }
            return new ArrayList<>(list);
        }

    }
}
