package cn.com.voyah.material.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BannerInfoDTO extends BaseDTO{

    private static final long serialVersionUID = 1L;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long id;

    @Schema(description = "")
    private String forwardUrl;
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "")
    private Long fileId;

    @Schema(description = "")
    private String name;

    @Schema(description = "")
    private String remark;

    @Schema(description = "")
    private Long sort;

    @Schema(description = "")
    private LocalDateTime startTime;

    @Schema(description = "")
    private LocalDateTime endTime;

    /**
     * 位置
     */
    @Schema(description = "位置")
    private Integer position;

    @Schema(description = "上架状态 0下架 1 上架")
    private Integer status;

    /**
     * 文件路径
     */
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String filename;
    @Schema(description = "文件在对应的存储平台对应的Key")
    private String thUrl;

    @Schema(description = "文件在对应的存储平台对应缩略图的Key")
    private String thFilename;

    @Schema(description = "文件在对应的存储平台对应的Key")
    private String url;

    @Schema(description = "MIME类型")
    private String contentType;
    private Long size;

    @Schema(description = "文件类型")
    private String fileType;
}
