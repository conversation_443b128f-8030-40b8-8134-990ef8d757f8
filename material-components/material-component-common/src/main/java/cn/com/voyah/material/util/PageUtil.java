package cn.com.voyah.material.util;

import cn.com.voyah.material.dto.PageQuery;
import cn.com.voyah.material.dto.PageResponse;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;

import java.util.List;
import java.util.function.Function;

public class PageUtil {
    private PageUtil() {
    }

    public static <T> PageResponse<T> of(Page<T> page) {
        return PageResponse.of(page.getRecords(),
                (int) page.getTotalRow(), (int) page.getPageSize(), (int) page.getPageNumber());
    }

    public static <T> PageResponse<T> of(Page<T> page, JSONObject jsonObject) {
        return PageResponse.of(page.getRecords(),
                (int) page.getTotalRow(), (int) page.getPageSize(), (int) page.getPageNumber(),jsonObject);
    }

    public static <T> Page<T> of(PageQuery query) {
        return new Page<>(query.getPageIndex(), query.getPageSize());
    }

    public static <T> Page<T> of(PageQueryCondition<?> query) {
        return new Page<>(query.getPageIndex(), query.getPageSize());
    }

    public static <T, R> Page<R> convertor(Page<T> page, Function<T, R> function) {
        Page<R> rpage = new Page<>(page.getPageNumber(), page.getPageSize(), page.getTotalRow());
        List<T> records = page.getRecords();
        if (records != null && !records.isEmpty()) {
            rpage.setRecords(records.stream().map(function).toList());
        }
        return rpage;
    }
}
