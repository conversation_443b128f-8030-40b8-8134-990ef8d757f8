package cn.com.voyah.material.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 文件资源类型
 */
@Data
@Schema(description = "文件资源类型")
public class FileSourceType {
    /**
     * 类型标识
     */
    @Schema(description = "类型标识")
    private String key;

    /**
     * 类型标签
     */
    @Schema(description = "类型标签")
    private String label;

    /**
     * 对应的文件支持后缀
     */
    @Schema(description = "对应的文件支持后缀")
    private List<String> suffixes;
}
