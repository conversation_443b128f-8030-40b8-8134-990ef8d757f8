<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.voyah.material</groupId>
        <artifactId>material-components</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>material-component-common</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <dependencies>
        <dependency>
            <groupId>com.mybatis-flex</groupId>
            <artifactId>mybatis-flex-core</artifactId>
            <version>${mybatis-flex.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations-jakarta</artifactId>
            <version>2.2.20</version>
            <scope>compile</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>net.coobird</groupId>-->
<!--            <artifactId>thumbnailator</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.bytedeco</groupId>-->
<!--            <artifactId>javacv</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.bytedeco</groupId>-->
<!--            <artifactId>javacpp</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.bytedeco.javacpp-presets</groupId>-->
<!--            <artifactId>opencv-platform</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.bytedeco.javacpp-presets</groupId>-->
<!--            <artifactId>ffmpeg-platform</artifactId>-->
<!--            &lt;!&ndash;            <classifier>windows-x86_64</classifier>&ndash;&gt;-->
<!--        </dependency>-->
<!--        &lt;!&ndash; javacv end &ndash;&gt;-->

<!--        <dependency>-->
<!--            <groupId>org.bytedeco.javacpp-presets</groupId>-->
<!--            <artifactId>opencv-platform</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>e-iceblue</groupId>
            <artifactId>spire.office.free</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel-core</artifactId>
            <version>3.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.14.0</version>
        </dependency>


        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>7.14.0</version>
        </dependency>

        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
            <version>portable-1.8.4</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.dromara.easy-es</groupId>-->
<!--            <artifactId>easy-es-boot-starter</artifactId>-->
<!--            &lt;!&ndash;这里Latest Version是指最新版本的依赖,比如2.0.0,可以通过下面的图片获取&ndash;&gt;-->
<!--            <version>2.1.0</version>-->
<!--        </dependency>-->
    </dependencies>


</project>