<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.com.voyah.material</groupId>
    <artifactId>material-server</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>material-server</name>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <hutool.version>5.8.25</hutool.version>
        <spring.boot.version>2.7.11</spring.boot.version>
        <knife4j.version>4.5.0</knife4j.version>
        <mysql.version>8.0.33</mysql.version>
        <mybatis-flex.version>1.9.8</mybatis-flex.version>
        <wiremock.version>3.5.4</wiremock.version>
        <h2.version>2.2.224</h2.version>
        <mapstruct.version>1.6.2</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <lombok.version>1.18.30</lombok.version>
        <jackson.version>2.13.5</jackson.version>
        <cos.version>5.6.234</cos.version>
        <commons-lang.version>2.6</commons-lang.version>
        <coobird.version>0.4.20</coobird.version>
        <javacv.version>1.4.1</javacv.version>
        <javacpp.version>1.5.4</javacpp.version>
    </properties>

    <modules>
        <module>material-components</module>
        <module>material-web-codegen</module>
        <module>material-web-client</module>
        <module>material-web-adapter</module>
        <module>material-web-app</module>
        <module>material-web-entity</module>
        <module>material-web-domain</module>
        <module>material-web-infrastructure</module>
        <module>start</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--Project modules End-->
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-components</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-component-common</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-component-mybatisflex</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-web-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-web-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-web-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-web-entity</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-web-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-web-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-component-catchlog</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.voyah.material</groupId>
                <artifactId>material-web-codegen</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--Project modules-->

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- MyBatis-Flex Mybatis扩展插件 -->
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-spring-boot-starter</artifactId>
                <version>${mybatis-flex.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-processor</artifactId>
                <version>${mybatis-flex.version}</version>
                <scope>provided</scope>
            </dependency>
            <!-- MyBatis-Flex 代码生成器 -->
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-codegen</artifactId>
                <version>${mybatis-flex.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- Lombok 注解工具 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!-- OpenAPI 文档 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- Mysql 连接驱动 -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!--this for embedded database unit test-->
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
                <scope>test</scope>
            </dependency>
            <!--this for dependent service mock, to avoid conflict, we'd better use standalone -->
            <dependency>
                <groupId>org.wiremock</groupId>
                <artifactId>wiremock-standalone</artifactId>
                <version>${wiremock.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- Unit Test Support End-->

            <!-- https://mvnrepository.com/artifact/org.mapstruct/mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${cos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos-sts_api</artifactId>
                <version>3.1.1</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>2.7.2</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.iov.tencent</groupId>
                <artifactId>inc-access-sdk</artifactId>
                <version>23.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>net.coobird</groupId>-->
<!--                <artifactId>thumbnailator</artifactId>-->
<!--                <version>${coobird.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.bytedeco</groupId>-->
<!--                <artifactId>javacv</artifactId>-->
<!--                <version>${javacv.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.bytedeco</groupId>-->
<!--                <artifactId>javacpp</artifactId>-->
<!--                <version>${javacpp.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.bytedeco.javacpp-presets</groupId>-->
<!--                <artifactId>opencv-platform</artifactId>-->
<!--                <version>3.4.1-1.4.1</version>-->
<!--                <classifier>windows-x86_64</classifier>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.bytedeco.javacpp-presets</groupId>-->
<!--                <artifactId>ffmpeg-platform</artifactId>-->
<!--                <version>3.4.2-1.4.1</version>-->
<!--                &lt;!&ndash;            <classifier>windows-x86_64</classifier>&ndash;&gt;-->
<!--            </dependency>-->
<!--            &lt;!&ndash; javacv end &ndash;&gt;-->

<!--            <dependency>-->
<!--                <groupId>org.bytedeco.javacpp-presets</groupId>-->
<!--                <artifactId>opencv-platform</artifactId>-->
<!--                <version>3.4.1-1.4.1</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>e-iceblue</groupId>
                <artifactId>spire.office.free</artifactId>
                <version>5.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>5.2.3</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.1.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <repositories>
        <repository>
            <id>com.e-iceblue</id>
            <url>https://repo.e-iceblue.cn/repository/maven-public/</url>
        </repository>
    </repositories>
</project>
