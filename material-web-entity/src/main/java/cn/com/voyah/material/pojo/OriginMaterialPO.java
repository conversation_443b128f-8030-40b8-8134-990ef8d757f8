package cn.com.voyah.material.pojo;

import cn.com.voyah.material.pojo.base.BasePO;
import cn.com.voyah.material.pojo.listener.FlexInsertListener;
import cn.com.voyah.material.pojo.listener.FlexUpdateListener;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import java.util.List;

import com.mybatisflex.core.handler.JacksonTypeHandler;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.*;

/**
 * 素材 实体类。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(value = "origin_material", onInsert = FlexInsertListener.class, onUpdate = FlexUpdateListener.class)
public class OriginMaterialPO extends BasePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 素材ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 素材名称
     */
    private String materialName;
    /**
     * 素材库id 1：产品素材 2：传播素材
     */
    private Long libraryId;

    /**
     * 是否公开 0：否  1：是
     */
    private Integer isPublic;

    /**
     * 是否是目录 0：否  1：是
     */
    private Boolean isDir;

    /**
     * 素材层级 0级为素材库
     */
    private Integer level;

    /**
     * 是否显示
     */
    private Integer isShow;

    /**
     * 层级父级ID
     */
    private Long parentId;

    /**
     * 目录及文件大小 Byte
     */
    private Long fileSize;

    /**
     * 文件类型 如；dir、pdf、img
     */
    private String fileType;

    /**
     * 文件本身的id
     */
    private Long fileId;

    /**
     * 最后上传人
     */
    private String lastUploadUser;

    /**
     * 最后上传时间
     */
    private LocalDateTime lastUploadTime;
    /**
     * 排序
     */
    private Integer sort;

    @Column(value="parent_id_list",typeHandler = JacksonTypeHandler.class)
    private List<Long> parentIdList;

    private Long fileCount;
    private Integer tagStatus;

}
