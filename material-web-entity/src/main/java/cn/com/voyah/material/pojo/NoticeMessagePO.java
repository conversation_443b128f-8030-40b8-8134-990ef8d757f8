package cn.com.voyah.material.pojo;

import cn.com.voyah.material.pojo.base.BasePO;
import cn.com.voyah.material.pojo.listener.FlexInsertListener;
import cn.com.voyah.material.pojo.listener.FlexUpdateListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

import java.io.Serial;

import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 通知表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(value="notice_message",onInsert = FlexInsertListener.class, onUpdate = FlexUpdateListener.class)
public class NoticeMessagePO extends BasePO implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 字典主键
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 1.任务派发通知
     * 2.任务到期提醒
     */
    private Integer type;

    private String content;

    private String shortId;

    private String userName;

    private LocalDateTime sendTime;

    private Integer source;

    /**
     * 0 未读 1已读
     */
    private Integer state;

    private Long relationId;

}
