package cn.com.voyah.material.pojo.base;

import com.mybatisflex.annotation.Column;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BasePO {

    /**
     * 创建人短ID
     */
    private String createShortId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人短ID
     */
    private String updateShortId;

    /**
     * 修改人
     */
    private String updateBy;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @Column(isLogicDelete = true)
    private Boolean isDelete;

    private Long version;
}
