package cn.com.voyah.material.pojo;

import cn.com.voyah.material.pojo.base.BasePO;
import cn.com.voyah.material.pojo.listener.FlexInsertListener;
import cn.com.voyah.material.pojo.listener.FlexUpdateListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;

import java.io.Serial;

import com.mybatisflex.core.keygen.KeyGenerators;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 *  实体类。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(value="tag_info", onInsert = FlexInsertListener.class, onUpdate = FlexUpdateListener.class)
public class TagInfoPO extends BasePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 字典主键
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 字典名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 字典描述
     */
    private String tagValue;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 状态 0：非必填  1：必填
     */
    private Integer required;

    @Schema(description = "0 显示 1 隐藏")
    private boolean hidden;

}
