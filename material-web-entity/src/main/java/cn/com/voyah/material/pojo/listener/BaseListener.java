package cn.com.voyah.material.pojo.listener;

import cn.com.voyah.material.pojo.base.BasePO;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.properties.IncInnerUserInfo;

public class BaseListener {

    /**
     * 当前会话用户信息赋值
     *  BasePO#setUpdateBy(String)  赋值
     *  BasePO#setUpdateShortId(String) 赋值
     */
    void userAssignment(BasePO entity) {
        IncInnerUserInfo userInfo = IncUserThreadLocal.get();
        String by;
        String shortId;
        if (userInfo != null) {
            shortId = userInfo.getName();
            by = userInfo.getUserName();
        } else {
            shortId = "";
            by = "";
        }
        entity.setUpdateShortId(shortId);
        entity.setUpdateBy(by);
    }
}
