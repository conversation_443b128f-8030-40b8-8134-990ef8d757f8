package cn.com.voyah.material.pojo;

import cn.com.voyah.material.pojo.base.BasePO;
import cn.com.voyah.material.pojo.listener.FlexInsertListener;
import cn.com.voyah.material.pojo.listener.FlexUpdateListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.io.Serial;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 收藏表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(value="material_favorite",onInsert = FlexInsertListener.class, onUpdate = FlexUpdateListener.class)
public class MaterialFavoritePO extends BasePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Integer id;

    /**
     * 素材库id
     */
    private Long materialId;

    private String shortId;

    private String userName;

}
