package cn.com.voyah.material.pojo.listener;

import cn.com.voyah.material.pojo.base.BasePO;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.properties.IncInnerUserInfo;
import com.mybatisflex.annotation.InsertListener;

import java.time.LocalDateTime;

/**
 * 写入数据监听
 */
public class FlexInsertListener extends BaseListener implements InsertListener {
    @Override
    public void onInsert(Object o) {
        BasePO entity = (BasePO) o;
        LocalDateTime now = LocalDateTime.now();
        entity.setVersion(0L);
        if (entity.getCreateBy() == null) {
            this.userAssignment(entity);
            entity.setCreateShortId(entity.getUpdateShortId());
            entity.setCreateBy(entity.getUpdateBy());
        }
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
        }
    }
}
