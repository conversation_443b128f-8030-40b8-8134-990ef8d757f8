package cn.com.voyah.material.pojo.listener;

import cn.com.voyah.material.pojo.base.BasePO;
import com.mybatisflex.annotation.UpdateListener;

import java.time.LocalDateTime;

/**
 * 写入数据监听
 */
public class FlexUpdateListener extends BaseListener implements UpdateListener {
    @Override
    public void onUpdate(Object o) {
        BasePO entity = (BasePO) o;
        if (entity.getUpdateBy() == null) {
            this.userAssignment(entity);
        }
        if (entity.getUpdateTime() == null) {
            entity.setUpdateTime(LocalDateTime.now());
        }
    }
}
