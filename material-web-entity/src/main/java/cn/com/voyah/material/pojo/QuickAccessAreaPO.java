package cn.com.voyah.material.pojo;

import cn.com.voyah.material.pojo.base.BasePO;
import cn.com.voyah.material.pojo.listener.FlexInsertListener;
import cn.com.voyah.material.pojo.listener.FlexUpdateListener;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.io.Serial;
import java.util.List;

import com.mybatisflex.core.handler.JacksonTypeHandler;
import com.mybatisflex.core.keygen.KeyGenerators;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 金刚位表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(value="quick_access_area",onInsert = FlexInsertListener.class, onUpdate = FlexUpdateListener.class)
public class QuickAccessAreaPO extends BasePO implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 字典主键
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    @Column(value="tags",typeHandler = JacksonTypeHandler.class)
    private List<TagInfoMergeVO> tags;

    private Long fileId;

    private String name;

    private String remark;

    private Long sort;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    /**
     * 位置
     */
    private Integer position;


    /**
     * 上架状态 0下架 1 上架
     */
    private Integer status;

    /**
     * 文件类型
     */
    private String fileType;

    private Long backgroundFileId;
}
