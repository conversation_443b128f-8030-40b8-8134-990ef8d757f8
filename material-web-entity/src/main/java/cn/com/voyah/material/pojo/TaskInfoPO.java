package cn.com.voyah.material.pojo;

import cn.com.voyah.material.pojo.base.BasePO;
import cn.com.voyah.material.pojo.listener.FlexInsertListener;
import cn.com.voyah.material.pojo.listener.FlexUpdateListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.io.Serial;
import com.mybatisflex.core.keygen.KeyGenerators;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 任务基本信息表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(value="task_info",onInsert = FlexInsertListener.class, onUpdate = FlexUpdateListener.class)
public class TaskInfoPO extends BasePO implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 字典主键
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务期望完成完成时间
     */
    private LocalDateTime taskEndTime;

    /**
     * 派发人姓名
     */
    private String dispatchBy;

    /**
     * 派发人短id
     */
    private String dispatchShortId;

    /**
     * 执行人姓名
     */
    private String executeBy;

    /**
     * 执行人短id
     */
    private String executeShortId;

    /**
     * 任务状态  新建进行中
     */
    private Integer taskStatus;

    /**
     * 素材库id
     */
    private Long materialFoldId;

    /**
     * 素材数量
     */
    private Integer materialFileCount;

    private LocalDateTime taskFinishTime;

    private Integer actualFileCount;

    @Schema(description = "任务期望开始时间")
    private LocalDateTime taskStartTime;

    private String remark;



}
