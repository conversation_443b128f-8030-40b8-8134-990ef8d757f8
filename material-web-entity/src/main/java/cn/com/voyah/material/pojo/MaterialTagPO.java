package cn.com.voyah.material.pojo;

import cn.com.voyah.material.pojo.base.BasePO;
import cn.com.voyah.material.pojo.listener.FlexInsertListener;
import cn.com.voyah.material.pojo.listener.FlexUpdateListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;

import java.io.Serial;

import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 *  实体类。
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(value="material_tag", onInsert = FlexInsertListener.class, onUpdate = FlexUpdateListener.class)
public class MaterialTagPO extends BasePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    private Long materialId;

    private String remark;
    /**
     * 用户浏览量
     */
    private Long userViewCount;
    /**
     * 用户收藏量
     */
    private Long userFavoriteCount;

    /**
     * 用户下载量
     */
    private Long userDownloadCount;

}
