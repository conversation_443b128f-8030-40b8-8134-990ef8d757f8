package cn.com.voyah.material.pojo;

import cn.com.voyah.material.pojo.base.BasePO;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;

import java.io.Serial;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 文件记录中间表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("file_detail_tmp")
public class FileDetailTmpPO extends BasePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件id
     */
    @Id
    private Long id;

    private String url;

    /**
     * 文件大小，单位字节
     */
    private Long size;

    private String filename;

    private String originalFilename;

    /**
     * 基础存储路径
     */
    private String basePath;

    private String path;

    /**
     * MIME类型
     */
    private String contentType;

    /**
     * 缩略图访问路径
     */
    private String thUrl;

    /**
     * 缩略图名称
     */
    private String thFilename;

    /**
     * 缩略图大小，单位字节
     */
    private Long thSize;

    /**
     * 缩略图MIME类型
     */
    private String thContentType;

    private Integer status;

}
