package cn.com.voyah.material.pojo;

import cn.com.voyah.material.pojo.base.BasePO;
import cn.com.voyah.material.pojo.listener.FlexInsertListener;
import cn.com.voyah.material.pojo.listener.FlexUpdateListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * 素材库（类型） 实体类。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(value = "material_library", onInsert = FlexInsertListener.class, onUpdate = FlexUpdateListener.class)
public class MaterialLibraryPO extends BasePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 素材ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 素材库编号
     */
    private String libraryNo;

    /**
     * 素材库名称
     */
    private String libraryName;

    /**
     * 是否公开 0：否  1：是
     */
    private Integer isPublic;

    /**
     * 启用状态 0：禁用 1：启用
     */
    private Integer status;

    /**
     * 原始素材主文件夹ID
     */
    private Long originMaterialId;

    /**
     * 排序
     */
    private Integer sort;

}
