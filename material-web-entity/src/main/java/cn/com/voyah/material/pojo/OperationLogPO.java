package cn.com.voyah.material.pojo;

import cn.com.voyah.material.pojo.base.BasePO;
import cn.com.voyah.material.pojo.listener.FlexInsertListener;
import cn.com.voyah.material.pojo.listener.FlexUpdateListener;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.io.Serial;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 日志表 实体类。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(value="operation_log",onInsert = FlexInsertListener.class, onUpdate = FlexUpdateListener.class)
public class OperationLogPO extends BasePO implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 字典主键
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    private String shortId;

    private String userName;

    private Integer state;

    private String interfaceRemark;

    private LocalDateTime operationTime;

    private String ip;

    private String path;

    private String method;

    private String requestParams;

    private String response;

    private String errorMsg;

    private Long timeInterval;

    private LocalDateTime requestTime;

    private LocalDateTime responseTime;

}
