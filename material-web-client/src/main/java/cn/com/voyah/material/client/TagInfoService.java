package cn.com.voyah.material.client;

import cn.com.voyah.material.domain.entity.TagInfoDO;
import cn.com.voyah.material.dto.query.TagInfoQuery;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;

import java.util.List;
import java.util.Map;

public interface TagInfoService {

    Map<String, List<TagInfoDO>> getByParentIdList2(TagInfoQuery tagInfoQuery);

    List<TagInfoMergeVO> getAllTagMergeVoList(Long categoryId,boolean filterHiden);

    List<TagInfoDO> likeValueList(String query);



}
