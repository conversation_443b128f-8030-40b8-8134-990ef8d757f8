package cn.com.voyah.material.dto.library;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.NOT_REQUIRED;

@Data
public class MaterialLibraryQuery {


    /**
     * 素材库名称
     */
    @Schema(description = "主文件夹名称（素材库名称）", requiredMode = NOT_REQUIRED)
    private String libraryName;

    /**
     * 启用状态 0：禁用 1：启用
     */
    @Schema(description = "启用状态 0：禁用 1：启用", requiredMode = NOT_REQUIRED)
    private Integer status;

    /**
     * 是否公开 0：否  1：是
     */
    @Schema(description = "是否公开 0：否  1：是", requiredMode = NOT_REQUIRED)
    private Integer isPublic;

}
