package cn.com.voyah.material.client;

import cn.com.voyah.material.constants.TaskStatus;
import cn.com.voyah.material.domain.entity.TaskInfoDO;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import com.mybatisflex.core.paginate.Page;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

public interface TaskInfoService {
    Collection<TaskInfoDO> list(TaskInfoDO taskInfoDO);

    Page<TaskInfoDO> getPage(PageQueryCondition<TaskInfoDO> query, TimeQueryInterval createTimeQueryInterval);

    boolean updateStatus(Long id, TaskStatus status, LocalDateTime date);

    TaskInfoDO save(TaskInfoDO taskInfoDO);

    void taskFileCountAdd(String shortId,Long materialFoldId);

    boolean updateStatusBatch(List<Long> ids, TaskStatus status);
}
