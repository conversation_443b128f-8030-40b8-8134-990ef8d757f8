package cn.com.voyah.material.client;

import cn.com.voyah.material.domain.entity.MaterialAuthDO;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import com.mybatisflex.core.paginate.Page;

import java.util.List;

public interface MaterialAuthService {


    List<Long> getAuthParentIdList();

    void removeAuthKeyCache(String shortId);


    Page<MaterialAuthDO> page(PageQueryCondition<MaterialAuthDO> query);

}
