package cn.com.voyah.material.client;
import cn.com.voyah.material.domain.entity.MaterialTagDO;
import cn.com.voyah.material.domain.entity.MaterialTagItemDO;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface MaterialTagService {

    Boolean save(MaterialTagVO materialTagVO);

    Boolean removeById(Long id);

    Boolean updateById(MaterialTagVO materialTagVO);

    MaterialTagVO getByMaterialId(Long materialId,Long categoryId);
    @Deprecated
    Set<MaterialTagHeadingVO> getHeadingByMaterialId(Long materialId, Long categoryId);

    List<MaterialTagItemDO> getBindTagsByMaterialId(Long materialId, Long categoryId);

    Boolean saveNew(MaterialTagNewDTO materialTagNewDTO);

    Collection<MaterialTagHeadingVO2> getHeadingByMaterialId2(Long materialId, Long categoryId);

    Boolean addRemark(MaterialTagDO materialTagDO);

    MaterialTagDO getBaseInfoByMaterialId(Long materialId);


    List<Long> getCategoryIdList(Long materialId);

    boolean addViewCount(Long materialId);
    boolean addFavoriteCount(Long materialId,boolean isIncr);

    MaterialTagDO getBaseByMaterialId(Long materialId);

    List<Long> getMaterialIdListByTags(List<TagInfoMergeVO> tags);

    Set<MaterialTagHeading3VO> getHeadingByMaterialId3(Long materialId, Long categoryId);

    Boolean saveBatch(Long materialId, List<MaterialTagNewDTO> list);

    Boolean saveBatchNew(MaterialTagBatchDTO materialTagBatchDTO);

    boolean addDownloadCount(Long materialId);

    List<Long> existTagIdList();
}
