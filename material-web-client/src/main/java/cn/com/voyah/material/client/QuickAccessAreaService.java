package cn.com.voyah.material.client;

import cn.com.voyah.material.domain.entity.QuickAccessAreaDO;
import cn.com.voyah.material.dto.OriginMaterialExtensionDTO;
import cn.com.voyah.material.dto.QuickAccessAreaDTO;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.update.SortChangeDTO;
import com.mybatisflex.core.paginate.Page;

import java.util.Collection;

public interface QuickAccessAreaService {

    Collection<QuickAccessAreaDTO> list(QuickAccessAreaDO quickAccessAreaDO);

    Page<QuickAccessAreaDTO> page(PageQueryCondition<QuickAccessAreaDO> query, TimeQueryInterval createTimeQueryInterval);

    QuickAccessAreaDTO getById(Long id);

    String generateFileName(String dir,String originName);

    Boolean sortChange(SortChangeDTO sortChangeDTO);
}
