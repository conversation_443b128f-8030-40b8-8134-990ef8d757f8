package cn.com.voyah.material.client;

import cn.com.voyah.material.domain.entity.RecommendSearchDO;
import cn.com.voyah.material.dto.RecommendSearchDTO;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.update.SortChangeDTO;
import com.mybatisflex.core.paginate.Page;

import java.util.Collection;

public interface RecommendSearchService {

    Collection<RecommendSearchDTO> list(PageQueryCondition<RecommendSearchDTO> pageQuery, TimeQueryInterval createTimeQueryInterval);

    Collection<RecommendSearchDTO> listByUser();


    Page<RecommendSearchDTO> page(PageQueryCondition<RecommendSearchDO> query, TimeQueryInterval createTimeQueryInterval);

    RecommendSearchDTO getById(Long id);



    Boolean sortChange(SortChangeDTO sortChangeDTO);
}
