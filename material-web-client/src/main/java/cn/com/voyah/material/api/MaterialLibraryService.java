package cn.com.voyah.material.api;

import cn.com.voyah.material.domain.entity.MaterialLibraryDO;
import cn.com.voyah.material.dto.library.MaterialLibraryAdd;
import cn.com.voyah.material.dto.library.MaterialLibraryQuery;
import cn.com.voyah.material.dto.library.MaterialLibrarySortChange;
import cn.com.voyah.material.dto.library.MaterialLibraryUpdate;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import com.mybatisflex.core.paginate.Page;

public interface MaterialLibraryService {

    MaterialLibraryDO saveLibrary(MaterialLibraryAdd add);

    Boolean updateLibrary(MaterialLibraryUpdate update);

    Page<MaterialLibraryDO> page(PageQueryCondition<MaterialLibraryQuery> of);

    /**
     * 排序修改
     */
    void sortChange(MaterialLibrarySortChange sortChange);

}
