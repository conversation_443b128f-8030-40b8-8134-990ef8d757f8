package cn.com.voyah.material.dto.library;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class MaterialLibrarySortChange  {

    /**
     * 第一个素材ID
     */
    @NotNull(message = "第一个ID不能为空")
    @Schema(description = "第一个素材ID")
    private Long oneId;

    /**
     * 第一个素材排序
     */
    @NotNull(message = "第一个排序不能为空")
    @Schema(description = "第一个素材排序")
    private Integer oneSort;
    /**
     * 第二个素材ID
     */
    @NotNull(message = "第二个ID不能为空")
    @Schema(description = "第二个素材ID")
    private Long towId;
    /**
     * 第二个素材排序
     */
    @NotNull(message = "第二个排序不能为空")
    @Schema(description = "第二个素材排序")
    private Integer towSort;

}
