package cn.com.voyah.material.client;

import cn.com.voyah.material.domain.entity.NoticeMessageDO;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import com.mybatisflex.core.paginate.Page;
import java.util.Collection;
import java.util.List;

public interface NoticeMessageService {
    Collection<NoticeMessageDO> list(NoticeMessageDO noticeMessageDO);

    Page<NoticeMessageDO> getPage(PageQueryCondition<NoticeMessageDO> query,TimeQueryInterval createTimeQueryInterval);

    Long getCount(NoticeMessageDO noticeMessageDO);

    NoticeMessageDO save(NoticeMessageDO noticeMessageDO);
    void saveBatch(List<NoticeMessageDO> noticeMessageDOList);

}
