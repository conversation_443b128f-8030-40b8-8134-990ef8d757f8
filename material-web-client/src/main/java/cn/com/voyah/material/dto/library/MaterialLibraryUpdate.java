package cn.com.voyah.material.dto.library;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialLibraryUpdate extends MaterialLibraryAdd {

    /**
     * 素材ID
     */
    @NotNull(message = "ID不能为空")
    @Schema(description = "素材ID")
    private Long id;

}
