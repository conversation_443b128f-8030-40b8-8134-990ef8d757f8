package cn.com.voyah.material.client;

import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.entity.FilePartDO;
import cn.com.voyah.material.domain.entity.dto.ResponseDTO;
import cn.com.voyah.material.domain.entity.migrate.MigrateEntityDO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

public interface FileService {

    FileDetailDO upload(MultipartFile file,String newFileName);
    ResponseDTO getFederationToken();

    String getObjectUrl(String fileName);

    FileDetailDO sliceUploadStart(MultipartFile file, String newFileName);
    FilePartDO sliceUploading(MultipartFile file, String uploadId, String key, int partNum) throws IOException;
    void sliceUploadFinish(String key, String uploadId, List<FilePartDO> parts);

    FileDetailDO generateThFile(FileDetailDO originFileDetailDO);

    Collection<MigrateEntityDO> getObjectLists(String dir);

    void upload(String fileDir,String newFileName);
    void downloadFileTmpKey(String key,String tmpPathName) throws IOException;
}
