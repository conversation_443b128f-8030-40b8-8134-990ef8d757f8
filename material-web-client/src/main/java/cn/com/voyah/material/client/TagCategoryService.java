package cn.com.voyah.material.client;

import cn.com.voyah.material.domain.entity.TagCategoryDO;
import cn.com.voyah.material.domain.entity.migrate.TagBatchSaveEntity;
import cn.com.voyah.material.dto.TagCategoryDTO;
import cn.com.voyah.material.dto.TagCategoryTreeDTO;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.query.TagCategoryQuery;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import com.mybatisflex.core.paginate.Page;

import java.util.Collection;
import java.util.List;

public interface TagCategoryService {
    Page<TagCategoryDTO> page(PageQueryCondition<TagCategoryQuery> of);

    void batchSave(TagBatchSaveEntity tagBatchSaveEntity);

    void batchSave2(TagBatchSaveEntity tagBatchSaveEntity);
    List<TagCategoryDO> getList(TagCategoryDO tagCategoryDO);

    TagCategoryDO save(TagCategoryDO tagCategoryDO);

    Boolean updateById(TagCategoryDO tagCategoryDO);

    List<TagCategoryTreeDTO> getAllTagMergeTreeList(Long categoryId);
}
