package cn.com.voyah.material.client;

import cn.com.voyah.material.domain.entity.BannerInfoDO;
import cn.com.voyah.material.dto.BannerInfoDTO;
import cn.com.voyah.material.dto.OriginMaterialExtensionDTO;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.update.SortChangeDTO;
import com.mybatisflex.core.paginate.Page;

import java.util.Collection;

public interface BannerInfoService {
    Collection<BannerInfoDTO> list(BannerInfoDO bannerInfoDO);

    Page<BannerInfoDTO> page(PageQueryCondition<BannerInfoDO> query,TimeQueryInterval createTimeQueryInterval);

    BannerInfoDTO getById(Long id);

    String generateFileName(String dir,String originName);

    Boolean sortChange(SortChangeDTO sortChangeDTO);
}
