package cn.com.voyah.material.client;

import cn.com.voyah.material.domain.entity.OperationLogDO;
import cn.com.voyah.material.dto.OperationLogExportQuery;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import com.mybatisflex.core.paginate.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface OperationLogService {
    List<OperationLogDO> list(OperationLogExportQuery operationLogExportQuery);

    Page<OperationLogDO> getPage(PageQueryCondition<OperationLogDO> query, TimeQueryInterval createTimeQueryInterval);

    void exportLogs(OperationLogExportQuery operationLogExportQuery, HttpServletResponse response);
}
