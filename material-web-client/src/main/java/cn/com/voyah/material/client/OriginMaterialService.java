package cn.com.voyah.material.client;

import cn.com.voyah.material.domain.entity.OriginMaterialDO;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.vo.user.OriginMaterialExtensionVO;
import cn.com.voyah.material.vo.user.OriginMaterialInfoV2VO;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;

import java.util.List;

public interface OriginMaterialService {


    OriginMaterialDO save(OriginMaterialDTO originMaterialDTO);

    PageResponse<OriginMaterialExtensionDTO> getPageInfo(PageQueryCondition<OriginMaterialExtensionDTO> query);

    JSONObject getAiName(Long id);

    JSONObject getAiName(String tag);

    JSONObject getAiDesc(Long id);

    JSONObject getAiDesc(String tag);

    OriginMaterialExtensionDTO getDetailById(Long id);

    Boolean updateById(OriginMaterialDO originMaterialDO);

    List<OriginMaterialExtensionDTO> getList(OriginMaterialExtensionDTO originMaterialExtensionDTO, Boolean isSuper);

    List<OriginMaterialDO> getParentList(Long id);

    OriginMaterialExtensionDTO generateFileName(OriginMaterialExtensionDTO originMaterialExtensionDTO);

    void foldUpload(Long parentId, List<OriginMaterialExtensionDTO> list);

    /**
     * 递归跟新文件数量
     */
    void updateFileCountRecursion(OriginMaterialDO originMaterialDO);

    void updateFileCountRecursion(Long parentId);

    PageResponse<OriginMaterialExtensionDTO> getGlobalSearchPageInfo(PageQueryCondition<GlobalOriginMaterialQuery> pageQuery, TimeQueryInterval createTimeQueryInterval);

    PageResponse<OriginMaterialExtensionDTO> getGlobalSearchPageInfo(PageQueryCondition<GlobalOriginMaterialQuery> pageQuery, TimeQueryInterval createTimeQueryInterval, TimeQueryInterval tagCreateTimeQueryInterval);

    void removeIdAndChildAll(OriginMaterialDO before);

    Boolean updatePublicStatusById(Long id, Integer publicStatus);

    Boolean updatePublicStatusBatch(UpdatePublicBatchDTO dto);

    /**
     * 向上递归
     *
     * @param id
     * @return
     */
    void updatePublicStatusRecursion(Long id);

    Boolean move(MaterialMoveDTO materialMoveDTO);

    Boolean moveList(MaterialMoveListDTO dto);


    Page<OriginMaterialExtensionVO> getGlobalTagSearchPageInfo(PageQueryCondition<GlobalOriginMaterialQuery> pageQuery, List<Long> materialIdList, TimeQueryInterval createTimeQueryInterval, TimeQueryInterval tagCreateTimeQueryInterval);


    Page<OriginMaterialInfoV2VO> getGlobalTagSearchPageInfoV2(PageQueryCondition<GlobalOriginMaterialQuery> pageQuery);


    MaterialTreeDTO getChildrenTree(Long id, boolean isDir);

    Page<OriginMaterialExtensionVO> getFavoriteSearchPage(PageQueryCondition<GlobalOriginMaterialQuery> pageQuery, TimeQueryInterval createTimeQueryInterval);

}
