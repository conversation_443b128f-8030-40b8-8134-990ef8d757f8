package cn.com.voyah.material.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class UserAccountVO {

    /**
     * 账号短ID
     */
    @Schema(description = "账号短ID")
    private String shortId;

    /**
     * 账号姓名
     */
    @Schema(description = "账号姓名")
    private String userName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleNames;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptNames;

    /**
     * 应用权限
     */
    @Schema(description = "应用权限")
    private Boolean appPermission;

    /**
     * 管理后台权限
     */
    @Schema(description = "管理后台权限")
    private Boolean adminPermission;

    /**
     * 上传登录时间
     */
    @Schema(description = "上传登录时间")
    private LocalDateTime lastLoginTime;

}
