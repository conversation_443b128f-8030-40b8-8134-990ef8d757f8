<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.voyah.material</groupId>
        <artifactId>material-server</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>material-web-adapter</artifactId>
    <packaging>jar</packaging>
    <name>material-web-adapter</name>

    <dependencies>
        <dependency>
            <groupId>cn.com.voyah.material</groupId>
            <artifactId>material-web-app</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.voyah.material</groupId>
            <artifactId>material-component-catchlog</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.bytedeco</groupId>-->
<!--            <artifactId>javacv</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.bytedeco</groupId>-->
<!--            <artifactId>javacpp</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.bytedeco.javacpp-presets</groupId>-->
<!--            <artifactId>opencv-platform</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.bytedeco.javacpp-presets</groupId>-->
<!--            <artifactId>ffmpeg-platform</artifactId>-->
<!--            &lt;!&ndash;            <classifier>windows-x86_64</classifier>&ndash;&gt;-->
<!--        </dependency>-->
<!--        &lt;!&ndash; javacv end &ndash;&gt;-->

<!--        <dependency>-->
<!--            <groupId>org.bytedeco.javacpp-presets</groupId>-->
<!--            <artifactId>opencv-platform</artifactId>-->
<!--        </dependency>-->
    </dependencies>
</project>
