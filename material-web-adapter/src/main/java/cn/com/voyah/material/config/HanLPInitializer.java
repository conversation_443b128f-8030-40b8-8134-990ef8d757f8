package cn.com.voyah.material.config;

import cn.com.voyah.material.domain.entity.TagInfoDO;
import cn.com.voyah.material.domain.gateway.TagInfoGateway;
import cn.hutool.core.date.DateUtil;
import com.hankcs.hanlp.dictionary.CustomDictionary;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class HanLPInitializer implements CommandLineRunner {

    @Autowired
    private TagInfoGateway tagInfoGateway;

    @Override
    public void run(String... args) throws Exception {
        log.info("分词同步开始：{}", DateUtil.now());
        List<TagInfoDO> list = tagInfoGateway.list();
        List<String> tagList = list.stream().map(TagInfoDO::getTagValue).collect(Collectors.toList());
        tagList.stream().forEach(t->{
            CustomDictionary.add(t);
            log.info("增加分词：{}",t);
        });
        log.info("分词同步完成：{}", DateUtil.now());
    }
}