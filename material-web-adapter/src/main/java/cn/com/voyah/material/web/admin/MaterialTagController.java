package cn.com.voyah.material.web.admin;
import cn.com.voyah.material.client.MaterialTagService;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.domain.entity.MaterialTagDO;
import cn.com.voyah.material.domain.gateway.TagInfoGateway;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.exception.BizException;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import java.util.List;
import java.util.Set;

/**
 *  控制层。
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@CatchAndLog
@RestController
@Tag(name = "标注相关(管理平台)")
@RequestMapping("/material-tag")
public class MaterialTagController {

    @Autowired
    private MaterialTagService materialTagService;
    @Autowired
    private TagInfoGateway tagInfoGateway;

    /**
     * 添加。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "添加标注",description = "管理平台-添加素材标注")
    public SingleResponse<Boolean> save(@RequestBody @Parameter(description="") MaterialTagVO materialTagVO) {
        return SingleResponse.of(materialTagService.save(materialTagVO));
    }

    /**
     * 根据主键删除。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "删除标注",description = "管理平台-删除素材标注")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(materialTagService.removeById(id));
    }

    /**
     * 根据主键更新。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "更新标注",description = "管理平台-修改素材标注")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description="主键")MaterialTagVO materialTagVO) {

        return SingleResponse.of(materialTagService.updateById(materialTagVO));
    }

    /**
     * 根据主键获取详细信息。
     *
     * @param materialId 根据素材id查询有无标注
     * @return 详情
     */
    @GetMapping("get-by-material-id/{materialId}")
    @Operation(summary = "根据素材id查询单个标注信息",description = "管理平台-查询单个素材标注")
    public SingleResponse<MaterialTagVO> getByMaterialId(@PathVariable(name = "materialId") Long materialId, @RequestParam("categoryId") Long categoryId) {
        return SingleResponse.of(materialTagService.getByMaterialId(materialId,categoryId));
    }


    @GetMapping("get-by-material-id/heading/{materialId}")
    @Operation(summary = "根据素材id查询单个标注表头信息",description = "管理平台-查询素材标注名称列表")
    public MultiResponse<MaterialTagHeadingVO> getHeadingByMaterialId(@PathVariable(name = "materialId") Long materialId, @RequestParam(required = false,value="categoryId") Long categoryId) {
        return MultiResponse.of(materialTagService.getHeadingByMaterialId(materialId,categoryId));
    }

    @Deprecated
    @GetMapping("get-by-material-id/heading2/{materialId}")
    @Operation(summary = "根据素材id查询单个标注表头信息",description = "管理平台-查询素材标注名称列表")
    public MultiResponse<MaterialTagHeadingVO2> getHeadingByMaterialId2(@PathVariable(name = "materialId") Long materialId, @RequestParam("categoryId") Long categoryId) {
        return MultiResponse.of(materialTagService.getHeadingByMaterialId2(materialId,categoryId));
    }


    /**
     * 添加。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save-new")
    @Operation(summary = "添加标注",description = "管理平台-添加标注")
    public SingleResponse<Boolean> saveNew(@RequestBody @Parameter(description="") MaterialTagNewDTO materialTagNewDTO) {
        if(ObjectUtil.isEmpty(materialTagNewDTO)){
            throw new BizException("标注信息不能为空!");
        }
        if(CollectionUtils.isEmpty(materialTagNewDTO.getTagIdList())){
            return SingleResponse.of(true);
        }
        if(ObjectUtil.isEmpty(materialTagNewDTO.getMaterialId())){
            throw new BizException("请选择素材id");
        }
        if(ObjectUtil.isEmpty(materialTagNewDTO.getCategoryId())){
            throw new BizException("请选择类目Id");
        }
        return SingleResponse.of(materialTagService.saveNew(materialTagNewDTO));
    }



    @PutMapping("update-new")
    @Operation(summary = "添加标注",description = "管理平台-更新标注")
    public SingleResponse<Boolean> updateNew(@RequestBody @Parameter(description="") MaterialTagNewDTO materialTagNewDTO) {
        if(ObjectUtil.isEmpty(materialTagNewDTO)){
            throw new BizException("标注信息不能为空!");
        }
        if(CollectionUtils.isEmpty(materialTagNewDTO.getTagIdList())){
            return SingleResponse.of(true);
        }
        if(ObjectUtil.isEmpty(materialTagNewDTO.getMaterialId())){
            throw new BizException("请选择素材id");
        }
        if(ObjectUtil.isEmpty(materialTagNewDTO.getCategoryId())){
            throw new BizException("请选择类目Id");
        }
        return SingleResponse.of(materialTagService.saveNew(materialTagNewDTO));
    }

//    /**
//     * 根据主键获取详细信息。
//     *
//     * @param id 根据素材id查询有无标注
//     * @return 详情
//     */
//    @GetMapping("get-by-id/{id}")
//    @Operation(summary = "根据标注主键id查询单个标注信息")
//    public SingleResponse<MaterialTagVO> getInfo(@PathVariable(name = "id") Long id) {
//        return SingleResponse.of(materialTagService.getById(id));
//    }



    @PostMapping("/add/remark")
    @Operation(summary = "添加备注信息",description = "管理平台-添加标注备注信息")
    public SingleResponse<Boolean> addRemark(@RequestBody @Parameter(description="") MaterialTagDO materialTagDO) {
        return SingleResponse.of(materialTagService.addRemark(materialTagDO));
    }


    @GetMapping("get-base-info-by-material-id/{materialId}")
    @Operation(summary = "根据素材id查询单个标注信息",description = "管理平台-查询素材标注详细信息")
    public SingleResponse<MaterialTagDO> getBaseInfoByMaterialId(@PathVariable(name = "materialId") Long materialId) {
        return SingleResponse.of(materialTagService.getBaseInfoByMaterialId(materialId));
    }


    @GetMapping("get-by-material-id/heading3/{materialId}")
    @Operation(summary = "根据素材id查询单个标注表头信息",description = "管理平台-查询素材标注名称列表")
    public SingleResponse<Set<MaterialTagHeading3VO>> getHeadingByMaterialId3(@PathVariable(name = "materialId") Long materialId, @RequestParam(required = false,value="categoryId") Long categoryId) {
        return SingleResponse.of(materialTagService.getHeadingByMaterialId3(materialId,categoryId));
    }

    @PostMapping("{materialId}/save-batch")
    @Operation(summary = "管理平台-添加标注不同类目一起保存",description = "管理平台-添加标注")
    public SingleResponse<Boolean> saveBatch(@PathVariable(name = "materialId") Long materialId,@RequestBody @Parameter(description="")MaterialTagBatchDTO materialTagBatchDTO) {
        if(ObjectUtil.isEmpty(materialTagBatchDTO)){
            throw new BizException("标注信息不能为空!");
        }
        materialTagBatchDTO.setMaterialId(materialId);
        return SingleResponse.of(materialTagService.saveBatchNew(materialTagBatchDTO));
    }

    @PostMapping("/tmp/hidden")
    @Operation(summary = "更新未使用的标签为隐藏状态")
    public SingleResponse<Boolean> tmpChangeHidden() {
        List<Long> existTagIdList=materialTagService.existTagIdList();
        if(!CollectionUtils.isEmpty(existTagIdList)){
            tagInfoGateway.updateHiddenStatusByExistTagIdList(existTagIdList);
        }
        return SingleResponse.of(true);
    }
}