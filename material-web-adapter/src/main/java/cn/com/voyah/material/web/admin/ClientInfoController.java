package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.client.ClientInfoService;
import cn.com.voyah.material.dto.ClientInfoDTO;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.dto.MultiResponse;
import cn.com.voyah.material.dto.PageResponse;
import cn.com.voyah.material.dto.SingleResponse;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import cn.com.voyah.material.domain.entity.ClientInfoDO;
import cn.com.voyah.material.domain.gateway.ClientInfoGateway;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 *  控制层。
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@CatchAndLog
@RestController
@Tag(name = "接口")
@RequestMapping("/client-info")
public class ClientInfoController {

    @Autowired
    private ClientInfoGateway clientInfoGateway;

    @Autowired
    private ClientInfoService clientInfoService;

    /**
     * 添加。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存",description = "管理平台-添加客户端安装包")
    public SingleResponse<ClientInfoDO> save(@RequestBody @Parameter(description="")ClientInfoDO clientInfoDO) {
        return SingleResponse.of(clientInfoGateway.save(clientInfoDO));
    }

    /**
     * 根据主键删除。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键",description = "管理平台-删除客户端安装包")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(clientInfoGateway.removeById(id));
    }

    /**
     * 根据主键更新。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新",description = "管理平台-修改客户端安装包信息")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description="主键")ClientInfoDO clientInfoDO) {
        return SingleResponse.of(clientInfoGateway.updateById(clientInfoDO));
    }

    /**
     * 查询所有。
     *
     * @return 所有数据
     */
    @GetMapping("list")
    @Operation(summary = "查询对应的客户端信息",description = "管理平台-查询客户端安装包列表")
    public MultiResponse<ClientInfoDTO> list() {

        return MultiResponse.of(clientInfoService.getRecentList());
    }

    /**
     * 根据主键获取详细信息。
     *
     * @param id 主键
     * @return 详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取安装包信息",description = "管理平台-查询客户端安装包信息")
    public SingleResponse<ClientInfoDO> getInfo(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(clientInfoGateway.getById(id));
    }

    /**
     * 分页查询。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询",description = "管理平台-分页查询客户端安装包信息")
    public PageResponse<ClientInfoDO> page(@RequestBody PageQueryCondition<ClientInfoDO> query) {
        return PageUtil.of(clientInfoGateway.page(PageUtil.of(query)));
    }

}