package cn.com.voyah.material.web.user;

import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.client.BannerInfoService;
import cn.com.voyah.material.domain.entity.BannerInfoDO;
import cn.com.voyah.material.dto.BannerInfoDTO;
import cn.com.voyah.material.dto.MultiResponse;
import cn.com.voyah.material.dto.SingleResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

/**
 * banner表 控制层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@CatchAndLog
@RestController
@Tag(name = "banner接口(使用平台)")
@RequestMapping("/use-platform/banner-info")
public class UserPlatformBannerInfoController {

    @Autowired
    private BannerInfoService bannerInfoService;

    /**
     * 查询所有banner表。
     *
     * @return 所有数据
     */
    @PostMapping("list")
    @Operation(summary = "查询所有banner",description = "使用平台-查询list列表")
    public MultiResponse<BannerInfoDTO> list(@RequestBody(required = false) BannerInfoDO bannerInfoDO) {
        if(ObjectUtils.isEmpty(bannerInfoDO)){
            bannerInfoDO=new BannerInfoDO();
        }
        bannerInfoDO.setStatus(1);
        return MultiResponse.of(bannerInfoService.list(bannerInfoDO));
    }

//    /**
//     * 根据banner表主键获取详细信息。
//     *
//     * @param id banner表主键
//     * @return banner表详情
//     */
//    @GetMapping("by/{id}")
//    @Operation(summary = "根据主键获取banner")
//    public SingleResponse<BannerInfoDTO> getInfo(@PathVariable(name = "id") Long id) {
//        return SingleResponse.of(bannerInfoService.getById(id));
//    }

}