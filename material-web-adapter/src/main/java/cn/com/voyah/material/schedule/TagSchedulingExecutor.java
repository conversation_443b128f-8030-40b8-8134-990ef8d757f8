package cn.com.voyah.material.schedule;

import cn.com.voyah.material.client.MaterialTagService;
import cn.com.voyah.material.domain.gateway.TagInfoGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.List;


@Slf4j
@Component
@ConditionalOnExpression("${manage.scheduling.tag.active:false}")
public class TagSchedulingExecutor {

    @Autowired
    private MaterialTagService materialTagService;
    @Autowired
    private TagInfoGateway tagInfoGateway;
    @Value("${manage.notice.expire.day:3}")
    private Integer toExpireDays;
    @Scheduled(cron = "${manage.tag.cron.express:0 10 0 * * ?}")
    public void taskExpired() {
        List<Long> existTagIdList=materialTagService.existTagIdList();
        if(!CollectionUtils.isEmpty(existTagIdList)){
            tagInfoGateway.updateHiddenStatusByExistTagIdList(existTagIdList);
        }
    }

}
