//package cn.com.voyah.material.es.controller;
//
//
//import cn.com.voyah.material.catchlog.CatchAndLog;
//import cn.com.voyah.material.dto.SingleResponse;
//import cn.com.voyah.material.es.index.OriginMaterialEs;
//import cn.com.voyah.material.es.mapper.EsOriginMaterialMapper;
//import cn.com.voyah.material.es.service.EsOriginMaterialService;
//import cn.com.voyah.material.es.vo.OriginMaterialEsVO;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.io.IOException;
//import java.util.List;
//
//@CatchAndLog
//@RestController
//@Tag(name = "es 同步接口(管理平台)")
//@RequestMapping("/es")
//public class EsController {
//
//
//    @Autowired
//    EsOriginMaterialMapper esOriginMaterialMapper;
//
//    @Autowired
//    EsOriginMaterialService esOriginMaterialService;
//
//    @GetMapping("/createIndex")
//    @Operation(summary = "创建索引",description = "创建索引")
//    public Boolean createIndex() {
//        // 1.初始化-> 创建索引(相当于mysql中的表)
//        return esOriginMaterialMapper.createIndex();
//    }
//
//    @PostMapping("/sync")
//    @Operation(summary = "es数据同步",description = "es数据同步")
//    public SingleResponse<Boolean> sync() {
//        esOriginMaterialService.sync();
//        return SingleResponse.of(true);
//    }
//
//    @GetMapping("/deleteIndex")
//    @Operation(summary = "es删除索引",description = "es删除索引")
//    public Boolean deleteIndex() {
//        // 1.初始化-> 创建索引(相当于mysql中的表)
//        return esOriginMaterialMapper.deleteIndex("origin_material");
//    }
//
//    @GetMapping("/match/{query}")
//    @Operation(summary = "模糊匹配查询",description = "模糊匹配查询")
//    public OriginMaterialEsVO match(@PathVariable("query")String query) throws IOException {
//        // 1.初始化-> 创建索引(相当于mysql中的表)
//        return esOriginMaterialService.match(query);
//    }
//
//
//
//
//
//}
