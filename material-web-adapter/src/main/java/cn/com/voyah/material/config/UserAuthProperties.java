package cn.com.voyah.material.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "spring.auth")
public class UserAuthProperties {

    /**
     * 可以添加不需要 拦截的地址
     */
    public List<String> permittedUrl = new ArrayList<>();
}