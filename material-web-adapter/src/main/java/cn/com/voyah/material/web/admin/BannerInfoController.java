package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.client.BannerInfoService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.update.SortChangeDTO;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.catchlog.CatchAndLog;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import cn.com.voyah.material.domain.entity.BannerInfoDO;
import cn.com.voyah.material.domain.gateway.BannerInfoGateway;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

import static cn.com.voyah.domain.entity.def.BannerInfoDef.BANNER_INFO;
import static cn.com.voyah.domain.entity.def.QuickAccessAreaDef.QUICK_ACCESS_AREA;

/**
 * banner表 控制层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@CatchAndLog
@RestController
@Tag(name = "banner接口(管理平台)")
@RequestMapping("/banner-info")
public class BannerInfoController {

    @Autowired
    private BannerInfoGateway bannerInfoGateway;

    @Autowired
    private BannerInfoService bannerInfoService;

    /**
     * 添加banner表。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存banner",description = "管理平台-新增banner信息")
    public SingleResponse<BannerInfoDO> save(@RequestBody BannerInfoDO bannerInfoDO) {
        bannerInfoDO.setSort(System.currentTimeMillis());
        return SingleResponse.of(bannerInfoGateway.save(bannerInfoDO));
    }

    /**
     * 根据主键删除banner表。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键banner表",description = "管理平台-删除banner信息")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(bannerInfoGateway.removeById(id));
    }

    /**
     * 根据主键更新banner表。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新banner表",description = "管理平台-修改banner信息")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description="banner表主键")BannerInfoDO bannerInfoDO) {
        return SingleResponse.of(bannerInfoGateway.updateById(bannerInfoDO));
    }

    /**
     * 查询所有banner表。
     *
     * @return 所有数据
     */
    @PostMapping("list")
    @Operation(summary = "查询所有banner",description = "管理平台-查询所有banner信息")
    public MultiResponse<BannerInfoDTO> list(@RequestBody(required = false) BannerInfoDO bannerInfoDO) {

        return MultiResponse.of(bannerInfoService.list(bannerInfoDO));
    }

    /**
     * 根据banner表主键获取详细信息。
     *
     * @param id banner表主键
     * @return banner表详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取banner",description = "查询banner详情")
    public SingleResponse<BannerInfoDTO> getInfo(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(bannerInfoService.getById(id));
    }

    /**
     * 分页查询banner表。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询banner",description = "管理平台-分页查询banner信息")
    public PageResponse<BannerInfoDTO> page(@RequestBody PageQueryCondition<BannerInfoDO> query) {
        if (StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(BANNER_INFO.SORT.getName());
        }
        query.setOrderDirection(CommonConstants.DESC);

        return PageUtil.of(bannerInfoService.page(query, TimeQueryInterval.buildFromPage(query)));
    }

    @PostMapping("{id}/sort-change")
    @Operation(summary = "排序变更",description = "管理平台-修改banner排序")
    public SingleResponse<Boolean> sortChange(@PathVariable(value = "id") Long id,@RequestBody SortChangeDTO sortChangeDTO) {
        sortChangeDTO.setId(id);
        return SingleResponse.of(bannerInfoService.sortChange(sortChangeDTO));
    }

}