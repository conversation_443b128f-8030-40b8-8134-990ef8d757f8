package cn.com.voyah.material.web.admin;

import cn.com.voyah.domain.entity.def.OriginMaterialDef;
import cn.com.voyah.domain.entity.def.TaskInfoDef;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.client.NoticeMessageService;
import cn.com.voyah.material.client.OriginMaterialService;
import cn.com.voyah.material.client.TaskInfoService;
import cn.com.voyah.material.config.MaterialConfig;
import cn.com.voyah.material.constants.NoticeState;
import cn.com.voyah.material.constants.NoticeType;
import cn.com.voyah.material.constants.TaskStatus;
import cn.com.voyah.material.domain.entity.NoticeMessageDO;
import cn.com.voyah.material.domain.entity.TaskInfoDO;
import cn.com.voyah.material.domain.gateway.TaskInfoGateway;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.util.PageUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 任务基本信息表 控制层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@CatchAndLog
@RestController
@Tag(name = "任务基本信息表接口(管理平台)")
@RequestMapping("/task-info")
public class TaskInfoController {

    @Autowired
    private TaskInfoGateway taskInfoGateway;
    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private OriginMaterialService originMaterialService;
    @Autowired
    private NoticeMessageService noticeMessageService;
    @Autowired
    private MaterialConfig materialConfig;

    /**
     * 添加任务基本信息表。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存任务基本信息表",description = "管理平台-添加点检任务")
    public SingleResponse<TaskInfoDO> save(@RequestBody @Parameter(description="任务基本信息表")TaskInfoDO taskInfoDO) {
        taskInfoDO.setActualFileCount(0);
        taskInfoDO.setTaskEndTime(LocalDateTime.of(taskInfoDO.getTaskEndTime().toLocalDate(),LocalDateTime.MAX.toLocalTime()).minusSeconds(1));
        TaskInfoDO result=taskInfoService.save(taskInfoDO);
        try{
            NoticeMessageDO noticeMessageDO=new NoticeMessageDO();
            noticeMessageDO.setShortId(taskInfoDO.getExecuteShortId());
            noticeMessageDO.setUserName(taskInfoDO.getExecuteBy());
            noticeMessageDO.setSource(0);
            noticeMessageDO.setSendTime(taskInfoDO.getCreateTime());
            noticeMessageDO.setState(NoticeState.UNREAD.getValue());
            noticeMessageDO.setType(NoticeType.TASK_CREATE.getValue());
            noticeMessageDO.setContent(NoticeType.TASK_CREATE.getContent());
            noticeMessageDO.setRelationId(result.getId());
            noticeMessageService.save(noticeMessageDO);
        }catch(Exception e){
            log.error(e.getMessage());
        }
        return SingleResponse.of(result);


    }

    /**
     * 根据主键删除任务基本信息表。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键任务基本信息表",description = "管理平台-删除点检任务")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Integer id) {
        return SingleResponse.of(taskInfoGateway.removeById(id));
    }

    /**
     * 根据主键更新任务基本信息表。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新任务基本信息表",description = "管理平台-更新点检任务")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description="任务基本信息表主键")TaskInfoDO taskInfoDO) {
        return SingleResponse.of(taskInfoGateway.updateById(taskInfoDO));
    }

    /**
     * 查询所有任务基本信息表。
     *
     * @return 所有数据
     */
    @PostMapping("list")
    @Operation(summary = "查询所有任务基本信息表",description = "管理平台-查询点检任务列表")
    public MultiResponse<TaskInfoDO> list(@RequestBody(required = false)TaskInfoDO taskInfoDO) {
        return MultiResponse.of(taskInfoService.list(taskInfoDO));
    }
    /**
     * 根据任务基本信息表主键获取详细信息。
     *
     * @param id 任务基本信息表主键
     * @return 任务基本信息表详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取任务基本信息表",description = "管理平台-查询点检任务详情")
    public SingleResponse<TaskInfoDO> getInfo(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(taskInfoGateway.getById(id));
    }
    /**
     * 根据任务基本信息表主键获取详细信息。
     *
     * @param id 任务基本信息表主键
     * @return 任务基本信息表详情
     */
    @PostMapping("/by/{id}/origin-material-page")
    @Operation(summary = "根据主键获取任务基本信息表",description = "管理平台-查询点检任务下的素材列表")
    public PageResponse<OriginMaterialExtensionDTO> getOriginMaterialPage(@PathVariable(name = "id") Long id,@RequestBody PageQueryCondition<GlobalOriginMaterialQuery> query) {
        if (StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(OriginMaterialDef.ORIGIN_MATERIAL.CREATE_TIME.getName());
        }
        if(ObjectUtils.isEmpty(query.getCondition())){
            GlobalOriginMaterialQuery globalOriginMaterialQuery=new GlobalOriginMaterialQuery();
            query.setCondition(globalOriginMaterialQuery);
        }
        if(CollectionUtil.isNotEmpty(query.getCondition().getFileSourceTypes())){
            List<String> fileSourceTypeList=query.getCondition().getFileSourceTypes();
            List<FileSourceType> fileSourceTypes = materialConfig.getFileSourceTypes();
            List<String> matchSuffixesList = new ArrayList<>();
            fileSourceTypes.stream().forEach(o -> {
                if (fileSourceTypeList.contains(o.getKey())) {
                    matchSuffixesList.addAll(o.getSuffixes());
                }
            });
            query.getCondition().setFileTypes(matchSuffixesList);
        }
        TaskInfoDO taskInfoDO=taskInfoGateway.getById(id);
        String createShortId=taskInfoDO.getExecuteShortId();
        query.getCondition().setCreateShortId(createShortId);
        query.getCondition().setHomeFolderId(taskInfoDO.getMaterialFoldId());
        TimeQueryInterval timeQueryInterval=new TimeQueryInterval();
        timeQueryInterval.setStartTime(taskInfoDO.getTaskStartTime());
        timeQueryInterval.setEndTime(ObjectUtils.isEmpty(taskInfoDO.getTaskFinishTime())?taskInfoDO.getTaskEndTime():taskInfoDO.getTaskFinishTime());
        return originMaterialService.getGlobalSearchPageInfo(query, timeQueryInterval);
    }

    /**
     * 分页查询任务基本信息表。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询任务基本信息表",description = "管理平台-分页查询点检任务")
    public PageResponse<TaskInfoDO> page(@RequestBody PageQueryCondition<TaskInfoDO> query) {
        if (StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(TaskInfoDef.TASK_INFO.CREATE_TIME.getName());
        }

        return PageUtil.of(taskInfoService.getPage(query,TimeQueryInterval.buildFromPage(query)));
    }


//    @PutMapping("finish/{id}")
//    @Operation(summary = "结束任务")
//    public SingleResponse<Boolean> finish(@PathVariable(value="id")Long id) {
//        TaskInfoDO taskInfoDO=taskInfoGateway.getById(id);
//        if(ObjectUtil.isEmpty(taskInfoDO)){
//            throw new BizException("该任务不存在!");
//        }
//        if(!TaskStatus.PROGRESS.getValue().equals(taskInfoDO.getTaskStatus())){
//            throw new BizException("只有进行中的任务可以结束!");
//        }
//        return SingleResponse.of(taskInfoService.updateStatus(id,TaskStatus.FINISH));
//    }
    @PutMapping("cancel/{id}")
    @Operation(summary = "取消任务",description = "管理平台-取消点检任务")
    public SingleResponse<Boolean> cancel(@PathVariable(value="id")Long id) {
        LocalDateTime now=LocalDateTime.now();
        TaskInfoDO taskInfoDO=taskInfoGateway.getById(id);
        if(ObjectUtil.isEmpty(taskInfoDO)){
            throw new BizException("该任务不存在!");
        }
        if(!TaskStatus.PROGRESS.getValue().equals(taskInfoDO.getTaskStatus())){
            throw new BizException("只有进行中的任务可以取消!");
        }
        return SingleResponse.of(taskInfoService.updateStatus(id,TaskStatus.CANCEL,now));
    }
}