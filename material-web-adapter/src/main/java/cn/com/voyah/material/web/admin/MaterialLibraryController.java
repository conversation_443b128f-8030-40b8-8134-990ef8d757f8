package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.api.MaterialLibraryService;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.domain.entity.MaterialLibraryDO;
import cn.com.voyah.material.domain.gateway.MaterialLibraryGateway;
import cn.com.voyah.material.dto.PageResponse;
import cn.com.voyah.material.dto.Response;
import cn.com.voyah.material.dto.SingleResponse;
import cn.com.voyah.material.dto.library.MaterialLibraryAdd;
import cn.com.voyah.material.dto.library.MaterialLibraryQuery;
import cn.com.voyah.material.dto.library.MaterialLibrarySortChange;
import cn.com.voyah.material.dto.library.MaterialLibraryUpdate;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.util.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 素材库 控制层。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@CatchAndLog
@RestController
@AllArgsConstructor
@Tag(name = "素材库类型（主文件夹）接口(管理平台)")
@RequestMapping("/material-library")
public class MaterialLibraryController {

    private MaterialLibraryGateway materialLibraryGateway;

    private MaterialLibraryService materialLibraryService;

    /**
     * 添加素材库。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存素材库",description = "管理平台-新增素材库")
    public SingleResponse<MaterialLibraryDO> save(@RequestBody @Parameter(description = "素材库")
                                                  @Valid MaterialLibraryAdd add) {
        return SingleResponse.of(materialLibraryService.saveLibrary(add));
    }

    /**
     * 根据主键删除素材库。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键素材库",description = "管理平台-删除素材库")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(materialLibraryGateway.removeById(id));
    }

    /**
     * 根据主键更新素材库。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新素材库",description = "管理平台-修改素材库")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description = "素材库主键") MaterialLibraryUpdate update) {
        return SingleResponse.of(materialLibraryService.updateLibrary(update));
    }

    /**
     * 根据素材库主键获取详细信息。
     *
     * @param id 素材库主键
     * @return 素材库详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取素材库",description = "管理平台-查询素材库详情")
    public SingleResponse<MaterialLibraryDO> getInfo(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(materialLibraryGateway.getById(id));
    }

    /**
     * 分页查询素材库。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询素材库",description = "管理平台-分页查询素材库")
    public PageResponse<MaterialLibraryDO> page(@RequestBody PageQueryCondition<MaterialLibraryQuery> query) {
        return PageUtil.of(materialLibraryService.page(query));
    }

    /**
     * 启用素材库。
     */
    @PostMapping("{id}/enable")
    @Operation(summary = "启用素材库",description = "管理平台-启用素材库")
    public Response enable(@PathVariable(name = "id") Long id) {
        materialLibraryGateway.enableOnDisable(id, true);
        return Response.buildSuccess();
    }

    /**
     * 停用素材库。
     */
    @PostMapping("{id}/disable")
    @Operation(summary = "停用素材库",description = "管理平台-停用素材库")
    public Response disable(@PathVariable(name = "id") Long id) {
        materialLibraryGateway.enableOnDisable(id, false);
        return Response.buildSuccess();
    }

    /**
     * 排序变更。
     */
    @PostMapping("/sort-change")
    @Operation(summary = "排序变更",description = "管理平台-修改素材库排序")
    public Response sortChange(@RequestBody MaterialLibrarySortChange sortChange) {
        materialLibraryService.sortChange(sortChange);
        return Response.buildSuccess();
    }

}