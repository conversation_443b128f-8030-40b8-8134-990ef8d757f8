package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.config.MaterialConfig;
import cn.com.voyah.material.dto.FileSourceType;
import cn.com.voyah.material.dto.MultiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@CatchAndLog
@RestController
@AllArgsConstructor
@Tag(name = "公用接口(管理平台)")
@RequestMapping("/common")
public class CommonController {
    private MaterialConfig materialConfig;

    @GetMapping("/file-type")
    @Operation(summary = "获取文件类型",description = "获取文件类型")
    public MultiResponse<FileSourceType> getFileType() {
        return MultiResponse.of(materialConfig.getFileSourceTypes());
    }
}
