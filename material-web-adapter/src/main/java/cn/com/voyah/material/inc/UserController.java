package cn.com.voyah.material.inc;

import com.iov.tencent.inc.access.controller.IncBaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@Tag(name = "SDK API")
public class UserController extends IncBaseController {

    @Override
    public String getToken(HttpServletRequest servletRequest) {
        if(servletRequest.getCookies() == null){
            return null;
        }
        return super.getToken(servletRequest);
    }
}
