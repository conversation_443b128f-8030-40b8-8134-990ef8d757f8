package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.client.MaterialTagService;
import cn.com.voyah.material.client.TagCategoryService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.query.TagCategoryQuery;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.com.voyah.material.domain.entity.TagCategoryDO;
import cn.com.voyah.material.domain.gateway.TagCategoryGateway;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import java.util.List;

/**
 *  控制层。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@CatchAndLog
@RestController
@Tag(name = "标注类目相关接口(管理平台)")
@RequestMapping("/tag-category")
public class TagCategoryController {

    @Autowired
    private TagCategoryGateway tagCategoryGateway;

    @Autowired
    private TagCategoryService tagCategoryService;

    @Autowired
    private MaterialTagService materialTagService;

    /**
     * 添加。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存",description = "管理平台-添加标签类目信息")
    public SingleResponse<TagCategoryDO> save(@RequestBody @Parameter(description="")TagCategoryDO tagCategoryDO) {
        return SingleResponse.of(tagCategoryService.save(tagCategoryDO));
    }

    /**
     * 根据主键删除。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键",description = "管理平台-删除标签类目信息")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(tagCategoryGateway.removeById(id));
    }

    /**
     * 根据主键更新。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新",description = "管理平台-更新标签类目信息")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description="主键")TagCategoryDO tagCategoryDO) {
        return SingleResponse.of(tagCategoryService.updateById(tagCategoryDO));
    }

    /**
     * 查询所有。
     *
     * @return 所有数据
     */
    @GetMapping("list")
    @Operation(summary = "查询所有",description = "管理平台-查询标签类目列表")
    public MultiResponse<TagCategoryDO> list(@RequestParam(value = "materialId",required = false) Long materialId) {
        List<TagCategoryDO> list=tagCategoryService.getList(null);
        if(StringUtils.isEmpty(materialId)){
            return MultiResponse.of(list);
        }
        List<Long> selectIds=materialTagService.getCategoryIdList(materialId);
        if(CollectionUtil.isNotEmpty(selectIds)){
            list.stream().forEach(each->{
                if(selectIds.contains(each.getId())&&CommonConstants.DISABLE.equals(each.getStatus())){
                    each.setStatus(CommonConstants.ENABLE);
                }
            });
        }
        return MultiResponse.of(list);
    }

    /**
     * 根据主键获取详细信息。
     *
     * @param id 主键
     * @return 详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取",description = "管理平台-查询单条标签类目详细信息")
    public SingleResponse<TagCategoryDO> getInfo(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(tagCategoryGateway.getById(id));
    }

    /**
     * 分页查询。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询",description = "管理平台-分页查询标签类目")
    public PageResponse<TagCategoryDTO> page(@RequestBody PageQueryCondition<TagCategoryQuery> query) {
        return PageUtil.of(tagCategoryService.page(query));
    }

    @PostMapping("/get-all-tag-merge-tree")
    @Operation(summary = "查询所有标签根据标签名和标签值合并后的树形结构")
    public MultiResponse<TagCategoryTreeDTO> getAllTagMergeVoTree(@RequestParam(value="categoryId",required = false)Long categoryId) {
        return MultiResponse.of(tagCategoryService.getAllTagMergeTreeList(categoryId));
    }


}