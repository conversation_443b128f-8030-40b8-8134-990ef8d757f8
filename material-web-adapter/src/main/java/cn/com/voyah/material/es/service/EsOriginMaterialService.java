//package cn.com.voyah.material.es.service;
//
//
//import cn.com.voyah.material.dto.GlobalOriginMaterialQuery;
//import cn.com.voyah.material.dto.query.PageQueryCondition;
//import cn.com.voyah.material.es.index.OriginMaterialEs;
//import cn.com.voyah.material.es.vo.OriginMaterialEsVO;
//import cn.com.voyah.material.pojo.OriginMaterialPO;
//import org.dromara.easyes.core.biz.EsPageInfo;
//
//import java.io.IOException;
//import java.util.List;
//
///**
// * 简历表
// *
// * <AUTHOR>
// * @date 2023-05-09 21:22:25
// */
//public interface EsOriginMaterialService {
//    EsPageInfo<OriginMaterialPO> search(PageQueryCondition<GlobalOriginMaterialQuery> query );
////    List<Score> searchIdList(ResumeQueryDTO dto);
//    void sync();
//
//
//    OriginMaterialEsVO match(String query) throws IOException;
//
////    void update();
//}
//
