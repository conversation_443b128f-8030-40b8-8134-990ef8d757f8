package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.dto.MultiResponse;
import cn.com.voyah.material.dto.PageResponse;
import cn.com.voyah.material.dto.SingleResponse;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import cn.com.voyah.material.domain.entity.FilePartDO;
import cn.com.voyah.material.domain.gateway.FilePartGateway;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * 文件分片信息表，仅在手动分片上传时使用 控制层。
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@CatchAndLog
@RestController
@Tag(name = "文件分片信息表，仅在手动分片上传时使用接口(管理平台)")
@RequestMapping("/file-part")
public class FilePartController {

    @Autowired
    private FilePartGateway filePartGateway;

    /**
     * 添加文件分片信息表，仅在手动分片上传时使用。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存文件分片信息表，仅在手动分片上传时使用")
    public SingleResponse<FilePartDO> save(@RequestBody @Parameter(description="文件分片信息表，仅在手动分片上传时使用")FilePartDO filePartDO) {
        return SingleResponse.of(filePartGateway.save(filePartDO));
    }

    /**
     * 根据主键删除文件分片信息表，仅在手动分片上传时使用。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键文件分片信息表，仅在手动分片上传时使用")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(filePartGateway.removeById(id));
    }

    /**
     * 根据主键更新文件分片信息表，仅在手动分片上传时使用。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新文件分片信息表，仅在手动分片上传时使用")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description="文件分片信息表，仅在手动分片上传时使用主键")FilePartDO filePartDO) {
        return SingleResponse.of(filePartGateway.updateById(filePartDO));
    }

    /**
     * 查询所有文件分片信息表，仅在手动分片上传时使用。
     *
     * @return 所有数据
     */
    @GetMapping("list")
    @Operation(summary = "查询所有文件分片信息表，仅在手动分片上传时使用")
    public MultiResponse<FilePartDO> list() {
        return MultiResponse.of(filePartGateway.list());
    }

    /**
     * 根据文件分片信息表，仅在手动分片上传时使用主键获取详细信息。
     *
     * @param id 文件分片信息表，仅在手动分片上传时使用主键
     * @return 文件分片信息表，仅在手动分片上传时使用详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取文件分片信息表，仅在手动分片上传时使用")
    public SingleResponse<FilePartDO> getInfo(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(filePartGateway.getById(id));
    }

    /**
     * 分页查询文件分片信息表，仅在手动分片上传时使用。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询文件分片信息表，仅在手动分片上传时使用")
    public PageResponse<FilePartDO> page(@RequestBody PageQueryCondition<FilePartDO> query) {
        return PageUtil.of(filePartGateway.page(PageUtil.of(query)));
    }

}