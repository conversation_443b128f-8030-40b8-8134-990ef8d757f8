package cn.com.voyah.material.config;
import cn.com.voyah.material.dto.Response;
import com.iov.tencent.inc.access.aop.IncAopMethod;
import com.iov.tencent.inc.access.aop.IncAuthAopAspect;
import com.iov.tencent.inc.access.aop.IncBaseAopAspect;
import com.iov.tencent.inc.access.model.comm.IncResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;


@Aspect
@Component
@Slf4j
@ConditionalOnExpression("${user.auth:true}")
public class UserAuthAopAspect extends IncBaseAopAspect implements IncAopMethod {
    /**
     * 可以添加不需要 拦截的地址
     */
    private final UserAuthProperties userAuthProperties;

    public UserAuthAopAspect(UserAuthProperties userAuthProperties) {
        this.userAuthProperties = userAuthProperties;

        this.userAuthProperties.getPermittedUrl().addAll(IncAuthAopAspect.permittedUrl);
    }


    @Override
    @Pointcut("execution(public * cn.com.voyah.material.web.*.*Controller.*(..))")
    public void verify() {

    }

    @Override
    @Around("verify()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        Object o = defaultIncAuthFilter(joinPoint, userAuthProperties.getPermittedUrl());
        if (o instanceof IncResponse<?> incResponse) {
            MethodSignature ms = (MethodSignature) joinPoint.getSignature();
            Class<?> returnType = ms.getReturnType();
            Response response = (Response) returnType.getDeclaredConstructor().newInstance();
            return response.failure(incResponse.getCode(), incResponse.getMsg());
        }
        return o;
    }




    @Override
    @After("verify()")
    public void afterAop() {
//        defaultCleanThreadLocal();
    }

    @Override
    public String getTokenFromBody(Object[] args) {
        return null;
    }

}
