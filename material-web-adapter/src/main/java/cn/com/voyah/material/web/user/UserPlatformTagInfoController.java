package cn.com.voyah.material.web.user;

import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.client.TagInfoService;
import cn.com.voyah.material.domain.entity.TagInfoDO;
import cn.com.voyah.material.dto.MultiResponse;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import cn.com.voyah.material.web.dto.TagInfoKeyDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 *  控制层。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@CatchAndLog
@RestController
@Tag(name = "标签基本信息接口(使用平台)")
@RequestMapping("/use-platform/tag-info")
public class UserPlatformTagInfoController {

    @Autowired
    private TagInfoService tagInfoService;
    @Value("${tag.user.filter.ignore:忽略,车型名称}")
    private List<String> tagUserFilterIgnores;


    @PostMapping("/get-all-tag-merge-list")
    @Operation(summary = "查询所有标签根据标签名和标签值合并后的列表",description = "使用平台-查询标签列表")
    public MultiResponse<TagInfoMergeVO> getAllTagMergeVoList() {
        List<TagInfoMergeVO> list=tagInfoService.getAllTagMergeVoList(null,true);
        //过滤 车型名称
        List<TagInfoMergeVO> resultList=new ArrayList<>();
        for (TagInfoMergeVO each:list) {
            if(!tagUserFilterIgnores.contains(each.getTagName())){
                resultList.add(each);
            }
        }
        return MultiResponse.of(resultList);
    }


    /**
     * 查询所有。
     *
     * @return 所有数据
     */
    @PostMapping("/like-value-list")
    @Operation(summary = "模糊匹配根据输入项",description = "模糊匹配根据输入项")
    public MultiResponse<TagInfoDO> list(@RequestBody TagInfoKeyDTO dto) {

        return MultiResponse.of(tagInfoService.likeValueList(dto.getQuery()));
    }


}