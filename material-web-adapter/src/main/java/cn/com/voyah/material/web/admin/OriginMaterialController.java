package cn.com.voyah.material.web.admin;

import cn.com.voyah.domain.entity.def.OriginMaterialDef;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.client.*;
import cn.com.voyah.material.config.MaterialConfig;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.constants.EventTypeEnum;
import cn.com.voyah.material.domain.config.CosConfig;
import cn.com.voyah.material.domain.entity.MaterialTagDO;
import cn.com.voyah.material.domain.entity.OriginMaterialDO;
import cn.com.voyah.material.domain.event.DomainEvent;
import cn.com.voyah.material.domain.gateway.MaterialTagItemGateway;
import cn.com.voyah.material.domain.gateway.OriginMaterialGateway;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.util.JsonUtil;
import cn.com.voyah.material.util.MaterialFileUtils;
import cn.com.voyah.material.utils.SpringContextHolder;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import cn.com.voyah.material.vo.user.OriginMaterialExtensionVO;
import cn.com.voyah.material.web.dto.TagValueDTO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 素材 控制层。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Slf4j
@CatchAndLog
@RestController
@Tag(name = "素材接口(管理平台)")
@RequestMapping("/origin-material")
public class OriginMaterialController {

    @Autowired
    private OriginMaterialGateway originMaterialGateway;

    @Autowired
    private OriginMaterialService originMaterialService;



    @Autowired
    private ThFileService thFileService;

    @Autowired
    @Qualifier("cosService")
    private FileService fileService;
    @Resource
    private CosConfig cosConfig;


    @Autowired
    private BannerInfoService bannerInfoService;

    @Autowired
    private QuickAccessAreaService quickAccessAreaService;
    @Autowired
    private MaterialConfig materialConfig;

    @Autowired
    private MaterialTagService materialTagService;

    @Autowired
    private MaterialTagItemGateway materialTagItemGateway;

    /**
     * 添加素材。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存素材", description = "管理平台-添加素材")
    public SingleResponse<OriginMaterialDO> save(@RequestBody @Parameter(description = "素材") OriginMaterialDTO originMaterialDTO) {
        OriginMaterialDO result = originMaterialService.save(originMaterialDTO);
        try {
            DomainEvent<String> domainEvent = new DomainEvent<>(EventTypeEnum.MATERIAL_ADD, JsonUtil.writeValueAsString(result));
            SpringContextHolder.publishEvent(domainEvent);
        } catch (Exception e) {
            log.error("发布事件失败，", e);
        }
        return SingleResponse.of(result);
    }


    @PostMapping("moveList")
    @Operation(summary = "移动素材（批量）", description = "管理平台-移动素材（批量）")
    public SingleResponse<Boolean> move(@RequestBody @Parameter(description = "素材") MaterialMoveListDTO dto) {

        Boolean res = originMaterialService.moveList(dto);
        try {
            dto.getMoveList().stream().forEach(t -> {
                DomainEvent<String> domainEvent = new DomainEvent<>(EventTypeEnum.MATERIAL_MOVE, JsonUtil.writeValueAsString(t));
                SpringContextHolder.publishEvent(domainEvent);
            });

        } catch (Exception e) {
            log.error("发布事件失败，", e);
        }
        return SingleResponse.of(res);
    }

    /**
     * 全局分页查询素材。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("global-search-page")
    @Operation(summary = "全局分页查询素材", description = "管理平台-全局搜索分页查询素材")
    public PageResponse<OriginMaterialExtensionDTO> globalSearchPage(@RequestBody PageQueryCondition<GlobalOriginMaterialQuery> query) {
        if (StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(OriginMaterialDef.ORIGIN_MATERIAL.CREATE_TIME.getName());
        }
        if (ObjectUtils.isEmpty(query.getCondition())) {
            GlobalOriginMaterialQuery globalOriginMaterialQuery = new GlobalOriginMaterialQuery();
            query.setCondition(globalOriginMaterialQuery);
        }
        if (CollectionUtil.isNotEmpty(query.getCondition().getFileSourceTypes())) {
            List<String> fileSourceTypeList = query.getCondition().getFileSourceTypes();
            List<FileSourceType> fileSourceTypes = materialConfig.getFileSourceTypes();
            List<String> matchSuffixesList = new ArrayList<>();
            fileSourceTypes.stream().forEach(o -> {
                if (fileSourceTypeList.contains(o.getKey())) {
                    matchSuffixesList.addAll(o.getSuffixes());
                }
            });
            query.getCondition().setFileTypes(matchSuffixesList);
        }
        return originMaterialService.getGlobalSearchPageInfo(query, TimeQueryInterval.buildFromMaterialQuery(query.getCondition()));
    }

    /**
     * 分页查询素材。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询素材", description = "管理平台-分页查询素材")
    public PageResponse<OriginMaterialExtensionDTO> page(@RequestBody PageQueryCondition<OriginMaterialExtensionDTO> query) {
        if (StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(OriginMaterialDef.ORIGIN_MATERIAL.CREATE_TIME.getName());
        }
        return originMaterialService.getPageInfo(query);
    }

    /**
     * 根据主键删除素材。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键删除素材", description = "管理平台-删除素材")
    public SingleResponse<Boolean> remove(@PathVariable @Parameter(description = "素材主键") Long id) {
        OriginMaterialDO before = originMaterialGateway.getById(id);
        if (before == null) {
            return SingleResponse.of(true);
        }
        originMaterialService.removeIdAndChildAll(before);
        try {
            DomainEvent<String> domainEvent = new DomainEvent<>(EventTypeEnum.MATERIAL_REMOVE, JsonUtil.writeValueAsString(before));
            SpringContextHolder.publishEvent(domainEvent);
        } catch (Exception e) {
            log.error("发布事件失败，", e);
        }
        return SingleResponse.of(true);
    }

    /**
     * 根据主键更新素材。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新单个素材", description = "管理平台-修改素材信息")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description = "素材主键") OriginMaterialDO originMaterialDO) {
        return SingleResponse.of(originMaterialService.updateById(originMaterialDO));
    }

    /**
     * 查询所有素材。
     *
     * @return 所有数据
     */
    @PostMapping("list")
    @Operation(summary = "查询所有素材列表", description = "管理平台-查询所有素材列表")
    public MultiResponse<OriginMaterialExtensionDTO> list(@RequestBody OriginMaterialExtensionDTO originMaterialExtensionDTO) {
        originMaterialExtensionDTO.setIsShow(CommonConstants.ENABLE);
        return MultiResponse.of(originMaterialService.getList(originMaterialExtensionDTO, false));
    }

    /**
     * 根据素材主键获取详细信息。
     *
     * @param id 素材主键
     * @return 素材详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取单个素材", description = "管理平台-查询素材详细信息")
    public SingleResponse<OriginMaterialExtensionDTO> getInfo(@PathVariable Long id) {
        OriginMaterialExtensionDTO originMaterialExtensionDTO = originMaterialService.getDetailById(id);
        if (ObjectUtil.isNotEmpty(originMaterialExtensionDTO) && !originMaterialExtensionDTO.getIsDir()) {
            String signUrl = fileService.getObjectUrl(originMaterialExtensionDTO.getFilename());
            originMaterialExtensionDTO.setSignedUrl(signUrl);
        }
        if (CommonConstants.TAG_STATUS_TAG.equals(originMaterialExtensionDTO.getTagStatus())) {
            MaterialTagDO materialTagDO = materialTagService.getBaseByMaterialId(id);
            if (ObjectUtil.isNotEmpty(materialTagDO)) {
                originMaterialExtensionDTO.setTagCreateBy(materialTagDO.getCreateBy());
                originMaterialExtensionDTO.setTagCreateShortId(materialTagDO.getCreateShortId());
                originMaterialExtensionDTO.setTagCreateTime(materialTagDO.getCreateTime());
            }
        }

        return SingleResponse.of(originMaterialExtensionDTO);
    }

    @GetMapping("/parent-list/{id}")
    @Operation(summary = "更具素材id查询父级List", description = "管理平台-查询素材父级文件夹列表信息")
    public MultiResponse<OriginMaterialDO> parentList(@PathVariable Long id) {
        return MultiResponse.of(originMaterialService.getParentList(id));
    }

    @PostMapping("/generate-file-name")
    @Operation(summary = "素材生成不重复的cos-key", description = "管理平台-为素材生成唯一素材名称")
    public SingleResponse<OriginMaterialExtensionDTO> generateFileName(@RequestBody OriginMaterialExtensionDTO originMaterialExtensionDTO) {
        OriginMaterialExtensionDTO originMaterialDTO = originMaterialService.generateFileName(originMaterialExtensionDTO);
        StringBuilder sb = new StringBuilder();
        sb.append(cosConfig.getPrefix());
        if (ObjectUtil.isNotEmpty(originMaterialExtensionDTO.getParentId())) {
            List<OriginMaterialDO> parentList = originMaterialService.getParentList(originMaterialExtensionDTO.getParentId());
            if (!CollectionUtil.isEmpty(parentList)) {
                parentList.forEach(each -> {
                    sb.append(each.getMaterialName());
                    sb.append(CommonConstants.DIR_SEPRATE);
                });
            }
        }
        Long timeStamp = System.currentTimeMillis();
        sb.append(originMaterialDTO.getMaterialName());
        originMaterialDTO.setFilename(sb.toString());
        originMaterialDTO.setOriginalFilename(originMaterialExtensionDTO.getOriginalFilename());
        String thFileName = MaterialFileUtils.generateThFileName(sb.toString(), timeStamp);
        originMaterialDTO.setThFilename(thFileName);
        return SingleResponse.of(originMaterialDTO);
    }

    @PostMapping("/generate-file-name-special")
    @Operation(summary = "banner或者金刚位生成不重复的cos-key")
    public SingleResponse<OriginMaterialExtensionDTO> generateFileNameSpecial(@RequestBody SpecialMaterialDTO specialMaterialDTO) {
        String originName = specialMaterialDTO.getOriginalFilename();
        String finalName = null;
        if (1 == specialMaterialDTO.getSpecialType()) {
            finalName = bannerInfoService.generateFileName(cosConfig.getPrefix(), originName);
        } else {
            finalName = quickAccessAreaService.generateFileName(cosConfig.getPrefix(), originName);
        }
        OriginMaterialExtensionDTO result = new OriginMaterialExtensionDTO();
        result.setFilename(finalName);
        result.setOriginalFilename(originName);
        String thFileName = MaterialFileUtils.generateThFileName(finalName, System.currentTimeMillis());
        result.setThFilename(thFileName);
        return SingleResponse.of(result);
    }


    @GetMapping("/{parentId}/generate-key-prefix")
    @Operation(summary = "查询文件存储key前缀", description = "管理平台-查询文件存储key前缀")
    public SingleResponse<String> generateKeyPrefix(@PathVariable Long parentId) {
        StringBuilder sb = new StringBuilder();
        sb.append(cosConfig.getPrefix());
        List<OriginMaterialDO> parentList = originMaterialService.getParentList(parentId);
        if (!CollectionUtil.isEmpty(parentList)) {
            parentList.forEach(each -> {
                sb.append(each.getMaterialName());
                sb.append(CommonConstants.DIR_SEPRATE);
            });
        }
        return SingleResponse.of(sb.toString());
    }

    @PostMapping("/{parentId}/fold-upload")
    @Operation(summary = "文件夹列表基本信息上传接口", description = "管理平台-文件夹列表基本信息上传接口")
    public void foldUpload(@PathVariable Long parentId, @RequestBody List<OriginMaterialExtensionDTO> list) {
        originMaterialService.foldUpload(parentId, list);
    }


    @PutMapping("/update-public-status/{id}")
    @Operation(summary = "修改公开状态 isPublic integer类型 1 公开  0 非公开", description = "管理平台-修改素材公开状态")
    public SingleResponse<Boolean> updatePublicStatus(@PathVariable Long id, @RequestParam(value = "isPublic") Integer isPublic) {
        return SingleResponse.of(originMaterialService.updatePublicStatusById(id, isPublic));
    }

    @PutMapping("/update-public-status-batch")
    @Operation(summary = "（批量）修改公开状态 isPublic integer类型 1 公开  0 非公开", description = "管理平台-（批量）修改素材公开状态")
    public SingleResponse<Boolean> updatePublicStatusBatch(@RequestBody UpdatePublicBatchDTO dto) {
        return SingleResponse.of(originMaterialService.updatePublicStatusBatch(dto));
    }


    @PostMapping("tag-search-page")
    @Operation(summary = "根据标注查询素材", description = "管理平台-根据标注查询素材")
    public PageResponse<OriginMaterialExtensionDTO> getTagSearchPage(@RequestBody PageQueryCondition<GlobalOriginMaterialQuery> query) {
        if (!StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(com.mybatisflex.core.util.StringUtil.camelToUnderline(query.getOrderBy()));
        } else {
            query.setOrderBy(OriginMaterialDef.ORIGIN_MATERIAL.CREATE_TIME.getName());
        }
        if (ObjectUtils.isEmpty(query.getCondition())) {
            GlobalOriginMaterialQuery globalOriginMaterialQuery = new GlobalOriginMaterialQuery();
            query.setCondition(globalOriginMaterialQuery);
        }
        query.getCondition().setIsPublic(CommonConstants.PUBLIC_STATUS_PUBLIC);
        if (CollectionUtil.isNotEmpty(query.getCondition().getFileSourceTypes())) {
            List<String> fileSourceTypeList = query.getCondition().getFileSourceTypes();
            List<FileSourceType> fileSourceTypes = materialConfig.getFileSourceTypes();
            List<String> matchSuffixesList = new ArrayList<>();
            fileSourceTypes.stream().forEach(o -> {
                if (fileSourceTypeList.contains(o.getKey())) {
                    matchSuffixesList.addAll(o.getSuffixes());
                }
            });
            query.getCondition().setFileTypes(matchSuffixesList);
        }
        return originMaterialService.getGlobalSearchPageInfo(query, TimeQueryInterval.buildFromMaterialQuery(query.getCondition()), TimeQueryInterval.buildFromDateQueryInterval(query.getCondition().getTagCreateTimeInterval()));
    }

    @PostMapping("super-list")
    @Operation(summary = "查询所有素材", description = "管理平台-查询所有素材库")
    public MultiResponse<OriginMaterialExtensionDTO> superList(@RequestBody OriginMaterialExtensionDTO originMaterialExtensionDTO) {
        originMaterialExtensionDTO.setIsShow(CommonConstants.ENABLE);
        return MultiResponse.of(originMaterialService.getList(originMaterialExtensionDTO, true));
    }

    @PostMapping("get-ai-name-by-tag")
    @Operation(summary = "获取标签名称(AI) 前端传递tag 逗号拼接", description = "管理平台-获取标签名称(AI)")
    public SingleResponse<JSONObject> getAiNameByTag(@RequestBody @Validated TagValueDTO dto) {
        return SingleResponse.of(originMaterialService.getAiName(dto.getTag()));
    }

    @PostMapping("get-ai-name/{id}")
    @Operation(summary = "获取标签名称(AI)", description = "管理平台-获取标签名称(AI)")
    public SingleResponse<JSONObject> getAiName(@PathVariable Long id) {
        return SingleResponse.of(originMaterialService.getAiName(id));
    }

    @PostMapping("get-ai-desc/{id}")
    @Operation(summary = "获取标签描述(AI)", description = "管理平台-获取标签描述(AI)")
    public SingleResponse<JSONObject> getAiDesc(@PathVariable Long id) {
        return SingleResponse.of(originMaterialService.getAiDesc(id));
    }

    @PostMapping("get-ai-desc-by-tag")
    @Operation(summary = "获取标签描述(AI) 前端传递tag 逗号拼接", description = "管理平台-获取标签描述(AI)")
    public SingleResponse<JSONObject> getAiDesc(@RequestBody @Validated TagValueDTO dto) {
        return SingleResponse.of(originMaterialService.getAiDesc(dto.getTag()));
    }


//    @PostMapping("move")
//    @Operation(summary = "移动素材",description = "管理平台-移动素材")
//    public SingleResponse<Boolean> move(@RequestBody MaterialMoveDTO materialMoveDTO) {
//        boolean result=originMaterialService.move(materialMoveDTO);
//        try {
//            DomainEvent<String> domainEvent = new DomainEvent<>(EventTypeEnum.MATERIAL_MOVE, JsonUtil.writeValueAsString(materialMoveDTO));
//            SpringContextHolder.publishEvent(domainEvent);
//        } catch (Exception e) {
//            log.error("发布事件失败，", e);
//        }
//        return SingleResponse.of(result);
//    }


    @GetMapping("{id}/children-fold-tree")
    @Operation(summary = "根据文件夹id查询子文件夹树形结构", description = "管理平台-查询所有子集文件夹")
    public SingleResponse<MaterialTreeDTO> getChildrenFoldTree(@PathVariable Long id) {

        return SingleResponse.of(originMaterialService.getChildrenTree(id, true));
    }


    @GetMapping("{id}/children-all-tree")
    @Operation(summary = "根据文件夹id查询子文件及文件夹树形结构", description = "管理平台-查询所有子集素材")
    public SingleResponse<MaterialTreeDTO> getChildrenAllTree(@PathVariable Long id) {

        return SingleResponse.of(originMaterialService.getChildrenTree(id, false));
    }


    @GetMapping("detail/{id}")
    @Operation(summary = "根据主键获取单个素材详情", description = "管理平台-查询素材详细信息")
    public SingleResponse<OriginMaterialExtensionVO> getDetail(@PathVariable Long id) {
        OriginMaterialExtensionDTO originMaterialExtensionDTO = originMaterialService.getDetailById(id);
        if (ObjectUtil.isEmpty(originMaterialExtensionDTO)) {
            throw new BizException("该素材不存在!");
        }
        OriginMaterialExtensionVO originMaterialExtensionVO = new OriginMaterialExtensionVO();
        BeanUtils.copyProperties(originMaterialExtensionDTO, originMaterialExtensionVO);
        //浏览量加一
        materialTagService.addViewCount(id);
        MaterialTagDO materialTagDO = materialTagService.getBaseByMaterialId(id);
        if (ObjectUtil.isNotEmpty(materialTagDO)) {
            originMaterialExtensionVO.setTagRemark(materialTagDO.getRemark());
            List<TagInfoMergeVO> itemsList = materialTagItemGateway.getTagMergeVoList(id, null);
            originMaterialExtensionVO.setTags(itemsList);
            originMaterialExtensionVO.setUserViewCount(materialTagDO.getUserViewCount());
            originMaterialExtensionVO.setUserFavoriteCount(materialTagDO.getUserFavoriteCount());
        }
        List<Long> parentIdList = originMaterialExtensionDTO.getParentIdList();
        if (CollectionUtil.isNotEmpty(parentIdList) && parentIdList.size() > 1) {
            Optional<Long> optional = parentIdList.stream().filter(each -> !CommonConstants.HOME_FOLD_PARENT_ID.equals(each)).findFirst();
            if (optional.isPresent()) {
                OriginMaterialDO parent = originMaterialGateway.getById(optional.get());
                if (ObjectUtil.isNotEmpty(parent)) {
                    originMaterialExtensionVO.setLabName(parent.getMaterialName());
                }
            }
        }
        return SingleResponse.of(originMaterialExtensionVO);
    }
}