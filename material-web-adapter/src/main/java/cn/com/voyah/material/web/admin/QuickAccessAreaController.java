package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.client.QuickAccessAreaService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.entity.QuickAccessAreaDO;
import cn.com.voyah.material.domain.gateway.FileDetailGateway;
import cn.com.voyah.material.domain.gateway.QuickAccessAreaGateway;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.update.SortChangeDTO;
import cn.com.voyah.material.util.PageUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import static cn.com.voyah.domain.entity.def.QuickAccessAreaDef.QUICK_ACCESS_AREA;

/**
 * 金刚位表 控制层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@CatchAndLog
@RestController
@Tag(name = "金刚位接口(管理平台)")
@RequestMapping("/quick-access-area")
public class QuickAccessAreaController {

    @Autowired
    private QuickAccessAreaGateway quickAccessAreaGateway;
    @Autowired
    private QuickAccessAreaService quickAccessAreaService;


    @Autowired
    private FileDetailGateway fileDetailGateway;

    /**
     * 添加金刚位表。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存金刚位表",description = "管理平台-添加金刚位信息")
    public SingleResponse<QuickAccessAreaDO> save(@RequestBody @Parameter(description="金刚位表")QuickAccessAreaDO quickAccessAreaDO) {
        quickAccessAreaDO.setSort(System.currentTimeMillis());
        return SingleResponse.of(quickAccessAreaGateway.save(quickAccessAreaDO));
    }

    /**
     * 根据主键删除金刚位表。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键金刚位表",description = "管理平台-删除金刚位信息")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(quickAccessAreaGateway.removeById(id));
    }

    /**
     * 根据主键更新金刚位表。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新金刚位表",description = "管理平台-修改金刚位信息")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description="金刚位表主键")QuickAccessAreaDO quickAccessAreaDO) {
        return SingleResponse.of(quickAccessAreaGateway.updateById(quickAccessAreaDO));
    }

    /**
     * 查询所有金刚位表。
     *
     * @return 所有数据
     */
    @PostMapping("list")
    @Operation(summary = "查询所有金刚位表",description = "管理平台-查询金刚位列表")
    public MultiResponse<QuickAccessAreaDTO> list(@RequestBody(required = false)QuickAccessAreaDO quickAccessAreaDO) {
        return MultiResponse.of(quickAccessAreaService.list(quickAccessAreaDO));
    }

    /**
     * 根据金刚位表主键获取详细信息。
     *
     * @param id 金刚位表主键
     * @return 金刚位表详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取金刚位表",description = "管理平台-查询单条金刚位信息")
    public SingleResponse<QuickAccessAreaDTO> getInfo(@PathVariable(name = "id") Long id) {
        QuickAccessAreaDTO res = quickAccessAreaService.getById(id);
        if(ObjectUtil.isNotEmpty(res.getBackgroundFileId())){
            FileDetailDO file = fileDetailGateway.getById(res.getBackgroundFileId());
            res.setBackgroundUrl(file.getUrl());
            res.setBackgroundFilename(file.getFilename());
        }
        return SingleResponse.of(res);
    }

    /**
     * 分页查询金刚位表。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询金刚位表",description = "管理平台-分页查询金刚位信息")
    public PageResponse<QuickAccessAreaDTO> page(@RequestBody PageQueryCondition<QuickAccessAreaDO> query) {
        if (StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(QUICK_ACCESS_AREA.SORT.getName());
        }
        query.setOrderDirection(CommonConstants.DESC);
        return PageUtil.of(quickAccessAreaService.page(query, TimeQueryInterval.buildFromPage(query)));
    }

    @PostMapping("{id}/sort-change")
    @Operation(summary = "排序变更",description = "管理平台-修改金刚位排序")
    public SingleResponse<Boolean> sortChange(@PathVariable(value = "id") Long id,@RequestBody SortChangeDTO sortChangeDTO) {
        sortChangeDTO.setId(id);
        return SingleResponse.of(quickAccessAreaService.sortChange(sortChangeDTO));
    }

}