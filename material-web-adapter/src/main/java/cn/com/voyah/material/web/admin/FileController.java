package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.client.FileDetailService;
import cn.com.voyah.material.client.FileService;
import cn.com.voyah.material.client.HistoryTransferService;
import cn.com.voyah.material.client.ThFileService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.constants.FilePlatform;
import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.entity.FileDetailTmpDO;
import cn.com.voyah.material.domain.entity.dto.ResponseDTO;
import cn.com.voyah.material.domain.gateway.FileDetailGateway;
import cn.com.voyah.material.domain.gateway.FileDetailTmpGateway;
import cn.com.voyah.material.dto.SingleResponse;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.util.SpireThumbnailUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 文件记录表 控制层。
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@Tag(name = "文件接口(管理平台)")
@RequestMapping("/file")
@Slf4j
public class FileController {


    @Autowired
    @Qualifier("cosService")
    private FileService fileService;

    @Autowired
    private FileDetailService fileDetailService;


    @Autowired
    private ThFileService thFileService;


    @Operation(summary = "获取临时访问凭证",description = "管理平台-获取cos临时访问凭证")
    @GetMapping("/federation-token")
    public SingleResponse<ResponseDTO> fetch(){
        ResponseDTO result=null;
        try {
            result=fileService.getFederationToken();
            log.info(JSONUtil.toJsonStr(result));
        } catch (Exception e) {
            log.error("获取临时访问凭证告警！", e);
        }
        return SingleResponse.of(result);
    }


    @PostMapping("save")
    @Operation(summary = "保存文件本身记录表",description = "管理平台-上传文件")
    public SingleResponse<FileDetailDO> save(@RequestBody @Parameter(description="文件记录表") FileDetailDO fileDetailDO) {
        fileDetailDO.setPlatform(FilePlatform.TENCENT.name());
        String url=fileService.getObjectUrl(fileDetailDO.getFilename());
        fileDetailDO.setUrl(url);
        if(!StringUtils.isEmpty(fileDetailDO.getThContentType())){
            String thUrl=fileService.getObjectUrl(fileDetailDO.getThFilename());
            fileDetailDO.setThUrl(thUrl);
        }else{
            fileDetailDO.setThUrl(null);
            fileDetailDO.setThFilename(null);
            fileDetailDO.setThSize(0L);
        }

        FileDetailDO result=fileDetailService.save(fileDetailDO);
        return SingleResponse.of(result);
    }




    /**
     * 根据主键删除文件记录表。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键文件记录表",description = "管理平台-删除文件")
    public SingleResponse<Boolean> remove(@PathVariable @Parameter(description="文件记录表主键")Long id) {
        return SingleResponse.of(fileDetailService.removeById(id));
    }




    /**
     * 根据文件记录表主键获取详细信息。
     *
     * @param id 文件记录表主键
     * @return 文件记录表详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取文件记录表",description = "管理平台-查询文件详细信息")
    public SingleResponse<FileDetailDO> getInfo(@PathVariable Long id) {
        FileDetailDO fileDetailDO=new FileDetailDO();
        fileDetailDO.setId(id);
        return SingleResponse.of(fileDetailService.getOne(fileDetailDO));
    }

    @PostMapping("/generete-th/{fileId}")
    @Operation(summary = "缩略图片生成接口",description = "管理平台-生成文件缩略图")
    public SingleResponse<Boolean> generateThByFileId(@PathVariable("fileId") String fileId) throws Exception {
        thFileService.generateThByFileId(fileId);
        return SingleResponse.of(true);
    }




}