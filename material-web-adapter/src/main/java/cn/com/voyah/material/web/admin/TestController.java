package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.client.FileDetailService;
import cn.com.voyah.material.client.FileDetailTmpService;
import cn.com.voyah.material.client.FileService;
import cn.com.voyah.material.client.HistoryTransferService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.constants.EventTypeEnum;
import cn.com.voyah.material.constants.FilePlatform;
import cn.com.voyah.material.domain.config.CosConfig;
import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.entity.FileDetailTmpDO;
import cn.com.voyah.material.domain.entity.FilePartDO;
import cn.com.voyah.material.domain.entity.OriginMaterialDO;
import cn.com.voyah.material.domain.entity.migrate.MigrateEntityDO;
import cn.com.voyah.material.domain.event.DomainEvent;
import cn.com.voyah.material.domain.gateway.FileDetailGateway;
import cn.com.voyah.material.domain.gateway.FilePartGateway;
import cn.com.voyah.material.domain.gateway.OriginMaterialGateway;
import cn.com.voyah.material.dto.SingleResponse;
import cn.com.voyah.material.exception.BizException;
//import cn.com.voyah.material.util.ImageUtil;
import cn.com.voyah.material.util.JsonUtil;
//import cn.com.voyah.material.util.VideoUtils;
import cn.com.voyah.material.utils.SpringContextHolder;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@RestController
@Tag(name = "文件测试接口(管理平台)")
@RequestMapping("/file")
@Slf4j
@ConditionalOnExpression("${api.test.allow:false}")
public class TestController {
    @Autowired
    @Qualifier("cosService")
    private FileService fileService;

    @Autowired
    private FileDetailService fileDetailService;
    @Autowired
    private CosConfig cosConfig;

    @Autowired
    private FilePartGateway filePartGateway;
    @Autowired
    private FileDetailGateway fileDetailGateway;

    @Autowired
    private OriginMaterialGateway originMaterialGateway;
    @Autowired
    private HistoryTransferService historyTransferService;

    @Autowired
    private FileDetailTmpService fileDetailTmpService;
    //上传文件
    @PostMapping("/test/tmp/upload")
    public SingleResponse<FileDetailDO> uploadTest(@RequestParam("file") MultipartFile file, @RequestParam("newFileName") String newFileName){
        newFileName=cosConfig.getPrefix()+newFileName;
        FileDetailDO fileBaseInfo=fileService.upload(file,newFileName);
        return SingleResponse.of(fileBaseInfo);
    }

    @GetMapping("/test/tmp/download-url")
    public SingleResponse<Object> getDownLoadTest(@RequestParam("fileName") String fileName){
        Object url=fileService.getObjectUrl(fileName);
        return SingleResponse.of(url);
    }

    @GetMapping("/test/tmp/download-url2")
    public void getDownLoadTest2(@RequestParam("fileName") String fileName){
        String originDirPrefix="D:\\test\\tmp\\history\\video\\origin\\";
        historyTransferService.downloadFile(fileName,originDirPrefix+fileName);
    }
    @GetMapping("/test/tmp/download-url3")
    public void getDownLoadTest3(@RequestParam("fileName") String fileName){
        String originDirPrefix="D:\\test\\tmp\\history\\video\\origin\\";
        try {
            fileService.downloadFileTmpKey(fileName,originDirPrefix+"1.jpg");
        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }


    @PostMapping("/test/tmp/slice-upload-start")
    public SingleResponse<FileDetailDO> sliceUploadStart(@RequestParam("file") MultipartFile file, @RequestParam("newFileName") String newFileName) throws IOException {
        newFileName=cosConfig.getPrefix()+newFileName;
        FileDetailDO fileDetailDO=new FileDetailDO();
        fileDetailDO.setFilename(newFileName);
        FileDetailDO result=fileDetailService.getOne(fileDetailDO);
        if(ObjectUtils.isEmpty(result)){
            result=fileService.sliceUploadStart(file,newFileName);
            result=fileDetailService.save(result);
        }
        return SingleResponse.of(result);
    }

    @PostMapping("/test/tmp/slice-uploading/{uploadId}")
    public SingleResponse<FilePartDO> sliceUploading(@RequestParam("file") MultipartFile file, @PathVariable("uploadId") String uploadId, @RequestParam("newFileName") String newFileName, @RequestParam("partNum") int partNum) throws IOException {
        FilePartDO filePartDO=fileService.sliceUploading(file,uploadId,newFileName,partNum);
        filePartGateway.save(filePartDO);
        return SingleResponse.of(filePartDO);
    }

    @PostMapping("/test/tmp/slice-upload-finish/{uploadId}")
    public SingleResponse<FileDetailDO> sliceUploadFinish(@PathVariable("uploadId") String uploadId) throws IOException {
        FileDetailDO fileDetailDO=new FileDetailDO();
        fileDetailDO.setUploadId(uploadId);
        FileDetailDO result=fileDetailService.getOne(fileDetailDO);
        List<FilePartDO> filePartDOList=filePartGateway.getListByUploadId(uploadId);
        fileService.sliceUploadFinish(result.getFilename(),uploadId,filePartDOList);
        result.setUploadStatus(2);
        fileDetailGateway.updateById(fileDetailDO);
        return SingleResponse.of(fileDetailDO);
    }


    @PostMapping("/test/tmp/get-object-list/{dir}")
    public void getObjectLists(@PathVariable("dir") String dir) {
        Collection<MigrateEntityDO> resultList=fileService.getObjectLists(dir);
        List<OriginMaterialDO> originMaterialDOList=new ArrayList<>();
        int i=0;
        int j=0;
        for (MigrateEntityDO each:resultList) {
            OriginMaterialDO originMaterialDO=MigrateEntityDO.buildToOriginMaterialDO(each);
            if(!each.getIsDir()){
                FileDetailDO fileDetailDO=MigrateEntityDO.buildToFileDetailDO(each);
                fileDetailDO.setPlatform(FilePlatform.TENCENT.name());
                String url=fileService.getObjectUrl(fileDetailDO.getFilename());
                fileDetailDO.setUrl(url);
                fileDetailService.save(fileDetailDO);
                originMaterialDOList.add(originMaterialDO);
                originMaterialGateway.save(originMaterialDO);
                i++;
            }else{
                originMaterialGateway.save(originMaterialDO);
                j++;
            }

            System.out.println("====已同步"+i+"个文件,"+j+"个文件夹");
        }

        for (OriginMaterialDO each:originMaterialDOList) {
            try {
                DomainEvent<String> domainEvent = new DomainEvent<>(EventTypeEnum.MATERIAL_ADD, JsonUtil.writeValueAsString(each));
                SpringContextHolder.publishEvent(domainEvent);
            } catch (Exception e) {
                log.error("发布事件失败，", e);
            }
        }
    }


//    @PostMapping("/test/tmp/test/process-video")
//    public void testProcessVideo() throws Exception {
//        int batchSize=1;
//        List<String> contentType=new ArrayList<>();
//        contentType.add("m4v");
//        contentType.add("m4a");
//        contentType.add("mov");
//        contentType.add("wav");
//        contentType.add("mp3");
//        List<FileDetailTmpDO> fileDetailTmpList=fileDetailTmpService.selectList(batchSize,contentType);
//        String originDirPrefix="D:\\test\\tmp\\history\\video\\origin\\";
//        String thDirPrefix="D:\\test\\tmp\\history\\video\\th\\";
//        int i=1;
//        while(!CollectionUtils.isEmpty(fileDetailTmpList)){
//            for (FileDetailTmpDO each:fileDetailTmpList) {
//                Long timeStamp=System.currentTimeMillis();
//                String fileName=each.getFilename();
//                String originFileName=each.getOriginalFilename();
//                String thOriginFileName=getName(originFileName,timeStamp);
//                String thFileName=getName(fileName,timeStamp);
//                Long size=0L;
//                try{
//                    log.info("download start "+each.getFilename()+"==========================================");
//                    historyTransferService.downloadFile(each.getFilename(),originDirPrefix+originFileName);
//                    log.info("download finish "+each.getFilename()+"==========================================");
//                    VideoUtils.convertFirstFrame2(originDirPrefix+originFileName,thDirPrefix+thOriginFileName);
//                    fileService.upload(thDirPrefix+thOriginFileName,thFileName);
//                    size=historyTransferService.getFileSize(thDirPrefix+thOriginFileName);
//                    each.setStatus(1);
//                    historyTransferService.deleteLocalFile(originDirPrefix+originFileName);
//                    historyTransferService.deleteLocalFile(thDirPrefix+thOriginFileName);
//                }catch(Exception e){
//                    each.setStatus(-1);
//                    log.error(e.getMessage());
//                }
//                if(each.getStatus()==1){
//                    each.setThFilename(thFileName);
//                    String thUrl=fileService.getObjectUrl(thFileName);
//                    each.setThUrl(thUrl);
//                    each.setThContentType("jpeg");
//                    each.setThSize((long)size);
//                }
//            }
//            fileDetailTmpService.updateList(fileDetailTmpList);
//            log.info("trasfer total "+i*batchSize+"==========================================");
//            i++;
//            fileDetailTmpList=fileDetailTmpService.selectList(batchSize,contentType);
//        }
//        log.info("finish ==========================================");
//    }

//    @PostMapping("/test/tmp/test/process-pic")
//    public void testProcessPic() throws Exception {
//        int batchSize=2;
//        List<String> contentType=new ArrayList<>();
//        contentType.add("jpeg");
//        List<FileDetailTmpDO> fileDetailTmpList=fileDetailTmpService.selectList(batchSize,contentType);
//        String originDirPrefix="D:\\test\\tmp\\history\\pic\\origin\\";
//        String thDirPrefix="D:\\test\\tmp\\history\\pic\\th\\";
//        int i=1;
//        while(!CollectionUtils.isEmpty(fileDetailTmpList)){
//            for (FileDetailTmpDO each:fileDetailTmpList) {
//                Long timeStamp=System.currentTimeMillis();
//                String fileName=each.getFilename();
//                String originFileName=each.getOriginalFilename();
//                String thOriginFileName=getName(originFileName,timeStamp);
//                String thFileName=getName(fileName,timeStamp);
//                int size=0;
//                try{
//                    historyTransferService.downloadFile(each.getFilename(),originDirPrefix+originFileName);
//                    File file=new File(originDirPrefix+originFileName);
//                    InputStream ins=new FileInputStream(file);
//                    byte[] bytes=ImageUtil.compress(ins.readAllBytes(), 100*1024);
//                    size=bytes.length;
//                    FileOutputStream outputStream=new FileOutputStream(thDirPrefix+thOriginFileName);
//                    outputStream.write(bytes);
//                    outputStream.close();
//                    ins.close();
//                    fileService.upload(thDirPrefix+thOriginFileName,thFileName);
//                    each.setStatus(1);
//                    historyTransferService.deleteLocalFile(originDirPrefix+originFileName);
//                    historyTransferService.deleteLocalFile(thDirPrefix+thOriginFileName);
//                }catch(Exception e){
//                    each.setStatus(-1);
//                    log.error(e.getMessage());
//                }
//                if(each.getStatus()==1){
//                    each.setThFilename(thFileName);
//                    String thUrl=fileService.getObjectUrl(thFileName);
//                    each.setThUrl(thUrl);
//                    each.setThContentType("jpeg");
//                    each.setThSize((long)size);
//                }
//            }
//            fileDetailTmpService.updateList(fileDetailTmpList);
//            log.info("trasfer total "+i*batchSize+"==========================================");
//            i++;
//            fileDetailTmpList=fileDetailTmpService.selectList(batchSize,contentType);
//        }
//        log.info("finish ==========================================");
//    }
//
//
    public String getName(String originName,Long timeStamp){
        if (StringUtils.isEmpty(originName)) {
            throw new BizException("文件名不能为空");
        }
        int index = originName.lastIndexOf(CommonConstants.FILE_SEPRATE_SUFFIX);
        String prefix="";
        String suffix = ".jpg";
        if (index > 0) {
            prefix = prefix+originName.substring(0, index);
        } else {
            prefix = prefix+originName;
        }

        StringBuilder stringBuilder = new StringBuilder(prefix);
        stringBuilder.append(CommonConstants.FILE_DUPLICATION_SEPRATE);
        stringBuilder.append(timeStamp);
        stringBuilder.append(CommonConstants.FILE_DUPLICATION_SEPRATE);
        stringBuilder.append("th");
        if (!StringUtils.isEmpty(suffix)) {
            stringBuilder.append(suffix);
        }
        return stringBuilder.toString();
    }
//
//
//
//    @PostMapping("/test/tmp/test/process-video2")
//    public void testProcessVideo2() throws Exception {
//        String originDirPrefix="D:\\test\\tmp\\history\\video\\origin\\";
//        String thDirPrefix="D:\\test\\tmp\\history\\video\\th\\";
//        String originFileName="C4818.mp4";
//        String thOriginFileName="C4818_th.jpg";
//        VideoUtils.convertFirstFrame2(originDirPrefix+originFileName,thDirPrefix+thOriginFileName);
//    }

}
