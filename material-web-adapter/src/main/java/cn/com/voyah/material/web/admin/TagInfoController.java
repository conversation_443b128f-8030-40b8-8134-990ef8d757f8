package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.client.TagCategoryService;
import cn.com.voyah.material.client.TagInfoService;
import cn.com.voyah.material.domain.entity.TagCategoryDO;
import cn.com.voyah.material.domain.entity.TagInfoDO;
import cn.com.voyah.material.domain.entity.migrate.TagBatchSaveEntity;
import cn.com.voyah.material.domain.gateway.TagInfoGateway;
import cn.com.voyah.material.dto.MultiResponse;
import cn.com.voyah.material.dto.PageResponse;
import cn.com.voyah.material.dto.SingleResponse;
import cn.com.voyah.material.dto.UpdateTagInfoHiddenDTO;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.query.TagInfoQuery;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.gatewayimpl.TagInfoGatewayImpl;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.com.voyah.domain.entity.def.TagInfoDef.TAG_INFO;

/**
 * 控制层。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@CatchAndLog
@RestController
@Tag(name = "标签基本信息接口(管理平台)")
@RequestMapping("/tag-info")
public class TagInfoController {

    @Autowired
    private TagCategoryService tagCategoryService;
    @Autowired
    private TagInfoGateway tagInfoGateway;
    @Autowired
    private TagInfoService tagInfoService;




    /**
     * 添加。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存", description = "管理平台-保存标签信息")
    public SingleResponse<TagInfoDO> save(@RequestBody @Parameter(description = "") TagInfoDO tagInfoDO) {
        tagInfoDO.setCode(tagInfoDO.getName());
        if (tagInfoDO.getLevel() == 1) {
            tagInfoDO.setParentId(0L);
        }
        TagInfoDO save = tagInfoGateway.save(tagInfoDO);
        save.setHiddenStatus(TagInfoGatewayImpl.HIDDEN_ALL_TRUE);
        return SingleResponse.of(save);
    }

    @PostMapping("update-hidden")
    @Operation(summary = "标签显隐控制", description = "管理平台-标签显隐控制")
    public MultiResponse<TagInfoDO> updateHidden(@RequestBody UpdateTagInfoHiddenDTO dto) {
        if (ObjectUtil.isEmpty(dto.getCategoryId()) && ObjectUtil.isEmpty(dto.getTagInfoId())) {
            throw new BizException("主标签id或者标签id不能为空");
        }
        if (ObjectUtil.isEmpty(dto.isHidden())) {
            throw new BizException("显隐参数不能为空");
        }
        List<TagInfoDO> allTreeList;
        if (ObjectUtil.isNotEmpty(dto.getCategoryId()) && ObjectUtil.isEmpty(dto.getTagInfoId())) {
            QueryWrapper wrapper = QueryWrapper.create()
                    .where(TAG_INFO.CATEGORY_ID.eq(dto.getCategoryId()))
                    .and(TAG_INFO.IS_DELETE.eq(false));
            allTreeList = tagInfoGateway.list(wrapper);
        } else {
            allTreeList = tagInfoGateway.getAllTreeList(dto.getTagInfoId());
        }
        allTreeList.stream().forEach(t -> t.setHidden(dto.isHidden()));
        tagInfoGateway.updateBatch(allTreeList);
        allTreeList.addAll(tagInfoGateway.getAllParentList(dto.getTagInfoId()));
        tagInfoGateway.setHiddenStatus(allTreeList);
        return MultiResponse.of(allTreeList);
    }

    /**
     * 根据主键删除。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键", description = "管理平台-删除标签信息")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(tagInfoGateway.removeById(id));
    }

    /**
     * 根据主键更新。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新", description = "管理平台-修改标签信息")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description = "主键") TagInfoDO tagInfoDO) {
        tagInfoDO.setCode(tagInfoDO.getName());
        if (tagInfoDO.getLevel() == 1) {
            tagInfoDO.setParentId(0L);
        }
        return SingleResponse.of(tagInfoGateway.updateById(tagInfoDO));
    }

    /**
     * 查询所有。
     *
     * @return 所有数据
     */
    @PostMapping("list")
    @Operation(summary = "查询所有", description = "管理平台-修改所有标签")
    public MultiResponse<TagInfoDO> list(@RequestBody TagInfoDO tagInfoDO) {
        if (ObjectUtil.isEmpty(tagInfoDO) || ObjectUtil.isEmpty(tagInfoDO.getCategoryId())) {
            throw new BizException("标签类目不能为空");
        }
        return MultiResponse.of(tagInfoGateway.list(tagInfoDO));
    }

    /**
     * 根据主键获取详细信息。
     *
     * @param id 主键
     * @return 详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取", description = "管理平台-查询标签详情")
    public SingleResponse<TagInfoDO> getInfo(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(tagInfoGateway.getById(id));
    }

    @GetMapping("by-parent-id/{parentId}")
    @Operation(summary = "根据主键获取", description = "管理平台-查询子集标签列表")
    public MultiResponse<TagInfoDO> getListByParentId(@PathVariable(name = "parentId") Long parentId) {
        return MultiResponse.of(tagInfoGateway.getListByParentId(parentId));
    }

    /**
     * 分页查询。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询", description = "管理平台-分页查询标签信息")
    public PageResponse<TagInfoDO> page(@RequestBody PageQueryCondition<TagInfoDO> query) {
        Page<TagInfoDO> page = tagInfoGateway.page(PageUtil.of(query));
        tagInfoGateway.setHiddenStatus(page.getRecords());
        return PageUtil.of(page);
    }


    @PostMapping("/batch/save")
    @Operation(summary = "保存", description = "管理平台-保存标签信息")
    public void batchSave(@RequestBody @Parameter(description = "") TagBatchSaveEntity tagBatchSaveEntity) {
        tagCategoryService.batchSave(tagBatchSaveEntity);
    }

    @PostMapping("/batch/save2")
    @Operation(summary = "保存", description = "管理平台-保存标签信息")
    public void batchSave2(@RequestBody @Parameter(description = "") TagBatchSaveEntity tagBatchSaveEntity) {
        tagCategoryService.batchSave2(tagBatchSaveEntity);
    }

    @PostMapping("/get-by-id-list")
    @Operation(summary = "通过id列表查询标签列表", description = "管理平台-查询标签列表")
    public MultiResponse<TagInfoDO> getByIdList(@RequestBody Long[] tagIdList) {
        if (tagIdList.length == 0) {
            return MultiResponse.of(List.of());
        }
        return MultiResponse.of(tagInfoGateway.getListByIdList(List.of(tagIdList)));
    }


    @PostMapping("/get-by-parent-id-list")
    @Operation(summary = "通过id列表查询标签列表", description = "管理平台-查询标签列表")
    public MultiResponse<TagInfoDO> getByParentIdList(@RequestBody TagInfoQuery tagInfoQuery) {
        if (ObjectUtil.isEmpty(tagInfoQuery)) {
            return MultiResponse.of(List.of());
        }
        if (CollectionUtils.isEmpty(tagInfoQuery.getParentTagIdList())) {
            throw new BizException("请选择父级IdList");
        }
        if (ObjectUtil.isEmpty(tagInfoQuery.getQueryLevel())) {
            throw new BizException("请选择层级");
        }
        if (ObjectUtil.isEmpty(tagInfoQuery.getCategoryId())) {
            throw new BizException("请选择类目Id");
        }
        if (StringUtils.isEmpty(tagInfoQuery.getTagName())) {
            throw new BizException("标签名称不能为空!");
        }
        return MultiResponse.of(tagInfoGateway.getByParentIdList(tagInfoQuery));
    }

    @PostMapping("/get-by-parent-id-list2")
    @Operation(summary = "通过id列表查询标签列表", description = "管理平台-查询标签列表")
    public SingleResponse<Map<String, List<TagInfoDO>>> getByParentIdList2(@RequestBody TagInfoQuery tagInfoQuery) {
        if (ObjectUtil.isEmpty(tagInfoQuery)) {
            return SingleResponse.of(Map.of());
        }
        if (CollectionUtils.isEmpty(tagInfoQuery.getParentTagIdList())) {
            throw new BizException("请选择父级IdList");
        }
        if (ObjectUtil.isEmpty(tagInfoQuery.getQueryLevel())) {
            throw new BizException("请选择层级");
        }
        if (ObjectUtil.isEmpty(tagInfoQuery.getCategoryId())) {
            throw new BizException("请选择类目Id");
        }
        if (StringUtils.isEmpty(tagInfoQuery.getTagName())) {
            throw new BizException("标签名称不能为空!");
        }
        return SingleResponse.of(tagInfoService.getByParentIdList2(tagInfoQuery));
    }


    @PostMapping("/get-all-tag-merge-list")
    @Operation(summary = "查询所有标签根据标签名和标签值合并后的列表")
    public MultiResponse<TagInfoMergeVO> getAllTagMergeVoList(@RequestParam(value = "categoryId", required = false) Long categoryId, @RequestParam(value = "status", required = false) Integer status) {
        List<TagInfoMergeVO> list = tagInfoService.getAllTagMergeVoList(categoryId, false);

        if (ObjectUtil.isEmpty(status)) {
            return MultiResponse.of(list);
        } else {
            List<TagInfoMergeVO> result = new ArrayList<>();
            TagCategoryDO tagCategoryDO = new TagCategoryDO();
            tagCategoryDO.setStatus(status);
            List<TagCategoryDO> categoryList = tagCategoryService.getList(tagCategoryDO);
            if (CollectionUtil.isEmpty(categoryList)) {
                return MultiResponse.of(List.of());
            }
            List<Long> catIdList = categoryList.stream().map(each -> each.getId()).collect(Collectors.toList());
            for (TagInfoMergeVO tagInfoMergeVO : list) {
                if (catIdList.contains(tagInfoMergeVO.getCategoryId())) {
                    result.add(tagInfoMergeVO);
                }
            }
            return MultiResponse.of(result);

        }

    }



}