package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.client.RecommendSearchService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.RecommendSearchDO;
import cn.com.voyah.material.domain.gateway.RecommendSearchGateway;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.update.SortChangeDTO;
import cn.com.voyah.material.util.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import static cn.com.voyah.domain.entity.def.RecommendSearchDef.RECOMMEND_SEARCH;

/**
 * 推荐搜索表 控制层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@CatchAndLog
@RestController
@Tag(name = "推荐搜索配置(管理平台)")
@RequestMapping("/recommend-search")
public class RecommendSearchController {

    @Autowired
    private RecommendSearchGateway recommendSearchGateway;
    @Autowired
    private RecommendSearchService recommendSearchService;



    /**
     * 添加推荐搜索表。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存推荐搜索表",description = "管理平台-添加推荐搜索信息")
    public SingleResponse<RecommendSearchDO> save(@RequestBody @Parameter(description="推荐搜索表")RecommendSearchDO recommendSearchDO) {
        recommendSearchDO.setSort(System.currentTimeMillis());
        return SingleResponse.of(recommendSearchGateway.save(recommendSearchDO));
    }

    /**
     * 根据主键删除推荐搜索表。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键推荐搜索表",description = "管理平台-删除推荐搜索信息")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(recommendSearchGateway.removeById(id));
    }

    /**
     * 根据主键更新推荐搜索表。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新推荐搜索表",description = "管理平台-修改推荐搜索信息")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description="推荐搜索表主键")RecommendSearchDO recommendSearchDO) {
        return SingleResponse.of(recommendSearchGateway.updateById(recommendSearchDO));
    }

    /**
     * 查询所有推荐搜索表。
     *
     * @return 所有数据
     */
    @PostMapping("list")
    @Operation(summary = "查询所有推荐搜索表",description = "管理平台-查询推荐搜索列表")
    public MultiResponse<RecommendSearchDTO> list(@RequestBody(required = false) PageQueryCondition<RecommendSearchDTO> query) {
        if (StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(RECOMMEND_SEARCH.SORT.getName());
        }
        query.setOrderDirection(CommonConstants.DESC);
        return MultiResponse.of(recommendSearchService.list(query,TimeQueryInterval.buildFromPage(query)));
    }

    /**
     * 根据推荐搜索表主键获取详细信息。
     *
     * @param id 推荐搜索表主键
     * @return 推荐搜索表详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取推荐搜索表",description = "管理平台-查询单条推荐搜索信息")
    public SingleResponse<RecommendSearchDTO> getInfo(@PathVariable(name = "id") Long id) {
        RecommendSearchDTO res = recommendSearchService.getById(id);
//        if(ObjectUtil.isNotEmpty(res.getBackgroundFileId())){
//            FileDetailDO file = fileDetailGateway.getById(res.getBackgroundFileId());
//            res.setBackgroundUrl(file.getUrl());
//            res.setBackgroundFilename(file.getFilename());
//        }
        return SingleResponse.of(res);
    }

    /**
     * 分页查询推荐搜索表。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询推荐搜索表",description = "管理平台-分页查询推荐搜索信息")
    public PageResponse<RecommendSearchDTO> page(@RequestBody PageQueryCondition<RecommendSearchDO> query) {
        if (StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(RECOMMEND_SEARCH.SORT.getName());
        }
        query.setOrderDirection(CommonConstants.DESC);
        return PageUtil.of(recommendSearchService.page(query, TimeQueryInterval.buildFromPage(query)));
    }

    @PostMapping("{id}/sort-change")
    @Operation(summary = "排序变更",description = "管理平台-修改推荐搜索排序")
    public SingleResponse<Boolean> sortChange(@PathVariable(value = "id") Long id,@RequestBody SortChangeDTO sortChangeDTO) {
        sortChangeDTO.setId(id);
        return SingleResponse.of(recommendSearchService.sortChange(sortChangeDTO));
    }

}