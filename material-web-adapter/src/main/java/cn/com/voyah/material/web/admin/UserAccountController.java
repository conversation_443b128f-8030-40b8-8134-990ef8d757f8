package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.api.UserAccountService;
import cn.com.voyah.material.dto.PageResponse;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.dto.user.UserAccountQuery;
import cn.com.voyah.material.dto.user.UserAccountVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户账号 控制层。
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "用户账号接口(管理平台)")
@RequestMapping("/user/account")
public class UserAccountController {

    private UserAccountService userAccountService;

    /**
     * 分页查询素材库（类型）。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询素材库（类型）",description = "管理平台-分页查询账号信息")
    public PageResponse<UserAccountVO> page(@RequestBody PageQueryCondition<UserAccountQuery> query) {
        return userAccountService.page(query);
    }



}