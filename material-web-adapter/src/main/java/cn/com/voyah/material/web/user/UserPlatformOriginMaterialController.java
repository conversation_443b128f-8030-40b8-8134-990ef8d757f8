package cn.com.voyah.material.web.user;

import cn.com.voyah.domain.entity.def.MaterialTagDef;
import cn.com.voyah.domain.entity.def.OriginMaterialDef;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.client.MaterialTagService;
import cn.com.voyah.material.client.OriginMaterialService;
import cn.com.voyah.material.config.MaterialUserPlatformConfig;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.MaterialTagDO;
import cn.com.voyah.material.domain.entity.OriginMaterialDO;
import cn.com.voyah.material.domain.gateway.MaterialFavoriteGateway;
import cn.com.voyah.material.domain.gateway.MaterialTagItemGateway;
import cn.com.voyah.material.domain.gateway.OriginMaterialGateway;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.vo.common.TagInfoMergeVO;
import cn.com.voyah.material.vo.user.OriginMaterialExtensionVO;
import cn.com.voyah.material.vo.user.OriginMaterialInfoV2VO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.properties.IncInnerUserInfo;
import com.mybatisflex.core.paginate.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 素材 控制层。
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Slf4j
@CatchAndLog
@RestController
@Tag(name = "素材接口(使用平台)")
@RequestMapping("/use-platform/origin-material")
public class UserPlatformOriginMaterialController {
    @Autowired
    private OriginMaterialService originMaterialService;
    @Autowired
    private MaterialTagItemGateway materialTagItemGateway;
    @Autowired
    private MaterialTagService materialTagService;
    @Autowired
    private OriginMaterialGateway originMaterialGateway;

    @Autowired
    private MaterialUserPlatformConfig materialUserPlatformConfig;

    @Autowired
    private MaterialFavoriteGateway materialFavoriteGateway;

    @GetMapping("/file-type")
    @Operation(summary = "文件类型", description = "使用平台-查询文件类型列表")
    public MultiResponse<FileSourceType> getFileType() {
        return MultiResponse.of(materialUserPlatformConfig.getFileSourceTypes());
    }

    /**
     * 根据素材主键获取详细信息。
     *
     * @param id 素材主键
     * @return 素材详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取素材", description = "使用平台-浏览素材详情")
    public SingleResponse<OriginMaterialExtensionVO> getInfo(@PathVariable Long id) {
        OriginMaterialExtensionDTO originMaterialExtensionDTO = originMaterialService.getDetailById(id);
        if (ObjectUtil.isEmpty(originMaterialExtensionDTO)) {
            throw new BizException("该素材不存在!");
        }
        OriginMaterialExtensionVO originMaterialExtensionVO = new OriginMaterialExtensionVO();
        BeanUtils.copyProperties(originMaterialExtensionDTO, originMaterialExtensionVO);
        //浏览量加一
        materialTagService.addViewCount(id);
        MaterialTagDO materialTagDO = materialTagService.getBaseByMaterialId(id);
        if (ObjectUtil.isNotEmpty(materialTagDO)) {
            originMaterialExtensionVO.setTagRemark(materialTagDO.getRemark());
            List<TagInfoMergeVO> itemsList = materialTagItemGateway.getTagMergeVoList(id, null);
            originMaterialExtensionVO.setTags(itemsList);
            originMaterialExtensionVO.setUserViewCount(materialTagDO.getUserViewCount());
            originMaterialExtensionVO.setUserFavoriteCount(materialTagDO.getUserFavoriteCount());
        }
        IncInnerUserInfo userInfo = IncUserThreadLocal.get();
        if (ObjectUtil.isNotEmpty(userInfo)) {
            List<Long> idList = new ArrayList();
            idList.add(originMaterialExtensionDTO.getId());
            List<Long> existList = materialFavoriteGateway.getHaveFavoriteIdList(idList, userInfo.getName());
            if (CollectionUtil.isNotEmpty(existList)) {
                originMaterialExtensionVO.setHaveFavorite(true);
            }
        }
        List<Long> parentIdList = originMaterialExtensionDTO.getParentIdList();
        if (CollectionUtil.isNotEmpty(parentIdList) && parentIdList.size() > 1) {
            Optional<Long> optional = parentIdList.stream().filter(each -> !CommonConstants.HOME_FOLD_PARENT_ID.equals(each)).findFirst();
            if (optional.isPresent()) {
                OriginMaterialDO parent = originMaterialGateway.getById(optional.get());
                if (ObjectUtil.isNotEmpty(parent)) {
                    originMaterialExtensionVO.setLabName(parent.getMaterialName());
                }
            }
        }
        return SingleResponse.of(originMaterialExtensionVO);
    }


    @PostMapping("tag-search-page")
    @Operation(summary = "根据标注查询素材", description = "使用平台-分页查询素材")
    public PageResponse<OriginMaterialExtensionVO> getTagSearchPage(@RequestBody PageQueryCondition<GlobalOriginMaterialQuery> query) {
        if (StringUtils.isEmpty(query.getOrderBy()) || query.getOrderBy().equals("createTime")) {
            query.setOrderBy(OriginMaterialDef.ORIGIN_MATERIAL.CREATE_TIME.getName());
        } else {
            query.setOrderBy(MaterialTagDef.MATERIAL_TAG.USER_VIEW_COUNT.getName());
        }

        if (ObjectUtils.isEmpty(query.getCondition())) {
            GlobalOriginMaterialQuery globalOriginMaterialQuery = new GlobalOriginMaterialQuery();
            query.setCondition(globalOriginMaterialQuery);
        }
        query.getCondition().setIsPublic(CommonConstants.PUBLIC_STATUS_PUBLIC);
        query.getCondition().setTagStatus(CommonConstants.TAG_STATUS_TAG);
        List<Long> materialIdList = new ArrayList();
        if (CollectionUtil.isNotEmpty(query.getCondition().getTags())) {
            materialIdList = materialTagService.getMaterialIdListByTags(query.getCondition().getTags());
            if (CollectionUtil.isEmpty(materialIdList)) {
                return PageResponse.of(List.of(), 0, query.getPageSize(), query.getPageIndex());
            }
        }
        if (CollectionUtil.isNotEmpty(query.getCondition().getFileSourceTypes())) {
            List<String> fileSourceTypeList = query.getCondition().getFileSourceTypes();
            List<FileSourceType> fileSourceTypes = materialUserPlatformConfig.getFileSourceTypes();
            List<String> matchSuffixesList = new ArrayList<>();
            fileSourceTypes.stream().forEach(o -> {
                if (fileSourceTypeList.contains(o.getKey())) {
                    matchSuffixesList.addAll(o.getSuffixes());
                }
            });
            query.getCondition().setFileTypes(matchSuffixesList);
        }
        Page<OriginMaterialExtensionVO> pageResp = originMaterialService.getGlobalTagSearchPageInfo(query, materialIdList, TimeQueryInterval.buildFromPage(query), null);
        enrichFavorite(pageResp.getRecords());
        return PageUtil.of(pageResp);
    }


    @PostMapping("/v2/tag-search-page")
    @Operation(summary = "根据标注查询素材V2", description = "使用平台-分页查询素材V2")
    public PageResponse<OriginMaterialInfoV2VO> getTagSearchPageV2(@RequestBody PageQueryCondition<GlobalOriginMaterialQuery> query) {
        if (ObjectUtils.isEmpty(query.getCondition())) {
            GlobalOriginMaterialQuery globalOriginMaterialQuery = new GlobalOriginMaterialQuery();
            query.setCondition(globalOriginMaterialQuery);
        }
        Page<OriginMaterialInfoV2VO> pageResp = originMaterialService.getGlobalTagSearchPageInfoV2(query);
        enrichFavoriteV2(pageResp.getRecords());
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.set("tageValue", getTagValue(pageResp.getRecords()));
//        jsonObject.set("fileType", getFileType(pageResp.getRecords()));
//        jsonObject.set("bind", "yes");
//        if (ObjectUtil.isEmpty(query.getCondition().getQueryParam1())){
//            jsonObject.set("bind", "no");
//        }
        return PageUtil.of(pageResp);
    }


    @PostMapping("favorite-by-id/{id}")
    @Operation(summary = "收藏素材", description = "使用平台-收藏素材")
    public SingleResponse<Boolean> favoriteById(@PathVariable("id") Long id) {
        return SingleResponse.of(materialTagService.addFavoriteCount(id, true));
    }

    @PostMapping("cancel-favorite-by-id/{id}")
    @Operation(summary = "收藏素材", description = "使用平台-取消收藏素材")
    public SingleResponse<Boolean> cancelFavoriteById(@PathVariable("id") Long id) {
        return SingleResponse.of(materialTagService.addFavoriteCount(id, false));
    }

    @PostMapping("list")
    @Operation(summary = "查询所有素材库", description = "使用平台-查询所有素材库")
    public MultiResponse<OriginMaterialExtensionDTO> list() {
        OriginMaterialExtensionDTO originMaterialExtensionDTO = new OriginMaterialExtensionDTO();
        originMaterialExtensionDTO.setIsPublic(CommonConstants.PUBLIC_STATUS_PUBLIC);
        return MultiResponse.of(originMaterialGateway.getLabList(originMaterialExtensionDTO));
    }

    @PostMapping("favorite-search-page")
    @Operation(summary = "用户收藏分页查询", description = "使用平台-分页查询用户收藏的素材信息")
    public PageResponse<OriginMaterialExtensionVO> getFavoriteSearchPage(@RequestBody(required = false) PageQueryCondition<GlobalOriginMaterialQuery> query) {
        if (ObjectUtils.isEmpty(query)) {
            query = new PageQueryCondition();
        }
        if (StringUtils.isEmpty(query.getOrderBy()) || query.getOrderBy().equals("createTime")) {
            query.setOrderBy(OriginMaterialDef.ORIGIN_MATERIAL.CREATE_TIME.getName());
        } else {
            query.setOrderBy(MaterialTagDef.MATERIAL_TAG.USER_VIEW_COUNT.getName());
        }

        if (ObjectUtils.isEmpty(query.getCondition())) {
            GlobalOriginMaterialQuery globalOriginMaterialQuery = new GlobalOriginMaterialQuery();
            query.setCondition(globalOriginMaterialQuery);
        }
        query.getCondition().setIsPublic(CommonConstants.PUBLIC_STATUS_PUBLIC);
        query.getCondition().setTagStatus(CommonConstants.TAG_STATUS_TAG);

        if (CollectionUtil.isNotEmpty(query.getCondition().getFileSourceTypes())) {
            List<String> fileSourceTypeList = query.getCondition().getFileSourceTypes();
            List<FileSourceType> fileSourceTypes = materialUserPlatformConfig.getFileSourceTypes();
            List<String> matchSuffixesList = new ArrayList<>();
            fileSourceTypes.stream().forEach(o -> {
                if (fileSourceTypeList.contains(o.getKey())) {
                    matchSuffixesList.addAll(o.getSuffixes());
                }
            });
            if(query.getCondition().getFileSourceTypes().contains("other")){
                matchSuffixesList.add("other");
            }
            query.getCondition().setFileTypes(matchSuffixesList);
        }
        Page<OriginMaterialExtensionVO> pageResult = originMaterialService.getFavoriteSearchPage(query, null);
        return PageUtil.of(pageResult);
    }

    @PostMapping("download-by-id/{id}")
    @Operation(summary = "下载素材", description = "使用平台-下载素材")
    public SingleResponse<Boolean> downloadById(@PathVariable("id") Long id) {
        return SingleResponse.of(materialTagService.addDownloadCount(id));
    }

    @PostMapping("view-by-id/{id}")
    @Operation(summary = "浏览素材", description = "使用平台-预览素材")
    public SingleResponse<Boolean> viewById(@PathVariable Long id) {
        return SingleResponse.of(materialTagService.addViewCount(id));
    }

    public void enrichFavorite(List<OriginMaterialExtensionVO> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }
        List idList = records.stream().map(each -> each.getId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(idList)) {
            IncInnerUserInfo userInfo = IncUserThreadLocal.get();
            if (ObjectUtil.isNotEmpty(userInfo)) {
                List<Long> haveFavoriteIdList = materialFavoriteGateway.getHaveFavoriteIdList(idList, userInfo.getName());
                if (CollectionUtil.isNotEmpty(haveFavoriteIdList)) {
                    for (OriginMaterialExtensionVO each : records) {
                        if (haveFavoriteIdList.contains(each.getId())) {
                            each.setHaveFavorite(true);
                        }
                    }
                }
            }
        }
    }

    public void enrichFavoriteV2(List<OriginMaterialInfoV2VO> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }
        List idList = records.stream().map(each -> each.getId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(idList)) {
            IncInnerUserInfo userInfo = IncUserThreadLocal.get();
            if (ObjectUtil.isNotEmpty(userInfo)) {
                List<Long> haveFavoriteIdList = materialFavoriteGateway.getHaveFavoriteIdList(idList, userInfo.getName());
                if (CollectionUtil.isNotEmpty(haveFavoriteIdList)) {
                    for (OriginMaterialInfoV2VO each : records) {
                        if (haveFavoriteIdList.contains(each.getId())) {
                            each.setHaveFavorite(true);
                        }
                    }
                }
            }
        }
    }


    private Set<String> getTagValue(List<OriginMaterialInfoV2VO> infoV2VOList) {

        Set<String> res = new HashSet<>();
        infoV2VOList.stream().forEach(t -> {
            if (ObjectUtil.isNotEmpty(t.getTagValue())) {
                List<String> tag = Arrays.asList(t.getTagValue().split(","));
                tag.stream().forEach(x -> {
                    res.add(x);
                });
            }
        });
        return res;
    }

    private Set<String> getFileType(List<OriginMaterialInfoV2VO> infoV2VOList) {
        Set<String> res = new HashSet<>();
        infoV2VOList.stream().forEach(t -> {
            if (ObjectUtil.isNotEmpty(t.getFileType())) {
                res.add(t.getFileType());
            }
        });
        return res;
    }
}