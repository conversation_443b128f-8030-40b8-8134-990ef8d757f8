package cn.com.voyah.material.listener;

import cn.com.voyah.material.client.FileListenerService;
import cn.com.voyah.material.constants.EventTypeEnum;
import cn.com.voyah.material.domain.event.DomainEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

@Service
public class FileEventListener {

    @Autowired
    private FileListenerService fileListenerService;
    @Order
    @EventListener(DomainEvent.class)
    public void eventlistener(DomainEvent<?> event) {
        if (event == null || event.getData() == null || event.getEventType() == null ) {
            return;
        }
        if(EventTypeEnum.MATERIAL_ADD==event.getEventType()){
            fileListenerService.processAdd(event);
        }else if(EventTypeEnum.MATERIAL_REMOVE==event.getEventType()){
            fileListenerService.processRemove(event);
        }else if(EventTypeEnum.MATERIAL_UPDATE_PUBLIC==event.getEventType()){
            fileListenerService.processUpdatePublic(event);
        }else if(EventTypeEnum.MATERIAL_MOVE==event.getEventType()){
            fileListenerService.processMove(event);
        }



    }

}
