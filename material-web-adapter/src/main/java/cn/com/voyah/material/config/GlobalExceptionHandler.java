package cn.com.voyah.material.config;

import cn.com.voyah.material.dto.Response;
import cn.com.voyah.material.exception.BizException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    /**
     * 业务错误
     */
    private static final int ERROR_BAD_CODE = 400;
    /**
     * 服务错误
     */
    private static final int ERROR_SERVER_CODE = 500;

    /**
     * 创建返回对象
     */
    private ResponseEntity<Response> createResult(HttpServletRequest req, Exception e, Level level, Response r,
                                                  HttpStatus httpStatus) {
        int errorCode = r.getCode();
        //打印日志
        this.printLog(req, level, errorCode, e, r.getMsg());

        //返回对象
        return ResponseEntity.status(httpStatus).body(r);
    }


    /**
     * Request 参数校验异常， 缺少参数/类型转换
     * 如： @RequestParam  @PathVariable 等
     */
    @ExceptionHandler({MissingServletRequestParameterException.class, MethodArgumentTypeMismatchException.class})
    public ResponseEntity<Response> requestParameterExceptionHandle(HttpServletRequest req, Exception e) {
        String name, errorMsg;
        if (e instanceof MissingServletRequestParameterException) {
            MissingServletRequestParameterException missingException = (MissingServletRequestParameterException) e;
            //缺少必填参数异常
            name = missingException.getParameterName();
            errorMsg = String.format("Request missing parameters. field:%s", name);

        } else {
            //类型转换异常
            MethodArgumentTypeMismatchException typeMismatchException = (MethodArgumentTypeMismatchException) e;
            name = typeMismatchException.getName();
            errorMsg = String.format("Invalid parameter. field: %s", name);
            log.error(e.getMessage());
        }
        Response singleResponse = Response.buildFailure(ERROR_BAD_CODE, errorMsg);
        return createResult(req, null, Level.INFO, singleResponse, HttpStatus.BAD_REQUEST);
    }

    private ResponseEntity<Response> validationFieldError(HttpServletRequest req, String field, String message) {
        if (Objects.nonNull(field)) {
            log.info("Spring validation. field: {}  message: {}", field, message);
            Response singleResponse = Response.buildFailure(ERROR_BAD_CODE, message);
            return createResult(req, null, Level.INFO, singleResponse, HttpStatus.BAD_REQUEST);
        }
        Response singleResponse = Response.buildFailure(ERROR_BAD_CODE, "无效的参数");
        return createResult(req, null, Level.ERROR, singleResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * Request @Valid 异常
     * 如：test(@RequestBody(required = false) @Valid User user)
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Response> validationExceptionHandler(HttpServletRequest req, MethodArgumentNotValidException e) {
        ObjectError error = e.getBindingResult().getAllErrors().get(0);
        String field = (error instanceof FieldError ? ((FieldError) error).getField() : error.getObjectName());
        return this.validationFieldError(req, field, error.getDefaultMessage());
    }

    /**
     * Request @Valid 异常
     * 如：test(@Valid User user)
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<Response> validationExceptionHandler(HttpServletRequest req, BindException e) {
        FieldError fieldError = e.getFieldError();
        //类型匹配失败进入
        if (fieldError != null && fieldError.isBindingFailure()) {
            if (Objects.requireNonNull(fieldError.getCodes()).length > 2) {
                String typeClass = fieldError.getCodes()[2];
                //检查是不是时间类型
                if (Objects.nonNull(typeClass) && (typeClass.endsWith("java.util.Date") || typeClass.endsWith("java.time.LocalDateTime"))) {
                    Response singleResponse = Response.buildFailure(ERROR_BAD_CODE, "时间类型错误");
                    return createResult(req, null, Level.INFO, singleResponse, HttpStatus.BAD_REQUEST);
                }
            }
        }
        Response singleResponse = Response.buildFailure(ERROR_BAD_CODE, "未知参数错误");
        return this.createResult(req, e, Level.ERROR, singleResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(BizException.class)
    public ResponseEntity<Response> bizRequestException(HttpServletRequest req, BizException e) {
        Response singleResponse = Response.buildFailure(e.getErrCode(), e.getMessage());
        return createResult(req, null, Level.INFO, singleResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * 运行异常(服务异常)
     */
    @ExceptionHandler(value = {RuntimeException.class, Exception.class})
    public ResponseEntity<Response> runtimeExceptionHandler(HttpServletRequest req, Exception e) {
        Response singleResponse = Response.buildFailure(ERROR_SERVER_CODE, "服务异常，请稍后重试！");
        return createResult(req, e, Level.ERROR, singleResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }


    /**
     * 请求数据解析异常 Json解析异常
     */
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public ResponseEntity<Response> httpMessageNotReadableExceptionHandler(HttpServletRequest req, Exception e) {
        String exceptionMsg;
        //解析主要message信息
        if (e.getCause() instanceof JsonProcessingException cause) {
            exceptionMsg = cause.getOriginalMessage();
            if (cause instanceof InvalidFormatException invalidFormatException) {
                //处理时间封装独立的时间格式错误
                Class<?> targetType = invalidFormatException.getTargetType();
                if (targetType == Date.class
                        || targetType == LocalDateTime.class) {
                    exceptionMsg = null;
                }
            }
        } else exceptionMsg = e.getMessage();

        Response singleResponse = Response.buildFailure(ERROR_BAD_CODE, exceptionMsg);
        return createResult(req, e, Level.ERROR, singleResponse, HttpStatus.BAD_REQUEST);
    }

    private void printLog(HttpServletRequest req, Level level, int errorCode, Throwable t, String message) {
        String msg = String.format("invoke %s error.%s", Objects.isNull(req) ? "" : req.getRequestURI(), errorCode);
        if (Objects.nonNull(message)) msg += String.format(" message:%s", message);
        if (level == Level.ERROR)
            log.error(msg, t);
        else if (level == Level.WARN)
            log.warn(msg, t);
        else
            log.info(msg);
    }


}
