//package cn.com.voyah.material.es.vo;
//
//import cn.com.voyah.material.es.index.OriginMaterialEs;
//import lombok.Data;
//import org.dromara.easyes.annotation.IndexField;
//import org.dromara.easyes.annotation.IndexName;
//import org.dromara.easyes.annotation.rely.FieldStrategy;
//import org.dromara.easyes.annotation.rely.FieldType;
//
//import java.io.Serializable;
//import java.util.List;
//
///**
// * 素材 实体类。
// *
// * <AUTHOR>
// * @since 2024-11-07
// */
//@Data
//public class OriginMaterialEsVO implements Serializable {
//
//    private List<String> keywordList;
//
//
//    private List<OriginMaterialEs> materialEsList;
//}
