package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.client.MaterialAuthService;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.pojo.MaterialAuthPO;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.dto.MultiResponse;
import cn.com.voyah.material.dto.PageResponse;
import cn.com.voyah.material.dto.SingleResponse;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import cn.com.voyah.material.domain.entity.MaterialAuthDO;
import cn.com.voyah.material.domain.gateway.MaterialAuthGateway;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import java.util.List;
import java.util.Optional;

/**
 *  控制层。
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@CatchAndLog
@RestController
@Tag(name = "数据权限接口(管理平台)")
@RequestMapping("/material-auth")
public class MaterialAuthController {

    @Autowired
    private MaterialAuthGateway materialAuthGateway;
    @Autowired
    private MaterialAuthService materialAuthService;
    /**
     * 添加。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存",description = "管理平台-添加数据权限")
    public SingleResponse<MaterialAuthDO> save(@RequestBody @Parameter(description="")MaterialAuthDO materialAuthDO) {
        if(StringUtils.isEmpty(materialAuthDO.getShortId())){
            throw new BizException("账户短id不能为空!");
        }
        if(StringUtils.isEmpty(materialAuthDO.getUserName())){
            throw new BizException("用户姓名不能为空!");
        }
        if(CollectionUtil.isNotEmpty(materialAuthGateway.getList(materialAuthDO.getShortId()))){
            throw new BizException("账户短id已配置");
        }
        materialAuthService.removeAuthKeyCache(materialAuthDO.getShortId());
        return SingleResponse.of(materialAuthGateway.save(materialAuthDO));
    }

    /**
     * 根据主键删除。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键",description = "管理平台-删除数据权限")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        MaterialAuthDO materialAuthDO=materialAuthGateway.getById(id);
        if(ObjectUtil.isEmpty(materialAuthDO)){
            return SingleResponse.of(false);
        }
        materialAuthService.removeAuthKeyCache(materialAuthDO.getShortId());
        return SingleResponse.of(materialAuthGateway.removeById(id));
    }

    /**
     * 根据主键更新。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新",description = "管理平台-修改账号的数据权限")
    public SingleResponse<Boolean> update(@RequestBody @Parameter(description="主键")MaterialAuthDO materialAuthDO) {
        if(StringUtils.isEmpty(materialAuthDO.getShortId())){
            throw new BizException("账户短id不能为空!");
        }
        if(StringUtils.isEmpty(materialAuthDO.getUserName())){
            throw new BizException("用户姓名不能为空!");
        }
        String shortId=materialAuthDO.getShortId();
        List<MaterialAuthPO> list=materialAuthGateway.getList(materialAuthDO.getShortId());
        if(CollectionUtil.isNotEmpty(list)){
            Optional<MaterialAuthPO> optional=list.stream().filter(each->!shortId.equals(each.getShortId())).findAny();
            if(optional.isPresent()){
                throw new BizException("账户短id重复!");
            }
        }
        materialAuthService.removeAuthKeyCache(shortId);
        return SingleResponse.of(materialAuthGateway.updateById(materialAuthDO));
    }

    /**
     * 查询所有。
     *
     * @return 所有数据
     */
    @GetMapping("list")
    @Operation(summary = "查询所有",description = "管理平台-查询所有数据权限")
    public MultiResponse<MaterialAuthDO> list() {
        return MultiResponse.of(materialAuthGateway.list());
    }

    /**
     * 根据主键获取详细信息。
     *
     * @param id 主键
     * @return 详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取",description = "管理平台-查询单条数据权限详细信息")
    public SingleResponse<MaterialAuthDO> getInfo(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(materialAuthGateway.getById(id));
    }

    /**
     * 分页查询。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询",description = "管理平台-分页查询数据权限信息")
    public PageResponse<MaterialAuthDO> page(@RequestBody PageQueryCondition<MaterialAuthDO> query) {
        return PageUtil.of(materialAuthService.page(query));
    }

}