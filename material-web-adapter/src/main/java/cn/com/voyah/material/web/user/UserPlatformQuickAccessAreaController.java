package cn.com.voyah.material.web.user;

import cn.com.voyah.domain.entity.def.MaterialTagDef;
import cn.com.voyah.domain.entity.def.OriginMaterialDef;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.client.MaterialTagService;
import cn.com.voyah.material.client.OriginMaterialService;
import cn.com.voyah.material.client.QuickAccessAreaService;
import cn.com.voyah.material.config.MaterialUserPlatformConfig;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.domain.entity.FileDetailDO;
import cn.com.voyah.material.domain.entity.QuickAccessAreaDO;
import cn.com.voyah.material.domain.gateway.FileDetailGateway;
import cn.com.voyah.material.domain.gateway.MaterialFavoriteGateway;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.vo.QuickAccessAreaMetaDataVO;
import cn.com.voyah.material.vo.user.OriginMaterialExtensionVO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.properties.IncInnerUserInfo;
import com.mybatisflex.core.paginate.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 金刚位表 控制层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@CatchAndLog
@RestController
@Tag(name = "金刚位相关接口(使用平台)")
@RequestMapping("/use-platform/quick-access-area")
public class UserPlatformQuickAccessAreaController {

    @Autowired
    private QuickAccessAreaService quickAccessAreaService;

    @Autowired
    private OriginMaterialService originMaterialService;
    @Autowired
    private MaterialTagService materialTagService;

    @Autowired
    private MaterialUserPlatformConfig materialUserPlatformConfig;

    @Autowired
    private MaterialFavoriteGateway materialFavoriteGateway;

    @Autowired
    private FileDetailGateway fileDetailGateway;
    @PostMapping("list")
    @Operation(summary = "查询所有金刚位",description = "使用平台-查询所有金刚位")
    public MultiResponse<QuickAccessAreaDTO> list(@RequestBody(required = false) QuickAccessAreaDO quickAccessAreaDO) {
        if(ObjectUtils.isEmpty(quickAccessAreaDO)){
            quickAccessAreaDO=new QuickAccessAreaDO();
        }
        quickAccessAreaDO.setStatus(1);
        return MultiResponse.of(quickAccessAreaService.list(quickAccessAreaDO));
    }


    @PostMapping("detail/{id}")
    @Operation(summary = "查询金刚位详情数据（背景图、统计）",description = "使用平台-查询金刚位详情数据（背景图、统计）")
    public SingleResponse<QuickAccessAreaMetaDataVO> detail(@PathVariable(name = "id") @NotNull(message = "金刚位不能为空") Long id) {
        QuickAccessAreaDTO quickAccessAreaDTO=quickAccessAreaService.getById(id);
        if(ObjectUtils.isEmpty(quickAccessAreaDTO)){
            throw new BizException("该金刚位不存在！");
        }
        QuickAccessAreaMetaDataVO res  = new QuickAccessAreaMetaDataVO();

        if(ObjectUtil.isNotEmpty(quickAccessAreaDTO.getBackgroundFileId())){
            FileDetailDO file = fileDetailGateway.getById(quickAccessAreaDTO.getBackgroundFileId());
            res.setBackgroundFileId(quickAccessAreaDTO.getBackgroundFileId());
            res.setBackgroundUrl(file.getUrl());}
        res.setTitle("");
        res.setCount("99999");
        return SingleResponse.of(res);
    }

    /**
     * 根据金刚位表主键获取详细信息。
     *
     * @param id 金刚位表主键
     * @return 金刚位表详情
     */
    @PostMapping("by/{id}/page")
    @Operation(summary = "金刚位主键跳转后的page页面",description = "使用平台-查询指定金刚位下的对应的素材信息")
    public PageResponse<OriginMaterialExtensionVO> getPageById(@PathVariable(name = "id") Long id,@RequestBody(required = false) PageQueryCondition<GlobalOriginMaterialQuery> query) {
        QuickAccessAreaDTO quickAccessAreaDTO=quickAccessAreaService.getById(id);
        if(ObjectUtils.isEmpty(quickAccessAreaDTO)){
            throw new BizException("该金刚位不存在！");
        }
        if(ObjectUtils.isEmpty(query)){
            query=new PageQueryCondition();
        }
        if (StringUtils.isEmpty(query.getOrderBy())||query.getOrderBy().equals("createTime")) {
            query.setOrderBy(OriginMaterialDef.ORIGIN_MATERIAL.CREATE_TIME.getName());
        }else{
            query.setOrderBy(MaterialTagDef.MATERIAL_TAG.USER_VIEW_COUNT.getName());
        }
        if(ObjectUtils.isEmpty(query.getCondition())){
            GlobalOriginMaterialQuery globalOriginMaterialQuery=new GlobalOriginMaterialQuery();
            query.setCondition(globalOriginMaterialQuery);
        }
        query.getCondition().setIsPublic(CommonConstants.PUBLIC_STATUS_PUBLIC);
        query.getCondition().setTagStatus(CommonConstants.TAG_STATUS_TAG);
        if(CollectionUtil.isNotEmpty(query.getCondition().getFileSourceTypes())){
            List<String> fileSourceTypeList=query.getCondition().getFileSourceTypes();
            List<FileSourceType> fileSourceTypes = materialUserPlatformConfig.getFileSourceTypes();
            List<String> matchSuffixesList = new ArrayList<>();
            fileSourceTypes.stream().forEach(o -> {
                if (fileSourceTypeList.contains(o.getKey())) {
                    matchSuffixesList.addAll(o.getSuffixes());
                }
            });
            query.getCondition().setFileTypes(matchSuffixesList);
        }
        List<Long> materialIdList=new ArrayList();
        if(CollectionUtil.isNotEmpty(quickAccessAreaDTO.getTags())){
            materialIdList=materialTagService.getMaterialIdListByTags(quickAccessAreaDTO.getTags());
            if(CollectionUtil.isEmpty(materialIdList)){
                return PageResponse.of(List.of(),0,query.getPageSize(),query.getPageIndex());
            }
        }
        Page<OriginMaterialExtensionVO> pageResp=originMaterialService.getGlobalTagSearchPageInfo(query,materialIdList,null,null);
        enrichFavorite(pageResp.getRecords());
        return PageUtil.of(pageResp);
    }
    public void enrichFavorite(List<OriginMaterialExtensionVO> records){
        if(CollectionUtil.isEmpty(records)){
            return ;
        }
        List idList=records.stream().map(each->each.getId()).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(idList)){
            IncInnerUserInfo userInfo= IncUserThreadLocal.get();
            if(ObjectUtil.isNotEmpty(userInfo)){
                List<Long> haveFavoriteIdList=materialFavoriteGateway.getHaveFavoriteIdList(idList,userInfo.getName());
                if(CollectionUtil.isNotEmpty(haveFavoriteIdList)){
                    for (OriginMaterialExtensionVO each:records) {
                        if(haveFavoriteIdList.contains(each.getId())){
                            each.setHaveFavorite(true);
                        }
                    }
                }
            }
        }
    }

}