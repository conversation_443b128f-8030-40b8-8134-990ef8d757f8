package cn.com.voyah.material.schedule;

import cn.com.voyah.material.client.NoticeMessageService;
import cn.com.voyah.material.client.TaskInfoService;
import cn.com.voyah.material.constants.NoticeState;
import cn.com.voyah.material.constants.NoticeType;
import cn.com.voyah.material.constants.TaskStatus;
import cn.com.voyah.material.domain.entity.NoticeMessageDO;
import cn.com.voyah.material.domain.entity.TaskInfoDO;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Component
@ConditionalOnExpression("${manage.scheduling.active:false}")
public class TaskSchedulingExecutor {

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private NoticeMessageService noticeMessageService;
    @Value("${manage.notice.expire.day:3}")
    private Integer toExpireDays;
    @Scheduled(cron = "${manage.task.cron.express:0 1 0 * * ?}")
    public void taskExpired() {
        TaskInfoDO taskInfoQueryDO=new TaskInfoDO();
        taskInfoQueryDO.setTaskStatus(TaskStatus.PROGRESS.getValue());
        Collection<TaskInfoDO> list=taskInfoService.list(taskInfoQueryDO);
        LocalDateTime now=LocalDateTime.now();
        List<Long> unfinishIdList=new ArrayList<>();
        //即将过期
        List<TaskInfoDO> toExpireList=new ArrayList<>();


        for (TaskInfoDO each:list) {
            try{
                if(now.isAfter(each.getTaskEndTime())){
                    unfinishIdList.add(each.getId());
                }else if(now.plusDays(toExpireDays).isAfter(each.getTaskEndTime())&&now.plusDays(toExpireDays-1).isBefore(each.getTaskEndTime())){
                    toExpireList.add(each);
                }
            }catch (Exception e){
                log.error(e.getMessage());
            }
        }
        if(CollectionUtil.isNotEmpty(unfinishIdList)){
            taskInfoService.updateStatusBatch(unfinishIdList,TaskStatus.UNFINISH);
        }
        if(CollectionUtil.isNotEmpty(toExpireList)){
            List<NoticeMessageDO> noticeMessageDOList=new ArrayList();
            for (TaskInfoDO taskInfoDO:toExpireList) {
                if(exist(taskInfoDO.getId(),NoticeType.TASK_TO_EXPIRE.getValue())){
                   continue;
                }
                NoticeMessageDO noticeMessageDO=new NoticeMessageDO();
                noticeMessageDO.setShortId(taskInfoDO.getExecuteShortId());
                noticeMessageDO.setUserName(taskInfoDO.getExecuteBy());
                noticeMessageDO.setSource(0);
                noticeMessageDO.setSendTime(taskInfoDO.getCreateTime());
                noticeMessageDO.setState(NoticeState.UNREAD.getValue());
                noticeMessageDO.setType(NoticeType.TASK_TO_EXPIRE.getValue());
                noticeMessageDO.setContent(NoticeType.TASK_TO_EXPIRE.getContent());
                noticeMessageDO.setRelationId(taskInfoDO.getId());
                noticeMessageDOList.add(noticeMessageDO);
            }
            noticeMessageService.saveBatch(noticeMessageDOList);
        }
    }

    private boolean exist(Long taskId,Integer type){
        NoticeMessageDO noticeMessageDO=new NoticeMessageDO();
        noticeMessageDO.setRelationId(taskId);
        noticeMessageDO.setType(type);
        Collection<NoticeMessageDO> list=noticeMessageService.list(noticeMessageDO);
        return CollectionUtil.isNotEmpty(list);
    }
}
