package cn.com.voyah.material.config;

import cn.com.voyah.material.catchlog.ResponseHandlerFactory;
import cn.com.voyah.material.dto.OperationLogDTO;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.exception.SysException;
import cn.com.voyah.material.util.JsonUtil;
import cn.com.voyah.material.utils.RedisUtil;
import cn.hutool.json.JSONUtil;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.properties.IncInnerUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

@Aspect
@Order(1)
public class CatchLogAspect {
    private static final Logger log = LoggerFactory.getLogger(CatchLogAspect.class);
    @Autowired
    private RedisUtil redisUtil;
    @Value("${topic.log:operation_log}")
    private  String topicLog;
    @Value("${operation.log.ignore:/common/file-type,/use-platform/origin-material/file-type,/operation-log/page}")
    private List<String> ignorePath;
    @Value("${operation.log.response.ignore:/operation-log/export}")
    private List<String> ignoreRepsonsePath;
    @Value("${operation.log.request.ignore:/operation-log/export}")
    private List<String> ignoreRequestPath;
    public CatchLogAspect() {
    }

//    @Pointcut("")
//    public void pointcut() {
//    }

    @Around("@annotation(operation) && execution(public * cn.com.voyah.material.web.admin.*.*(..))")
    public Object around(ProceedingJoinPoint joinPoint, Operation operation) {
        LocalDateTime startTime = LocalDateTime.now();
//        this.logRequest(joinPoint);
        Object response = null;
        int state=0;
        String errorMsg=null;
        Object[] args=null;
        String desc = StringUtils.isNotEmpty(operation.description())?operation.description():operation.summary();
        try {
            args=joinPoint.getArgs();
            response = joinPoint.proceed();
        } catch (Throwable var9) {
            response = this.handleException(joinPoint, var9);
            errorMsg=var9.getMessage();
            state=1;
        } finally {
            this.logResponse(startTime, response);
            OperationLogDTO operationLogDTO=getRequestLog();
            if(!ignorePath.contains(operationLogDTO.getPath())){
                LocalDateTime responseTime = LocalDateTime.now();
                operationLogDTO.setOperationTime(startTime);

                if(!ignoreRepsonsePath.contains(operationLogDTO.getPath())&&!ObjectUtils.isEmpty(response)){
                    String resp=JSONUtil.toJsonStr(response);
                    operationLogDTO.setResponse(resp.length()>499?resp.substring(0,499):resp);
                }
                if(!ignoreRequestPath.contains(operationLogDTO.getPath())&&!ObjectUtils.isEmpty(args)){
                    String requestParams=JSONUtil.toJsonStr(args);
                    operationLogDTO.setRequestParams(requestParams.length()>499?requestParams.substring(0,499):requestParams);
                }
                operationLogDTO.setInterfaceRemark(desc);
                operationLogDTO.setErrorMsg(errorMsg);
                operationLogDTO.setState(state);
                operationLogDTO.setRequestTime(startTime);
                operationLogDTO.setResponseTime(responseTime);
                operationLogDTO.setTimeInterval(Duration.between(startTime,responseTime).toMillis());
                log.warn("requestLogDTO:[{}]", JsonUtil.writeValueAsString(operationLogDTO));
                redisUtil.publishMessage(topicLog,JsonUtil.writeValueAsString(operationLogDTO));
                defaultCleanThreadLocal();
            }
        }

        return response;
    }

    private Object handleException(ProceedingJoinPoint joinPoint, Throwable e) {
        MethodSignature ms = (MethodSignature) joinPoint.getSignature();
        Class<?> returnType = ms.getReturnType();
        if (e instanceof BizException) {
            if (log.isDebugEnabled()) {
                log.warn("BIZ EXCEPTION : {}", e.getMessage());
            }
            return ResponseHandlerFactory.get().handle(returnType, ((BizException) e).getErrCode(), e.getMessage());
        } else if (e instanceof SysException) {
            log.error("SYS EXCEPTION: {}", e.getMessage(), e);
            return ResponseHandlerFactory.get().handle(returnType, ((SysException) e).getErrCode(), e.getMessage());
        } else {
            log.error("UNKNOWN EXCEPTION: {}", e.getMessage(), e);
            return ResponseHandlerFactory.get().handle(returnType, 500, e.getMessage());
        }
    }

    private void logResponse(LocalDateTime startTime, Object response) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            if (log.isDebugEnabled()) {
//                log.debug("RESPONSE: {}", JsonUtil.writeValueAsString(response));
                log.debug("COST: {}ms", Duration.between(endTime,startTime).toMillis());
            }
        } catch (Exception var6) {
            log.error("logResponse error: {}", var6.getMessage(), var6);
        }

    }

    private void logRequest(ProceedingJoinPoint joinPoint) {
        if (log.isDebugEnabled()) {
            try {
                log.debug("START PROCESSING: {}", joinPoint.getSignature().toShortString());
                if(joinPoint.getSignature().toShortString().contains("export")){
                    return ;
                }
                Object[] args = joinPoint.getArgs();
                for (Object arg : args) {
                    log.debug("REQUEST: {}", JsonUtil.writeValueAsString(arg));
                }
            } catch (Exception var7) {
                log.error("logReqeust error: {}", var7.getMessage(), var7);
            }

        }
    }

    public OperationLogDTO getRequestLog(){
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String ipAddr=getIpAddr(request);
        OperationLogDTO requestLogDTO=new OperationLogDTO();
        requestLogDTO.setIp(ipAddr);
        requestLogDTO.setRequestTime(LocalDateTime.now());
        requestLogDTO.setPath(request.getRequestURI());
        requestLogDTO.setMethod(request.getMethod());
        IncInnerUserInfo userInfo = IncUserThreadLocal.get();
        requestLogDTO.setShortId(ObjectUtils.isNotEmpty(userInfo)?userInfo.getName():null);
        String userName= ObjectUtils.isNotEmpty(userInfo)?userInfo.getUserName():"unknown";
        requestLogDTO.setUserName(userName);
        return requestLogDTO;
    }
    public String getIpAddr(HttpServletRequest request){
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    public static void defaultCleanThreadLocal() {
        IncUserThreadLocal.set((IncInnerUserInfo)null);
    }
}
