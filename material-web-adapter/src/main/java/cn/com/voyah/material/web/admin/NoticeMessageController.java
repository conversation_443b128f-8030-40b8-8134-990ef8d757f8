package cn.com.voyah.material.web.admin;

import cn.com.voyah.material.client.NoticeMessageService;
import cn.com.voyah.material.constants.CommonConstants;
import cn.com.voyah.material.constants.NoticeState;
import cn.com.voyah.material.dto.TimeQueryInterval;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.dto.MultiResponse;
import cn.com.voyah.material.dto.PageResponse;
import cn.com.voyah.material.dto.SingleResponse;
import com.iov.tencent.inc.access.aop.IncUserThreadLocal;
import com.iov.tencent.inc.access.model.properties.IncInnerUserInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import cn.com.voyah.material.domain.entity.NoticeMessageDO;
import cn.com.voyah.material.domain.gateway.NoticeMessageGateway;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import java.time.LocalDateTime;
import java.util.Objects;

import static cn.com.voyah.domain.entity.def.NoticeMessageDef.NOTICE_MESSAGE;
import static cn.com.voyah.domain.entity.def.QuickAccessAreaDef.QUICK_ACCESS_AREA;

/**
 * 通知表 控制层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@CatchAndLog
@RestController
@Tag(name = "通知表接口(管理平台)")
@RequestMapping("/notice-message")
public class NoticeMessageController {

    @Autowired
    private NoticeMessageGateway noticeMessageGateway;
    @Autowired
    private NoticeMessageService noticeMessageService;

    /**
     * 添加通知表。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存通知表",description = "管理平台-新增消息通知")
    public SingleResponse<NoticeMessageDO> save(@RequestBody @Parameter(description="通知表")NoticeMessageDO noticeMessageDO) {
        return SingleResponse.of(noticeMessageService.save(noticeMessageDO));
    }

    /**
     * 根据主键删除通知表。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键通知表",description = "管理平台-删除消息通知")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(noticeMessageGateway.removeById(id));
    }

    /**
     * 根据主键更新通知表。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update/{id}")
    @Operation(summary = "根据主键更新通知表已读未读",description = "管理平台-消息通知更新为已读")
    public SingleResponse<Boolean> update(@PathVariable(name = "id") Long id) {
        NoticeMessageDO noticeMessageDO=new NoticeMessageDO();
        noticeMessageDO.setId(id);
        noticeMessageDO.setState(NoticeState.READ.getValue());
        return SingleResponse.of(noticeMessageGateway.updateById(noticeMessageDO));
    }

    /**
     * 查询所有通知表。
     *
     * @return 所有数据
     */
    @PostMapping("list")
    @Operation(summary = "查询所有通知",description = "管理平台-查询消息通知列表")
    public MultiResponse<NoticeMessageDO> list(@RequestBody(required = false) NoticeMessageDO noticeMessageDO) {
        return MultiResponse.of(noticeMessageService.list(noticeMessageDO));
    }

    /**
     * 根据通知表主键获取详细信息。
     *
     * @param id 通知表主键
     * @return 通知表详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取通知表",description = "管理平台-查询消息通知详情")
    public SingleResponse<NoticeMessageDO> getInfo(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(noticeMessageGateway.getById(id));
    }

    /**
     * 分页查询通知表。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询通知表",description = "管理平台-分页查询消息通知信息")
    public PageResponse<NoticeMessageDO> page(@RequestBody PageQueryCondition<NoticeMessageDO> query) {
        if (StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(NOTICE_MESSAGE.CREATE_TIME.getName());
        }
        if (StringUtils.isEmpty(query.getOrderDirection())) {
            query.setOrderDirection(CommonConstants.DESC);
        }
        IncInnerUserInfo userInfo= IncUserThreadLocal.get();
        if(ObjectUtils.isEmpty(query.getCondition())){
            query.setCondition(new NoticeMessageDO());
        }
        query.getCondition().setShortId(userInfo.getName());
        return PageUtil.of(noticeMessageService.getPage(query, TimeQueryInterval.buildFromPage(query)));
    }

    @GetMapping("count")
    @Operation(summary = "根据主键获取通知表",description = "管理平台-查询未读消息通知信息条数")
    public SingleResponse<Long> getCount() {

        IncInnerUserInfo userInfo= IncUserThreadLocal.get();
        NoticeMessageDO noticeMessageDO=new NoticeMessageDO();
        noticeMessageDO.setShortId(userInfo.getName());
        noticeMessageDO.setUserName(userInfo.getUserName());
        noticeMessageDO.setState(NoticeState.UNREAD.getValue());
        return SingleResponse.of(noticeMessageService.getCount(noticeMessageDO));
    }


}