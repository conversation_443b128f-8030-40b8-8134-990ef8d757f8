package cn.com.voyah.material.web.user;

import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.com.voyah.material.client.RecommendSearchService;
import cn.com.voyah.material.dto.MultiResponse;
import cn.com.voyah.material.dto.RecommendSearchDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 推荐搜索表 控制层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@CatchAndLog
@RestController
@Tag(name = "推荐搜索相关接口(使用平台)")
@RequestMapping("/use-platform/recommend-search")
public class UserPlatformRecommendSearchController {

    @Autowired
    private RecommendSearchService recommendSearchService;



    @PostMapping("list")
    @Operation(summary = "查询所有推荐搜索",description = "使用平台-查询所有推荐搜索")
    public MultiResponse<RecommendSearchDTO> list() {
        return MultiResponse.of(recommendSearchService.listByUser());
    }


   
}