//package cn.com.voyah.material.es.service.impl;
//
//
//import cn.com.voyah.material.client.MaterialTagService;
//import cn.com.voyah.material.domain.entity.MaterialTagDO;
//import cn.com.voyah.material.domain.entity.MaterialTagItemDO;
//import cn.com.voyah.material.domain.entity.OriginMaterialDO;
//import cn.com.voyah.material.domain.gateway.MaterialTagGateway;
//import cn.com.voyah.material.domain.gateway.MaterialTagItemGateway;
//import cn.com.voyah.material.domain.gateway.OriginMaterialGateway;
//import cn.com.voyah.material.dto.GlobalOriginMaterialQuery;
//import cn.com.voyah.material.dto.query.PageQueryCondition;
//import cn.com.voyah.material.es.index.OriginMaterialEs;
//import cn.com.voyah.material.es.mapper.EsOriginMaterialMapper;
//import cn.com.voyah.material.es.service.EsOriginMaterialService;
//import cn.com.voyah.material.es.vo.OriginMaterialEsVO;
//import cn.com.voyah.material.pojo.OriginMaterialPO;
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.json.JSONUtil;
//import com.mybatisflex.core.query.QueryWrapper;
//import lombok.extern.slf4j.Slf4j;
//import org.dromara.easyes.core.biz.EsPageInfo;
//import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
//import org.elasticsearch.action.search.SearchResponse;
//import org.elasticsearch.client.ElasticsearchClient;
//import org.elasticsearch.client.RequestOptions;
//import org.elasticsearch.client.RestHighLevelClient;
//import org.elasticsearch.client.indices.AnalyzeRequest;
//import org.elasticsearch.client.indices.AnalyzeResponse;
//import org.elasticsearch.index.query.Operator;
//import org.elasticsearch.index.query.QueryBuilder;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.elasticsearch.search.SearchHit;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Repository;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
//import static cn.com.voyah.domain.entity.def.MaterialTagItemDef.MATERIAL_TAG_ITEM;
//import static cn.com.voyah.domain.entity.def.OriginMaterialDef.ORIGIN_MATERIAL;
//
///**
// * 简历表
// *
// * <AUTHOR>
// * @date 2023-05-09 21:22:25
// */
//@Repository
//@Slf4j
//public class EsOriginMaterialServiceImpl implements EsOriginMaterialService {
//    @Autowired
//    EsOriginMaterialMapper esOriginMaterialMapper;
//
//    @Autowired
//    OriginMaterialGateway originMaterialGateway;
//
//    @Autowired
//    MaterialTagItemGateway materialTagItemGateway;
//
//    @Autowired
//    MaterialTagGateway materialTagGateway;
//
//    @Autowired
//    MaterialTagService materialTagService;
//
//    @Autowired
//    private RestHighLevelClient restHighLevelClient;
//
//    @Override
//    public EsPageInfo<OriginMaterialPO> search(PageQueryCondition<GlobalOriginMaterialQuery> query) {
//        LambdaEsQueryWrapper<OriginMaterialPO> wrapper = new LambdaEsQueryWrapper<>();
//        QueryBuilder queryBuilder = QueryBuilders.matchQuery("content", "推*").minimumShouldMatch("80%");
//
//        wrapper.mix(queryBuilder);
////        wrapper.multiMatchQuery(dto.getMajor(),BsResume::getMajor,BsResume::getWorkExperience,BsResume::getProjectExperience)
////                .and(i->i.match(BsResume::getDegree,dto.getDegree()));
////        List<OriginMaterialPO> res = new ArrayList<>();
////        wrapper.multiMatchQuery(dto.getExPosition(), BsResume::getMajor, BsResume::getWorkExperience, BsResume::getProjectExperience);
////        if (ObjectUtil.isNotEmpty(dto.getMajor())) {
////            wrapper.or().multiMatchQuery(dto.getMajor(), BsResume::getMajor, BsResume::getWorkExperience, BsResume::getProjectExperience);
////        }
////        wrapper.size(dto.getPageSize());
//
////        SearchResponse response = esResumeMapper.search(wrapper);
////        //意思就是获取response 中的hits 然后遍历筛选放到map中
////        float maxScore = response.getHits().getMaxScore();
////        SearchHit[] hits = response.getHits().getHits();
////
////        for (SearchHit hit : hits) {
////            if (hit.getScore() > maxScore * 0.4) {
////                OriginMaterialPO resume = JSONUtil.toBean(hit.getSourceAsString(), OriginMaterialPO.class);
//////                resume.setScore(String.valueOf(hit.getScore()/maxScore));
////                res.add(resume);
////            }
////        }
//
////        wrapper.multiMatchQuery(dto.getMajor(), BsResume::getMajor, BsResume::getWorkExperience, BsResume::getProjectExperience)
////                .or(t -> {
////                    t.multiMatchQuery(dto.getExPosition(), BsResume::getMajor, BsResume::getWorkExperience, BsResume::getProjectExperience);
////                });
////        EsPageInfo<OriginMaterialPO> esPageInfo = esOriginMaterialMapper.pageQuery(wrapper, query.getPageIndex(), query.getPageSize());
//        return null;
//    }
//
//    @Override
//    public void sync() {
//
//        log.info("执行es-OriginMaterial同步任务开始：" + DateUtil.now());
//        QueryWrapper query = QueryWrapper.create()
//                .from(ORIGIN_MATERIAL.getTableName())
//                .where(ORIGIN_MATERIAL.IS_DELETE.eq(Boolean.FALSE))
//                .and(ORIGIN_MATERIAL.IS_DIR.eq(false))
//                .limit(10);
//        List<OriginMaterialDO> originMaterialDOList = originMaterialGateway.list(query);
//
//        originMaterialDOList.stream().forEach(t -> {
//            List<OriginMaterialEs> bsRequirements = esOriginMaterialMapper.selectList(new LambdaEsQueryWrapper<OriginMaterialEs>()
//                    .eq(OriginMaterialEs::getMaterialId, t.getId()));
//            if(CollectionUtil.isNotEmpty(bsRequirements)){
//                List<String> edIds = bsRequirements.stream().map(OriginMaterialEs::getMaterialId).collect(Collectors.toList());
//                esOriginMaterialMapper.deleteBatchIds(edIds);
//            }
//            MaterialTagDO materialTagDO = materialTagGateway.getByMaterialId(t.getId());
//            QueryWrapper wrapper =QueryWrapper.create()
//                    .from(MATERIAL_TAG_ITEM.getTableName())
//                    .where(MATERIAL_TAG_ITEM.IS_DELETE.eq(Boolean.FALSE))
//                    .and(MATERIAL_TAG_ITEM.MATERIAL_ID.eq(t.getId()));
//            List<MaterialTagItemDO> materialTagItemDOList = materialTagItemGateway.list(wrapper);
//
////            Set<MaterialTagHeading3VO> headingByMaterialId3 = materialTagService.getHeadingByMaterialId3(t.getId(), null);
//            OriginMaterialEs es  =new OriginMaterialEs();
//            BeanUtil.copyProperties(t,es);
//            es.setMaterialId(t.getId().toString());
//            es.setRemark(ObjectUtil.isNotEmpty(materialTagDO)&&ObjectUtil.isNotEmpty(materialTagDO.getRemark())?materialTagDO.getRemark():"");
//            es.setTagIdList(materialTagItemDOList.stream().map(a->a.getTagId().toString()).collect(Collectors.joining(",")));
//            es.setTagValueList(materialTagItemDOList.stream().map(a->a.getTagValue().toString()).collect(Collectors.joining(",")));
//
//
//            esOriginMaterialMapper.insert(es);
//        });
//        log.info("执行es-OriginMaterial同步任务结束：" + DateUtil.now());
//    }
//
//    @Override
//    public OriginMaterialEsVO match(String query) throws IOException {
//        OriginMaterialEsVO res = new OriginMaterialEsVO();
//        List<String>keywork = new ArrayList<>();
//        AnalyzeRequest analyzeRequest = AnalyzeRequest.withGlobalAnalyzer("ik_smart", query);
//
//        AnalyzeResponse analyzeResponse = restHighLevelClient.indices()
//                .analyze(analyzeRequest, RequestOptions.DEFAULT);
//
//        analyzeResponse.getTokens().forEach(analyzeToken -> {
//            System.out.println(analyzeToken.getTerm());
//            keywork.add(analyzeToken.getTerm());
//        });
//        res.setKeywordList(keywork);
//
//
//        LambdaEsQueryWrapper<OriginMaterialEs> wrapper = new LambdaEsQueryWrapper<>();
////        wrapper.like(OriginMaterialEs::getMaterialName,query);
////        wrapper.or().like(OriginMaterialEs::getTagValueList,query);
////        wrapper.or().like(OriginMaterialEs::getRemark,query);
//
////        wrapper.multiMatchQuery(dto.getMajor(),BsResume::getMajor,BsResume::getWorkExperience,BsResume::getProjectExperience)
////                .and(i->i.match(BsResume::getDegree,dto.getDegree()));
////        wrapper.matchPhrase(query, Operator.AND,50 ,OriginMaterialEs::getMaterialName, OriginMaterialEs::getTagValueList, OriginMaterialEs::getRemark);
//        wrapper.multiMatchQuery(query, Operator.OR,80,OriginMaterialEs::getMaterialName, OriginMaterialEs::getTagValueList, OriginMaterialEs::getRemark);
//        //        wrapper.size(dto.getPageSize());
//        List<OriginMaterialEs> materialEsList = esOriginMaterialMapper.selectList(wrapper);
//        res.setMaterialEsList(materialEsList);
//        return res;
//    }
//
//
//}
