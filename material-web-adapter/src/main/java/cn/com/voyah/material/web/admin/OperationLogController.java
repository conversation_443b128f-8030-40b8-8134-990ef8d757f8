package cn.com.voyah.material.web.admin;

import cn.com.voyah.domain.entity.def.OperationLogDef;
import cn.com.voyah.domain.entity.def.TaskInfoDef;
import cn.com.voyah.material.client.OperationLogService;
import cn.com.voyah.material.dto.*;
import cn.com.voyah.material.dto.query.PageQueryCondition;
import cn.com.voyah.material.exception.BizException;
import cn.com.voyah.material.util.PageUtil;
import cn.com.voyah.material.catchlog.CatchAndLog;
import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import cn.com.voyah.material.domain.entity.OperationLogDO;
import cn.com.voyah.material.domain.gateway.OperationLogGateway;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 日志表 控制层。
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@CatchAndLog
@RestController
@Tag(name = "日志表接口(管理平台)")
@RequestMapping("/operation-log")
public class OperationLogController {

    @Autowired
    private OperationLogGateway operationLogGateway;
    @Autowired
    private OperationLogService operationLogService;

    @Getter
    @Value("${operation.log.export-limit-count:10000}")
    private Integer exportLimitCount;

    /**
     * 添加日志表。
     *
     * @return {@code true} 添加成功，{@code false} 添加失败
     */
    @PostMapping("save")
    @Operation(summary = "保存日志表",description = "管理平台-查询未读消息通知信息条数")
    public SingleResponse<OperationLogDO> save(@RequestBody OperationLogDO operationLogDO) {
        return SingleResponse.of(operationLogGateway.save(operationLogDO));
    }

    /**
     * 根据主键删除日志表。
     *
     * @param id 主键
     * @return {@code true} 删除成功，{@code false} 删除失败
     */
    @DeleteMapping("remove/{id}")
    @Operation(summary = "根据主键日志表")
    public SingleResponse<Boolean> remove(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(operationLogGateway.removeById(id));
    }

    /**
     * 根据主键更新日志表。
     *
     * @return {@code true} 更新成功，{@code false} 更新失败
     */
    @PutMapping("update")
    @Operation(summary = "根据主键更新日志表")
    public SingleResponse<Boolean> update(@RequestBody OperationLogDO operationLogDO) {
        return SingleResponse.of(operationLogGateway.updateById(operationLogDO));
    }


    /**
     * 根据日志表主键获取详细信息。
     *
     * @param id 日志表主键
     * @return 日志表详情
     */
    @GetMapping("by/{id}")
    @Operation(summary = "根据主键获取日志表")
    public SingleResponse<OperationLogDO> getInfo(@PathVariable(name = "id") Long id) {
        return SingleResponse.of(operationLogGateway.getById(id));
    }

    /**
     * 分页查询日志表。
     *
     * @param query 分页对象
     * @return 分页对象
     */
    @PostMapping("page")
    @Operation(summary = "分页查询日志表")
    public PageResponse<OperationLogDO> page(@RequestBody PageQueryCondition<OperationLogDO> query) {
        if (StringUtils.isEmpty(query.getOrderBy())) {
            query.setOrderBy(OperationLogDef.OPERATION_LOG.CREATE_TIME.getName());
        }
        return PageUtil.of(operationLogService.getPage(query, TimeQueryInterval.buildFromPage(query)));
    }


    @Operation(summary = "导出")
    @PostMapping("/export")
    public void exportTemplate(@RequestBody(required = false) OperationLogExportQuery operationLogExportQuery, HttpServletResponse response) {
        try {
            if(ObjectUtil.isEmpty(operationLogExportQuery)){
                operationLogExportQuery=new OperationLogExportQuery();
            }
            if(ObjectUtil.isEmpty(operationLogExportQuery.getLimit())){
                operationLogExportQuery.setLimit(exportLimitCount);
            }
            operationLogService.exportLogs(operationLogExportQuery, response);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BizException("系统异常！");
        }
    }

    @Operation(summary = "导出2")
    @GetMapping("/export2")
    public void exportTemplate2(@RequestBody(required = false) OperationLogExportQuery operationLogExportQuery, HttpServletResponse response) {
        try {
            if(ObjectUtil.isEmpty(operationLogExportQuery)){
                operationLogExportQuery=new OperationLogExportQuery();
            }
            if(ObjectUtil.isEmpty(operationLogExportQuery.getLimit())){
                operationLogExportQuery.setLimit(exportLimitCount);
            }
            operationLogService.exportLogs(operationLogExportQuery, response);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BizException("系统异常！");
        }
    }
}