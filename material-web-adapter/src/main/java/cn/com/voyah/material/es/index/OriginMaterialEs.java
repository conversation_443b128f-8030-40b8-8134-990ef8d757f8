//package cn.com.voyah.material.es.index;
//
//import lombok.Data;
//import org.dromara.easyes.annotation.HighLight;
//import org.dromara.easyes.annotation.IndexField;
//import org.dromara.easyes.annotation.IndexId;
//import org.dromara.easyes.annotation.IndexName;
//import org.dromara.easyes.annotation.rely.FieldStrategy;
//import org.dromara.easyes.annotation.rely.FieldType;
//
//import java.io.Serial;
//import java.io.Serializable;
//
///**
// * 素材 实体类。
// *
// * <AUTHOR>
// * @since 2024-11-07
// */
//@Data
//@IndexName("origin_material")
//public class OriginMaterialEs implements Serializable {
//
////    @Serial
////    private static final long serialVersionUID = 1L;
//
//
//
//    /**
//     * 素材ID
//     */
//    @IndexField(fieldType = FieldType.KEYWORD)
//    private String materialId;
//
//
//    private String id;
//
//    /**
//     * 素材名称
//     */
//    @IndexField(strategy = FieldStrategy.NOT_EMPTY,fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
//    @HighLight
//    private String materialName;
//    /**
//     * 素材库id 1：产品素材 2：传播素材
//     */
//    private Long libraryId;
//
//    /**
//     * 层级父级ID
//     */
//    private Long parentId;
//
//    /**
//     * 目录及文件大小 Byte
//     */
//    private Long fileSize;
//
//    /**
//     * 文件类型 如；dir、pdf、img
//     */
//    private String fileType;
//
//    /**
//     * 文件本身的id
//     */
//    private Long fileId;
//
//
//    /**
//     * 素材描述 material_tag  表中的remark字段
//     */
//    @IndexField(strategy = FieldStrategy.NOT_EMPTY,fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
//    @HighLight
//    private String remark;
//
//
//    /**
//     * 素材绑定tag value 集合  字符串拼接
//     */
//    @IndexField(strategy = FieldStrategy.NOT_EMPTY,fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
//    @HighLight
//    private String tagValueList;
//
//
//    /**
//     * 素材绑定tag id 集合  字符串拼接
//     */
//    private String tagIdList;
//}
